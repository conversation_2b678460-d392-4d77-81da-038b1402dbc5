#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成保温音频文件脚本
使用edge-tts生成MP3，然后转换为Opus格式
"""

import asyncio
import os
import subprocess
import sys

# 保温音频配置
WARMUP_AUDIOS = {
    "ok": "好的",
    "hmm": "嗯",
    "listening": "我在听",
    "processing": "正在处理"
}

# 输出目录
OUTPUT_DIR = "audio/warmup"

# TTS语音
VOICE = "zh-CN-XiaoxiaoNeural"

async def generate_mp3(text, output_file):
    """使用edge-tts生成MP3文件"""
    print(f"📝 生成音频: {text} -> {output_file}")
    
    try:
        # 使用edge-tts命令行工具
        cmd = [
            sys.executable, "-m", "edge_tts",
            "--voice", VOICE,
            "--text", text,
            "--write-media", output_file
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            print(f"✅ MP3生成成功: {output_file}")
            return True
        else:
            print(f"❌ MP3生成失败: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 生成MP3时出错: {e}")
        return False

def convert_to_opus(mp3_file, opus_file):
    """将MP3转换为Opus格式"""
    print(f"🔄 转换为Opus: {mp3_file} -> {opus_file}")
    
    try:
        cmd = [
            "ffmpeg",
            "-i", mp3_file,
            "-c:a", "libopus",
            "-b:a", "16k",
            "-ar", "16000",
            "-ac", "1",
            "-y",  # 覆盖已存在的文件
            opus_file
        ]
        
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ Opus转换成功: {opus_file}")
            # 删除临时MP3文件
            if os.path.exists(mp3_file):
                os.remove(mp3_file)
                print(f"🗑️ 删除临时文件: {mp3_file}")
            return True
        else:
            print(f"❌ Opus转换失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 转换Opus时出错: {e}")
        return False

async def generate_all_audios():
    """生成所有保温音频"""
    print("🎙️ 开始生成保温音频文件...")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"🎤 使用语音: {VOICE}")
    print("-" * 60)
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    success_count = 0
    total_count = len(WARMUP_AUDIOS)
    
    for audio_type, text in WARMUP_AUDIOS.items():
        print(f"\n[{success_count + 1}/{total_count}] 处理: {audio_type} - \"{text}\"")
        
        mp3_file = os.path.join(OUTPUT_DIR, f"{audio_type}.mp3")
        opus_file = os.path.join(OUTPUT_DIR, f"{audio_type}.opus")
        
        # 生成MP3
        if await generate_mp3(text, mp3_file):
            # 转换为Opus
            if convert_to_opus(mp3_file, opus_file):
                success_count += 1
                # 检查文件大小
                file_size = os.path.getsize(opus_file)
                print(f"📊 文件大小: {file_size} bytes ({file_size / 1024:.2f} KB)")
            else:
                print(f"⚠️ {audio_type} 转换失败")
        else:
            print(f"⚠️ {audio_type} 生成失败")
    
    print("\n" + "=" * 60)
    print(f"🎉 完成! 成功生成 {success_count}/{total_count} 个音频文件")
    print("=" * 60)
    
    # 列出生成的文件
    print("\n📂 生成的文件列表:")
    for audio_type in WARMUP_AUDIOS.keys():
        opus_file = os.path.join(OUTPUT_DIR, f"{audio_type}.opus")
        if os.path.exists(opus_file):
            file_size = os.path.getsize(opus_file)
            print(f"  ✅ {opus_file} ({file_size} bytes)")
        else:
            print(f"  ❌ {opus_file} (不存在)")
    
    return success_count == total_count

def main():
    """主函数"""
    print("=" * 60)
    print("🎙️ 保温音频生成工具")
    print("=" * 60)
    
    # 检查ffmpeg
    try:
        result = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        if result.returncode != 0:
            print("❌ 错误: ffmpeg未安装或不在PATH中")
            print("请安装ffmpeg: https://ffmpeg.org/download.html")
            return False
    except FileNotFoundError:
        print("❌ 错误: ffmpeg未安装或不在PATH中")
        print("请安装ffmpeg: https://ffmpeg.org/download.html")
        return False
    
    # 运行异步任务
    success = asyncio.run(generate_all_audios())
    
    if success:
        print("\n✅ 所有音频文件生成成功!")
        print("📝 请启动应用查看日志确认音频加载")
        return True
    else:
        print("\n⚠️ 部分音频文件生成失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

