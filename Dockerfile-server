# Use Eclipse Temurin for both build and runtime stages
FROM docker.m.daocloud.io/eclipse-temurin:21-jdk AS builder

# 添加构建参数，默认使用标准模型
ARG VOSK_MODEL_SIZE=standard

# 安装必要的构建工具
RUN apt-get update && apt-get install -y --no-install-recommends \
    maven \
    wget \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# 复制源代码
COPY ./src ./src
COPY ./db ./db
COPY pom.xml ./
# 构建应用
RUN mvn -Dmaven.repo.local=/root/.m2/repository package -DskipTests

# 正确提取项目版本号 - 使用更精确的grep模式匹配项目版本而非父项目版本
RUN APP_VERSION=$(grep -A1 "<artifactId>iot-manager</artifactId>" pom.xml | grep "<version>" | sed -e 's/<version>//' -e 's/<\/version>//' -e 's/[[:space:]]//g') && \
    echo "APP_VERSION=${APP_VERSION}" > /build/app_version.env && \
    cat /build/app_version.env

# 确保模型目录存在
RUN mkdir -p /build/models

# 复制模型文件
COPY ./models/silero_vad.onnx /build/models/silero_vad.onnx

# 下载并准备Vosk模型
RUN mkdir -p /vosk_cache && \
    if [ "$VOSK_MODEL_SIZE" = "small" ]; then \
        if [ ! -d "/vosk_cache/vosk-model-small-cn-0.22" ]; then \
            echo "Downloading small Vosk model..." && \
            cd /vosk_cache && \
            wget https://alphacephei.com/vosk/models/vosk-model-small-cn-0.22.zip && \
            unzip vosk-model-small-cn-0.22.zip && \
            rm vosk-model-small-cn-0.22.zip; \
        else \
            echo "Using cached small Vosk model"; \
        fi && \
        cp -r /vosk_cache/vosk-model-small-cn-0.22 /build/models/vosk-model; \
    else \
        if [ ! -d "/vosk_cache/vosk-model-cn-0.22" ]; then \
            echo "Downloading standard Vosk model..." && \
            cd /vosk_cache && \
            wget https://alphacephei.com/vosk/models/vosk-model-cn-0.22.zip && \
            unzip vosk-model-cn-0.22.zip && \
            rm vosk-model-cn-0.22.zip; \
        else \
            echo "Using cached standard Vosk model"; \
        fi && \
        cp -r /vosk_cache/vosk-model-cn-0.22 /build/models/vosk-model; \
    fi

# 使用更小的JRE镜像作为运行时环境
FROM docker.m.daocloud.io/eclipse-temurin:21-jre

# 安装必要的运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制构建阶段的JAR文件和版本信息
COPY --from=builder /build/target/iot-manager-*.jar /app/
COPY --from=builder /build/app_version.env /app/
# 复制模型文件夹
COPY --from=builder /build/models /app/models

# 设置启动脚本 - 使用 . 代替 source，并确保使用 bash
RUN echo '#!/bin/bash\n\
if [ -f /app/app_version.env ]; then\n\
  . /app/app_version.env\n\
fi\n\
echo "Starting application version: ${APP_VERSION}"\n\
java -Xms512m -Xmx1024m -jar /app/iot-manager-${APP_VERSION}.jar\n\
' > /app/start.sh && chmod +x /app/start.sh

# 使用 bash 执行启动脚本
CMD ["/bin/bash", "/app/start.sh"]