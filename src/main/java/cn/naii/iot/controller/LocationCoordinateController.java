package cn.naii.iot.controller;

import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.entity.LocationCoordinate;
import cn.naii.iot.service.LocationCoordinateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地理位置坐标控制器
 */
@RestController
@RequestMapping("/api/location")
public class LocationCoordinateController {

    @Autowired
    private LocationCoordinateService locationCoordinateService;

    /**
     * 查询所有记录
     */
    @GetMapping
    public AjaxResult findAll() {
        List<LocationCoordinate> data = locationCoordinateService.findAll();
        return AjaxResult.success(data);
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/{id}")
    public AjaxResult findById(@PathVariable Long id) {
        LocationCoordinate data = locationCoordinateService.findById(id);
        return AjaxResult.success(data);
    }

    /**
     * 根据条件查询
     */
    @PostMapping("/search")
    public AjaxResult search(@RequestBody LocationCoordinate locationCoordinate) {
        List<LocationCoordinate> data = locationCoordinateService.findByCondition(locationCoordinate);
        return AjaxResult.success(data);
    }

    /**
     * 插入记录
     */
    @PostMapping
    public AjaxResult insert(@RequestBody LocationCoordinate locationCoordinate) {
        boolean result = locationCoordinateService.insert(locationCoordinate);
        if (result) {
            return AjaxResult.success("插入成功");
        } else {
            return AjaxResult.error("插入失败");
        }
    }

    /**
     * 更新记录
     */
    @PutMapping
    public AjaxResult update(@RequestBody LocationCoordinate locationCoordinate) {
        boolean result = locationCoordinateService.update(locationCoordinate);
        if (result) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 删除记录
     */
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        boolean result = locationCoordinateService.deleteById(id);
        if (result) {
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败");
        }
    }
}
