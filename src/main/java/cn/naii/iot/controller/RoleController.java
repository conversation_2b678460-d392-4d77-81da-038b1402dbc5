package cn.naii.iot.controller;

import cn.naii.iot.resp.SysRoleResp;
import com.github.pagehelper.PageInfo;
import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.dialogue.tts.factory.TtsServiceFactory;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.service.SysConfigService;
import cn.naii.iot.service.SysRoleService;
import cn.naii.iot.utils.CmsUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理
 * 
 * <AUTHOR>
 * 
 */

@RestController
@RequestMapping("/api/role")
@Tag(name = "角色管理", description = "角色相关操作")
public class RoleController extends BaseController {

    @Resource
    private SysRoleService roleService;

    @Resource
    private TtsServiceFactory ttsService;

    @Resource
    private SysConfigService configService;

    /**
     * 角色查询
     * 
     * @param role
     * @return roleList
     */
    @GetMapping("/query")
    @ResponseBody
    @Operation(summary = "根据条件查询角色信息", description = "返回角色信息列表")
    public AjaxResult query(SysRole role, HttpServletRequest request) {
        try {
            PageFilter pageFilter = initPageFilter(request);
            role.setUserId(CmsUtils.getUserId());
            if(CmsUtils.getUserId()!= null && CmsUtils.getUserId() != 1){
                role.setUserId(CmsUtils.getUserId());
            }else {
                role.setUserId(null);
            }
            List<SysRoleResp> roleList = roleService.queryList(role, pageFilter);
            AjaxResult result = AjaxResult.success();
            result.put("data", new PageInfo<>(roleList));
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    /**
     * 角色信息更新
     * 
     * @param role
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    @Operation(summary = "更新角色信息", description = "返回更新结果")
    public AjaxResult update(SysRole role) {
        try {
            role.setUserId(CmsUtils.getUserId());
            roleService.update(role);
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    /**
     * 添加角色
     * 
     * @param role
     */
    @PostMapping("/add")
    @ResponseBody
    @Operation(summary = "添加角色信息", description = "返回添加结果")
    public AjaxResult add(SysRole role) {
        try {
            role.setUserId(CmsUtils.getUserId());
            roleService.add(role);
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    @GetMapping("/testVoice")
    @ResponseBody
    @Operation(summary = "测试语音合成", description = "返回语音合成结果")
    public AjaxResult testAudio(
        @Parameter(description = "消息文本") String message, 
        @Parameter(description = "语音合成提供方") String provider, 
        @Parameter(description = "TTS ID") Integer ttsId, 
        @Parameter(description = "音色名称") String voiceName) {
        SysConfig config = null;
        try {
            if (!provider.equals("edge")) {
                config = configService.selectConfigById(ttsId);
            }
            String audioFilePath = ttsService.getTtsService(config, voiceName).textToSpeech(message);
            AjaxResult result = AjaxResult.success();
            result.put("data", audioFilePath);
            return result;
        } catch (IndexOutOfBoundsException e) {
            return AjaxResult.error("请先到语音合成配置页面配置对应Key");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }
}