package cn.naii.iot.controller;

import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.entity.SysVoicePrint;
import cn.naii.iot.request.*;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.service.SysTimbreService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.service.SysVoicePrintService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/app/user")
@Tag(name = "小程序端应用接口", description = "用户和设备相关操作")
public class AppUserController extends BaseController {

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysTimbreService sysTimbreService;

    @Resource
    private SysVoicePrintService sysVoicePrintService;


    /**
     * 获取用户设备信息列表
     */
    @PostMapping("/deviceInfo")
    @Operation(summary = "获取用户设备信息", description = "根据用户信息获取设备列表")
    public AjaxResult device(@RequestBody @Valid DeviceRequest request) {
        return AjaxResult.success(deviceService.selectDeviceList(request));
    }

    /**
     * 删除设备信息
     */
    @PostMapping("/delDevice")
    @Operation(summary = "删除设备", description = "根据openId，userId和设备ID删除设备")
    public AjaxResult deleteDevice(@RequestBody @Valid DelDeviceRequest request) {
        int i = deviceService.delDevice(request);
        if (i > 0) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }


    /**
     * 绑定设备信息
     */
    @PostMapping("/bindDevice")
    @Operation(summary = "绑定设备", description = "绑定设备到用户")
    public AjaxResult bindDevice(@RequestBody @Valid BindRequest request) {
        int i = deviceService.bindDevice(request);
        if (i > 0) {
            return AjaxResult.success("绑定成功");
        }
        return AjaxResult.error("绑定失败");

    }

    /**
     * 用户设备音量修改
     */
    @PostMapping("/upVolume")
    @Operation(summary = "修改设备音量", description = "修改指定设备的音量")
    public AjaxResult updateVolume(@RequestBody @Valid VolumeRequest request) {
        int i = deviceService.updateVolume(request);
        if (i > 0) {
            return AjaxResult.success("音量调节成功");
        }
        return AjaxResult.error("音量调节失败");

    }

    /**
     * 更新用户信息
     */
    @PostMapping("/upUserInfo")
    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    public AjaxResult updateUserInfo(@RequestBody @Valid UpUserRequest request) {
        int i = sysUserService.updateUser(request);
        if (i > 0) {
            return AjaxResult.success("用户信息更新成功");
        }
        return AjaxResult.error("用户信息更新失败");
    }

    @PostMapping("/userInfo")
    @Operation(summary = "获取用户信息", description = "根据uid获取用户详细信息")
    public AjaxResult getUserInfo(@RequestBody @Valid UserRequest request) {
        return AjaxResult.success(sysUserService.getUserInfo(request));
    }


    @PostMapping("/upTimbre")
    @Operation(summary = "更新设备音色", description = "更新设备音色")
    public AjaxResult updateTimbre(@RequestBody @Valid UpTimbreRequest request) {

        int i = deviceService.updateTimbre(request);
        if (i > 0) {
            return AjaxResult.success("音色变更成功");
        }
        return AjaxResult.success("音色变更失败");
    }

    @PostMapping("/timbreList")
    @Operation(summary = "获取设备音色列表", description = "获取设备音色列表")
    public AjaxResult timbreList(@RequestBody  GetTimbreRequest request) {
        //TODO 后续需要实现根据用户的ID查询当前创建的音色列表以及公共音色列表
        return AjaxResult.success(sysTimbreService.getTimbreList());
    }

    @PostMapping("/voicePrintList")
    @Operation(summary = "获取用户声纹列表", description = "获取用户声纹列表")
    public AjaxResult voicePrintList(@RequestBody  VoicePrintRequest request, HttpServletRequest httpServletRequest) {
        try {
            PageFilter pageFilter = initPageFilter(httpServletRequest);
            List<SysVoicePrint> list = sysVoicePrintService.list(request, pageFilter);
            if (list != null && !list.isEmpty()) {
                return AjaxResult.success(new PageInfo<>(list));
            }
            else{
                return AjaxResult.success(new PageInfo<>(Collections.emptyList()));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    @PostMapping("/addVoicePrint")
    @Operation(summary = "新增用户声纹", description = "新增用户声纹")
    public AjaxResult addVoicePrint(@RequestBody  VoicePrintRequest request) {
        return AjaxResult.success(sysVoicePrintService.add(request));
    }

    @PostMapping("/deleteVoicePrint")
    @Operation(summary = "删除用户声纹", description = "删除用户声纹")
    public AjaxResult deleteVoicePrint(@RequestBody  VoicePrintRequest request) {
        return AjaxResult.success(sysVoicePrintService.delete(request));
    }
}
