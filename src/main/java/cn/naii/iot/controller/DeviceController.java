package cn.naii.iot.controller;

import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysMessage;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.manage.GetDeviceRequest;
import cn.naii.iot.request.manage.UpdateDevice;
import cn.naii.iot.resp.SysDeviceResp;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.service.SysMessageService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.utils.CmsUtils;
import cn.naii.iot.utils.JsonUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备管理
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/api/device")
@Tag(name = "设备管理", description = "设备相关操作")
public class DeviceController extends BaseController {

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private Environment environment;

    @Resource
    private CmsUtils cmsUtils;


    @Resource
    private SysMessageService sysMessageService;


    @Resource
    private SysUserService sysUserService;

    @Value("${role.default.id}")
    private Integer defaultRoleId;

    /**
     * 设备查询
     *
     * @param device
     * @return deviceList
     */
    @GetMapping("/query")
    @ResponseBody
    @Operation(summary = "根据条件查询设备", description = "返回设备信息列表")
    public AjaxResult query(SysDevice device, HttpServletRequest request) {
        try {
            PageFilter pageFilter = initPageFilter(request);
            // 查询所有已绑定用户的设备
            device.setUserId(-1);

            List<SysDevice> deviceList = deviceService.query(device, pageFilter);
            AjaxResult result = AjaxResult.success();
            if (deviceList == null){
                logger.info("设备列表为空");
                result.put("data", new PageInfo<>(Collections.emptyList()));
            }else {
                List<SysDeviceResp> sysDeviceResps = deviceList.stream().map(d -> {
                    SysMessage sysMessage = sysMessageService.queryLastMessage(d.getDeviceId());
                    SysDeviceResp sysDeviceResp = new SysDeviceResp();
                    BeanUtils.copyProperties(d, sysDeviceResp);
                    if (sysMessage != null && sysMessage.getCreateTime() != null) {
                        sysDeviceResp.setLastActiveTime(DateUtils.format(sysMessage.getCreateTime()));
                    } else {
                        sysDeviceResp.setLastActiveTime(DateUtils.format(new Date()));
                    }
                    SysUser sysUser = sysUserService.selectUserByOpenId(d.getOpenId());
                    if (sysUser != null) {
                        sysDeviceResp.setUserName(sysUser.getUsername());
                        sysDeviceResp.setName(sysUser.getName());
                    }
                    return sysDeviceResp;
                }).toList();
                result.put("data", new PageInfo<>(sysDeviceResps));
            }
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    /**
     * 添加设备
     *
     * @param deviceId 设备编号(MAC地址)
     */
    @PostMapping("/add")
    @ResponseBody
    @Operation(summary = "添加设备", description = "返回添加结果")
    public AjaxResult add(@Parameter(description = "设备编号(MAC地址)") String deviceId) {
        try {
            // 验证设备ID格式
            if (deviceId == null || deviceId.trim().isEmpty()) {
                return AjaxResult.error("设备编号不能为空");
            }

            deviceId = deviceId.trim();

            // 验证MAC地址格式
            if (!cmsUtils.isMacAddressValid(deviceId)) {
                return AjaxResult.error("设备编号格式不正确");
            }

            // 查询设备是否已存在
            SysDevice queryDevice = new SysDevice();
            queryDevice.setDeviceId(deviceId);
            List<SysDevice> existingDevices = deviceService.queryExactly(queryDevice, new PageFilter());

            if (!ObjectUtils.isEmpty(existingDevices)) {
                // 设备已存在
                return AjaxResult.error("设备已添加");
            }

            // 设备不存在，添加新设备（不绑定用户）
            SysDevice newDevice = new SysDevice();
            newDevice.setDeviceId(deviceId);
            newDevice.setDeviceName("小智"); // 默认设备名称
            newDevice.setRoleId(defaultRoleId); // 使用默认角色ID
            newDevice.setState(SysDevice.DEVICE_STATE_OFFLINE); // 初始状态为离线
            // 不设置userId，保持为null

            // 插入设备
            int row = deviceService.add(newDevice);
            if (row > 0) {
                logger.info("✅ 设备添加成功 - DeviceId: {}", deviceId);
                return AjaxResult.success("设备添加成功");
            } else {
                return AjaxResult.error("设备添加失败");
            }
        } catch (DuplicateKeyException e) {
            // 并发情况下可能已被其他请求插入
            logger.warn("设备已存在（并发插入） - DeviceId: {}", deviceId);
            return AjaxResult.error("设备已添加");
        } catch (Exception e) {
            logger.error("设备添加失败 - DeviceId: {}", deviceId, e);
            if (e.getMessage() != null && e.getMessage().contains("没有配置角色")) {
                return AjaxResult.error(e.getMessage());
            }
            return AjaxResult.error("设备添加失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加设备
     * 用户上传设备mac地址的列表，批量往数据库中添加数据
     *
     * @param deviceIds
     * @return
     */
    @PostMapping("/addBatch")
    @ResponseBody
    @Operation(summary = "批量添加设备", description = "返回添加结果")
    public AjaxResult addBatch(@RequestBody List<String> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return AjaxResult.error("设备ID不能为空");
        }
        // 过滤空值、去重
        LinkedHashSet<String> ids = deviceIds.stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toCollection(LinkedHashSet::new));
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        for (String id : ids) {
            try {
                SysDevice d = new SysDevice();
                d.setUserId(CmsUtils.getUserId());
                d.setDeviceId(id);
                d.setRoleId(defaultRoleId); // 替代 1 的常量
                int row = deviceService.add(d);
                if (row > 0) {
                    successIds.add(id);
                } else {
                    failedIds.add(id);
                }
            } catch (DuplicateKeyException e) {
                // 唯一键冲突等
                failedIds.add(id);
                logger.warn("设备添加重复 deviceId={}", id, e);
            } catch (Exception e) {
                failedIds.add(id);
                logger.error("设备添加异常 deviceId={}", id, e);
            }
        }

        Map<String, Object> data = new HashMap<>();
        data.put("total", ids.size());
        data.put("successCount", successIds.size());
        data.put("failedCount", failedIds.size());
        data.put("successIds", successIds);
        data.put("failedIds", failedIds);

        return failedIds.isEmpty()
                ? AjaxResult.success(data)
                : AjaxResult.error("部分设备添加失败", data);

    }


    /**
     * 设备信息更新
     *
     * @param request
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    @Operation(summary = "更新设备信息", description = "返回更新结果")
    public AjaxResult update(UpdateDevice request) {
        try {
            SysDevice device = new SysDevice();
            if (CmsUtils.getUserId() != null && CmsUtils.getUserId() != 1) {
                device.setUserId(CmsUtils.getUserId());
            }
            BeanUtils.copyProperties(request, device);
            deviceService.update(device);
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error();
        }
    }

    /**
     * 删除设备
     *
     * @param device
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @Operation(summary = "删除设备", description = "返回删除结果")
    public AjaxResult delete(SysDevice device) {
        try {
            device.setUserId(CmsUtils.getUserId());
            // 删除设备
            int rows = deviceService.delete(device);

            if (rows > 0) {
                // 如果设备有会话，清除会话
                String deviceId = device.getDeviceId();
                ChatSession session = sessionManager.getSessionByDeviceId(deviceId);
                if (session != null) {
                    sessionManager.closeSession(session);
                }
                return AjaxResult.success("删除成功");
            } else {
                return AjaxResult.error("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除设备时发生错误", e);
            return AjaxResult.error("删除设备时发生错误");
        }
    }

    @PostMapping("/ota")
    @ResponseBody
    @Operation(summary = "处理OTA请求", description = "返回OTA结果")
    public ResponseEntity<byte[]> ota(
            @Parameter(description = "设备ID") @RequestHeader("Device-Id") String deviceIdAuth,
            @RequestBody String requestBody,
            HttpServletRequest request) {
        try {
            logger.info("收到OTA请求");
            // 读取请求体内容
            SysDevice device = new SysDevice();

            // 解析JSON请求体
            try {
                Map<String, Object> jsonData = JsonUtil.OBJECT_MAPPER.readValue(requestBody, new TypeReference<>() {
                });

                // 获取设备ID (MAC地址)
                if (deviceIdAuth == null) {
                    if (jsonData.containsKey("mac_address")) {
                        deviceIdAuth = (String) jsonData.get("mac_address");
                    } else if (jsonData.containsKey("mac")) {
                        deviceIdAuth = (String) jsonData.get("mac");
                    }
                }

                // 提取芯片型号
                if (jsonData.containsKey("chip_model_name")) {
                    device.setChipModelName((String) jsonData.get("chip_model_name"));
                }

                // 提取应用版本
                if (jsonData.containsKey("application") && jsonData.get("application") instanceof Map) {
                    Map<String, Object> application = (Map<String, Object>) jsonData.get("application");
                    if (application.containsKey("version")) {
                        device.setVersion((String) application.get("version"));
                    }
                }

                // 提取WiFi名称和设备类型
                if (jsonData.containsKey("board") && jsonData.get("board") instanceof Map) {
                    Map<String, Object> board = (Map<String, Object>) jsonData.get("board");
                    if (board.containsKey("ssid")) {
                        device.setWifiName((String) board.get("ssid"));
                    }
                    if (board.containsKey("type")) {
                        device.setType((String) board.get("type"));
                    }
                }
            } catch (Exception e) {
                logger.debug("JSON解析失败: {}", e.getMessage());
            }

            if (deviceIdAuth == null || !cmsUtils.isMacAddressValid(deviceIdAuth)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "设备ID不正确");
                byte[] responseBytes = JsonUtil.OBJECT_MAPPER.writeValueAsBytes(errorResponse);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setContentLength(responseBytes.length);
                return new ResponseEntity<>(responseBytes, headers, HttpStatus.BAD_REQUEST);
            }

            final String deviceId = deviceIdAuth;
            device.setDeviceId(deviceId);
            device.setLastLogin(new Date().toString());

            // 设置设备IP地址
            device.setIp(CmsUtils.getClientIp(request));
            // 根据设备的IP地址获取地理位置信息
            var ipInfo = CmsUtils.getIPInfoByAddress(device.getIp());
            if (ipInfo != null && ipInfo.getLocation() != null && !ipInfo.getLocation().isEmpty()) {
                device.setLocation(ipInfo.getLocation());
            }

            // 查询设备是否已存在
            List<SysDevice> queryDevice = deviceService.queryExactly(device, new PageFilter());
            Map<String, Object> responseData = new HashMap<>();
            Map<String, Object> firmwareData = new HashMap<>();
            Map<String, Object> serverTimeData = new HashMap<>();

            // 设置服务器时间
            long timestamp = System.currentTimeMillis();
            serverTimeData.put("timestamp", timestamp);
            serverTimeData.put("timezone_offset", 480); // 东八区

            // 设置固件信息
            firmwareData.put("url", cmsUtils.getOtaAddress());
            firmwareData.put("version", "1.0.0");

            // 设置WebSocket连接信息
            String websocketToken = "";//deviceService.generateToken(deviceId);
            Map<String, Object> websocketData = new HashMap<>();
            websocketData.put("url", cmsUtils.getWebsocketAddress());
            websocketData.put("token", websocketToken);
            logger.info("生成WebSocket连接信息: {}", websocketData);
            responseData.put("websocket", websocketData);

            // 检查设备是否已存在
            if (ObjectUtils.isEmpty(queryDevice)) {
                // 设备不存在，直接插入新设备记录
                try {
                    SysDevice newDevice = new SysDevice();
                    newDevice.setDeviceId(deviceId);
                    newDevice.setDeviceName(device.getType() != null && !device.getType().isEmpty()
                            ? device.getType() : "小智"); // 使用设备类型或默认名称
                    newDevice.setType(device.getType());
                    newDevice.setChipModelName(device.getChipModelName());
                    newDevice.setVersion(device.getVersion());
                    newDevice.setWifiName(device.getWifiName());
                    newDevice.setIp(device.getIp());
                    newDevice.setLocation(device.getLocation());
                    newDevice.setState(SysDevice.DEVICE_STATE_ONLINE);
                    newDevice.setRoleId(defaultRoleId); // 使用默认角色ID
                    newDevice.setLastLogin(new Date().toString());

                    // 插入设备
                    int row = deviceService.add(newDevice);
                    if (row > 0) {
                        logger.info("✅ 新设备自动添加成功 - DeviceId: {}, DeviceName: {}",
                                deviceId, newDevice.getDeviceName());
                    } else {
                        logger.warn("⚠️ 新设备添加失败 - DeviceId: {}", deviceId);
                    }
                } catch (DuplicateKeyException e) {
                    // 并发情况下可能已被其他请求插入，忽略此异常
                    logger.warn("设备已存在（并发插入） - DeviceId: {}", deviceId);
                } catch (Exception e) {
                    logger.error("❌ 新设备添加失败 - DeviceId: {}", deviceId, e);
                    // 即使插入失败，也继续返回WebSocket连接信息，允许设备连接
                }
            } else {
                // 设备已存在，更新设备状态和信息
                SysDevice boundDevice = queryDevice.get(0);
                // 保留原设备名称，更新其他信息
                device.setDeviceName(boundDevice.getDeviceName());
                device.setState(SysDevice.DEVICE_STATE_ONLINE);

                // 更新设备信息
                deviceService.update(device);
                logger.info("📊 设备信息已更新 - DeviceId: {}", deviceId);
            }
            // 组装响应数据
            responseData.put("firmware", firmwareData);
            responseData.put("serverTime", serverTimeData);
            logger.info("responseData 设备信息: {}", responseData);

            // 手动将响应数据转换为字节数组，以便设置确切的Content-Length
            byte[] responseBytes = JsonUtil.OBJECT_MAPPER.writeValueAsBytes(responseData);

            // 使用ResponseEntity明确设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setContentLength(responseBytes.length); // 明确设置Content-Length

            return new ResponseEntity<>(responseBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("处理OTA请求失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "处理请求失败: " + e.getMessage());

            try {
                byte[] responseBytes = JsonUtil.OBJECT_MAPPER.writeValueAsBytes(errorResponse);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setContentLength(responseBytes.length);
                return new ResponseEntity<>(responseBytes, headers, HttpStatus.INTERNAL_SERVER_ERROR);
            } catch (Exception ex) {
                logger.error("生成错误响应失败", ex);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
    }


    @PostMapping("/ota/activate")
    @ResponseBody
    @Operation(summary = "查询OTA激活状态", description = "返回OTA激活状态")
    public ResponseEntity<String> otaActivate(
            @Parameter(name = "Device-Id", description = "设备唯一标识", required = true, in = ParameterIn.HEADER)
            @RequestHeader("Device-Id") String deviceId) {
        try {
            if (!cmsUtils.isMacAddressValid(deviceId)) {
                return ResponseEntity.status(202).build();
            }
            // 解析请求体
            SysDevice sysDevice = deviceService.selectDeviceById(deviceId);
            if (sysDevice == null) {
                return ResponseEntity.status(202).build();
            }
            logger.info("OTA激活结果查询成功, deviceId: {} 激活时间: {}", deviceId, sysDevice.getCreateTime());
        } catch (Exception e) {
            logger.error("OTA激活失败", e);
            return ResponseEntity.status(202).build();
        }
        return ResponseEntity.ok("success");
    }


    @PostMapping("/userDevice")
    @Operation(summary = "查询设备信息", description = "根据用户id查询设备信息")
    public AjaxResult getDeviceInfo(@RequestBody GetDeviceRequest request) {
        return AjaxResult.success(deviceService.getDeviceList(request.getOpenId()));
    }

}