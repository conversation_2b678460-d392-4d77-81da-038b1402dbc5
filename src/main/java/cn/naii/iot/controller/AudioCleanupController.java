package cn.naii.iot.controller;

import cn.naii.iot.dialogue.service.AudioCleanupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 音频清理管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/audio/cleanup")
@Tag(name = "音频清理管理", description = "音频文件清理和缓存管理接口")
public class AudioCleanupController {

    @Autowired
    private AudioCleanupService audioCleanupService;

    /**
     * 获取清理服务统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取统计信息", description = "获取音频清理服务的统计信息")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = audioCleanupService.getStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 立即清理指定会话的文件
     */
    @PostMapping("/session/{sessionId}")
    @Operation(summary = "清理会话文件", description = "立即清理指定会话的音频文件")
    public ResponseEntity<Map<String, Object>> cleanupSession(@PathVariable String sessionId) {
        try {
            audioCleanupService.cleanupSessionNow(sessionId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "会话文件清理完成");
            result.put("sessionId", sessionId);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理会话文件失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 触发定时清理任务
     */
    @PostMapping("/trigger")
    @Operation(summary = "触发清理任务", description = "手动触发定时清理任务")
    public ResponseEntity<Map<String, Object>> triggerCleanup() {
        try {
            audioCleanupService.scheduledCleanup();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "清理任务已执行");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("触发清理任务失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}

