package cn.naii.iot.controller;


import cn.naii.iot.entity.VoicePrint;
import cn.naii.iot.service.VoicePrintService;
import cn.naii.iot.utils.CmsUtils;
import cn.naii.iot.common.web.AjaxResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/voice")
@Tag(name = "声纹管理", description = "声纹管理相关接口")
public class VoicePrintController {
    @Autowired
    private VoicePrintService voicePrintService;

    @PostMapping
    @Operation(summary = "创建用户声纹信息", description = "新增一条用户声纹记录")
    public AjaxResult insert(@RequestBody VoicePrint voicePrint) {
        voicePrint.setUserId(CmsUtils.getUserId());
        int result = voicePrintService.insert(voicePrint);
        return AjaxResult.success("操作成功", result);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户声纹信息", description = "根据ID删除用户声纹记录")
    public AjaxResult deleteById(@Parameter(description = "用户声纹ID") @PathVariable Long id) {
        int result = voicePrintService.deleteById(id);
        return AjaxResult.success("操作成功", result);
    }

    @PutMapping
    @Operation(summary = "更新用户声纹信息", description = "根据ID更新用户声纹记录")
    public AjaxResult updateById(@RequestBody VoicePrint voicePrint) {
        int result = voicePrintService.updateById(voicePrint);
        return AjaxResult.success("操作成功", result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户声纹详情", description = "根据ID查询用户声纹详细信息")
    public AjaxResult selectById(@Parameter(description = "用户声纹ID") @PathVariable Long id) {
        VoicePrint data = voicePrintService.selectById(id);
        return AjaxResult.success(data);
    }

    @GetMapping
    @Operation(summary = "获取所有用户声纹列表", description = "查询所有用户声纹信息")
    public AjaxResult selectAll() {
        List<VoicePrint> data = voicePrintService.selectAll();
        return AjaxResult.success(data);
    }

    @PostMapping("/search")
    @Operation(summary = "条件查询用户声纹信息", description = "根据条件查询用户声纹列表")
    public AjaxResult selectByCondition(@RequestBody VoicePrint voicePrint) {
        List<VoicePrint> data = voicePrintService.selectByCondition(voicePrint);
        return AjaxResult.success(data);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询用户声纹信息", description = "分页获取用户声纹列表")
    public AjaxResult selectPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int limit,
            @Parameter(description = "查询条件") VoicePrint voicePrint) {
        List<VoicePrint> data = voicePrintService.selectPage(page, limit, voicePrint);
        return AjaxResult.success(data);
    }

    @GetMapping("/count")
    @Operation(summary = "统计用户声纹数量", description = "根据条件统计用户声纹记录数量")
    public AjaxResult countByCondition(VoicePrint voicePrint) {
        int count = voicePrintService.countByCondition(voicePrint);
        return AjaxResult.success("统计成功", count);
    }
}
