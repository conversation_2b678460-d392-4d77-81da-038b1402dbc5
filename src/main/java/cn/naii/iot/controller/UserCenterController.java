package cn.naii.iot.controller;

import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.request.manage.TotalRequest;
import cn.naii.iot.service.SysUserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/center")
public class UserCenterController {


    @Resource
    private SysUserService sysUserService;

    @PostMapping("/getTotalInfo")
    public AjaxResult getTotalInfo(@RequestBody TotalRequest request) {
        return AjaxResult.success(sysUserService.getTotalInfo(request));
    }

}
