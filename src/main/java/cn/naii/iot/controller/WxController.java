package cn.naii.iot.controller;

import cn.naii.iot.common.interceptor.UnLogin;
import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.dto.WxRequestDTO;
import cn.naii.iot.service.WxLoginService;
import cn.xfyun.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * wx 小程序相关接口
 */
@RestController
@RequestMapping("/api/wx")
@Tag(name = "微信小程序管理", description = "微信小程序相关操作")
public class WxController {
    @Resource
    private WxLoginService wxLoginService;

    @UnLogin
    @PostMapping("/authorize")
    @Operation(summary = "微信授权登录", description = "登录成功返回用户信息")
    public AjaxResult authorize(@RequestBody WxRequestDTO wxRequestDTO, HttpServletRequest request) {
        if (wxRequestDTO == null || StringUtils.isNullOrEmpty(wxRequestDTO.getCode())) {
            throw new RuntimeException("wx code不能为空");
        }
        return AjaxResult.success(wxLoginService.login(wxRequestDTO,request));
    }

    @PostMapping("/phone")
    @Operation(summary = "微信获取电话号码", description = "获取电话号码")
    public AjaxResult phoneNumber(@RequestBody WxRequestDTO wxRequestDTO) {
        if (wxRequestDTO == null || StringUtils.isNullOrEmpty(wxRequestDTO.getCode())) {
            throw new RuntimeException("wx code不能为空");
        }
        return AjaxResult.success(wxLoginService.getPhone(wxRequestDTO.getCode()));
    }



}
