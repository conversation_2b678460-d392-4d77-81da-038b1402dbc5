package cn.naii.iot.controller;

import cn.naii.iot.common.web.AjaxResult;
import cn.naii.iot.request.GetTimbreRequest;
import cn.naii.iot.service.SysTimbreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/timbre")
@Tag(name = "音色管理", description = "音色管理")
public class TimbreController {
    @Resource
    private SysTimbreService sysTimbreService;
    @PostMapping("/list")
    @Operation(summary = "获取设备音色列表", description = "获取设备音色列表")
    public AjaxResult list(@RequestBody GetTimbreRequest request) {
        return AjaxResult.success(sysTimbreService.getTimbreList(request.getProvider(),request.getValue()));
    }
}
