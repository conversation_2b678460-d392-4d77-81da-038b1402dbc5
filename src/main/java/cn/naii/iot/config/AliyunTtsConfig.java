package cn.naii.iot.config;

import cn.naii.iot.dialogue.tts.providers.AliyunTtsService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云TTS配置类
 * 用于注入全局配置到AliyunTtsService
 */
@Configuration
public class AliyunTtsConfig {
    private static final Logger logger = LoggerFactory.getLogger(AliyunTtsConfig.class);

    @Value("${aliyun.tts.request.interval.ms:500}")
    private long requestIntervalMs;

    @PostConstruct
    public void init() {
        // 将配置注入到AliyunTtsService的静态变量中
        AliyunTtsService.setRequestIntervalMs(requestIntervalMs);
        logger.info("✅ 阿里云TTS配置初始化完成 - 请求间隔: {}ms", requestIntervalMs);
    }
}

