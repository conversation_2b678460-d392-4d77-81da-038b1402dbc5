package cn.naii.iot.service;

import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.request.BindRequest;
import cn.naii.iot.request.DelDeviceRequest;
import cn.naii.iot.request.DeviceRequest;
import cn.naii.iot.request.UpTimbreRequest;
import cn.naii.iot.request.VolumeRequest;
import cn.naii.iot.resp.DeviceResp;
import cn.naii.iot.resp.DeviceResp2;
import org.apache.ibatis.javassist.NotFoundException;

import java.util.List;

/**
 * 设备查询/更新
 *
 * <AUTHOR>
 */
public interface SysDeviceService {

    /**
     * 添加设备
     *
     * @param device
     * @return
     */
    int add(SysDevice device) throws NotFoundException;

    /**
     * 查询设备信息
     *
     * @param device
     * @return
     */
    List<SysDevice> query(SysDevice device, PageFilter pageFilter);

    /**
     * 查询设备信息
     *
     * @param device
     * @return
     */
    List<SysDevice> queryExactly(SysDevice device, PageFilter pageFilter);

    /**
     * 查询设备信息，并join配置表联查，用来过滤不存在的configId
     *
     * @param deviceId 设备id
     * @return
     */
    SysDevice selectDeviceById(String deviceId);

    /**
     * 查询验证码
     */
    SysDevice queryVerifyCode(SysDevice device);

    /**
     * 查询并生成验证码
     */
    SysDevice generateCode(SysDevice device);

    /**
     * 关系设备验证码语音路径
     */
    int updateCode(SysDevice device);

    /**
     * 更新设备信息
     *
     * @param device
     * @return
     */
    int update(SysDevice device);

    /**
     * 删除设备
     *
     * @param device
     * @return
     */
    int delete(SysDevice device);

    /**
     * 生成设备访问平台的token
     *
     * @param deviceId
     * @return
     */
    String generateToken(String deviceId);


    List<DeviceResp> selectDeviceList(DeviceRequest request);

    List<DeviceResp2> getDeviceList(String openId);

    int updateVolume(VolumeRequest request);


    int delDevice(DelDeviceRequest device);

    int bindDevice(BindRequest request);


    int updateTimbre(UpTimbreRequest request);

}