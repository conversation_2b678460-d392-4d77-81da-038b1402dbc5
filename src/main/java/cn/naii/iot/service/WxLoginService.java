package cn.naii.iot.service;

import cn.naii.iot.dto.WxRequestDTO;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.utils.CmsUtils;
import cn.naii.iot.utils.HttpUtils;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;


/**
 * 微信登录服务相关的接口
 */
@Service
@Slf4j
public class WxLoginService {
    @Value("${wx.url.login}")
    private String loginUrl;

    @Value("${wx.url.token}")
    private String tokenUrl;

    @Value("${wx.url.phone}")
    private String phoneUrl;

    @Value("${wx.appId}")
    private String appId;

    @Value("${wx.secret}")
    private String secret;

    @Resource
    private SysUserService sysUserService;

    /**
     * 接口参数
     */
    private static final String APPID = "appid";
    private static final String SECRET = "secret";
    private static final String JSCODE = "js_code";
    private static final String GRANT_TYPE = "grant_type";
    private static final String ACCESS_TOKEN = "access_token";
    private static final String CODE = "code";


    /**
     *检查用户是否存在，不存在进行创建
     */
    public SysUser checkExistOrCreate(SysUser user,String openid) {
        if (user != null) {
            return user;
        }
        user = new SysUser();
        user.setOpenid(openid);
        user.setState("1");
        user.setIsAdmin("0");
        //user name 先和 openid保持一致，后期可修改
        user.setUsername(openid);
        user.setName(openid);
        user.setCreateTime(new java.util.Date());
        user.setUpdateTime(new java.util.Date());
        //随机生成一个uuid作为密码，不使用密码登录
        user.setPassword(UUID.randomUUID().toString());
        sysUserService.add(user);
        //由于这里的user不会填充userId，需要再次查询，进行填充
        return sysUserService.selectUserByOpenId(openid);
    }

    /**
     * 执行 登录，系统基于session实现登录
     */
    public void doLogin(HttpServletRequest request, SysUser user) {
        // 保存用户到会话
        HttpSession session = request.getSession();
        session.setAttribute(SysUserService.USER_SESSIONKEY, user);
        // 保存用户
        CmsUtils.setUser(request, user);
    }

    public SysUser login(WxRequestDTO wxRequestDTO, HttpServletRequest request) {
        //构建查询参数
       Map<String, Object> params =  Map.of(APPID, appId, SECRET, secret, JSCODE, wxRequestDTO.getCode(), GRANT_TYPE, "authorization_code");
       log.info("小程序登录参数:{}",params);
        try {
            JSONObject result = HttpUtils.get(loginUrl, params);
            checkResult(result,"login");
            //提取 openid,session_key
            String openid = result.getString("openid");
            // 后续如果使用，再进行存储
            String session_key = result.getString("session_key");
            SysUser user = sysUserService.selectUserByOpenId(openid);
            //新用户登录，自动完成注册
            user = checkExistOrCreate(user,openid);
            //实现登录
            doLogin(request, user);
            user.setPassword(null);
            return user;

        } catch (Exception e) {
            log.error("小程序登录失败",e);
            throw new RuntimeException(e);
        }
    }

    public void checkResult(JSONObject result,String type) {
        if (result == null) {
            log.error("小程序接口调用异常,调用类型:{}",type);
            throw new RuntimeException("小程序接口调用异常");
        }
        if (result.containsKey("errcode")) {
            //正常错误码
            if (result.getIntValue("errcode") == 0) {
               return;
            }
            //异常错误码
            String errmsg = result.getString("errmsg");
            //如果异常信息是ok 忽略
            if ("ok".equals(errmsg)) {
                return;
            }
            log.error("小程序接口调用失败,调用类型:{} err:msg{}",type,result.getString("errmsg"));
            //抛出异常
            throw new RuntimeException(result.getString("errmsg"));
        }
    }


    public String getPhone(String code) {
        String accessToken = getAccessToken();
        Map<String, Object> params =  Map.of(ACCESS_TOKEN, accessToken,CODE, code);
        try {
            JSONObject result = HttpUtils.get(phoneUrl, params);
            checkResult(result,"phone");
            return result.getString("access_token");

        } catch (IOException e) {
            log.error("获取电话号码失败",e);
            throw new RuntimeException(e);
        }

    }


    public String getAccessToken() {
        Map<String, Object> params =  Map.of(APPID, appId, SECRET, secret, GRANT_TYPE, "client_credential");
        try {
            JSONObject result = HttpUtils.get(tokenUrl, params);
            checkResult(result,"accessToken");
            return result.getString("access_token");

        } catch (IOException e) {
            log.error("获取小程序token失败",e);
            throw new RuntimeException(e);
        }
    }

}
