package cn.naii.iot.service;

import cn.naii.iot.common.exception.UserPasswordNotMatchException;
import cn.naii.iot.common.exception.UsernameNotFoundException;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.UpUserRequest;
import cn.naii.iot.request.UserRequest;
import cn.naii.iot.resp.UserResp;

import java.util.List;

/**
 * 用户操作
 * 
 * <AUTHOR>
 * 
 */
public interface SysUserService {

    /**
     * 用户名sessionkey
     */
    public static final String USER_SESSIONKEY = "user_sessionkey";

    /**
     * 登录校验
     * 
     * @param username
     * @param password
     * @return
     */
    SysUser login(String username, String password)
            throws UsernameNotFoundException, UserPasswordNotMatchException;

    /**
     * 查询用户信息
     * 
     * @param username
     * @return 用户信息
     */
    SysUser query(String username);

    /**
     * 用户查询列表
     * 
     * @param user
     * @return 用户列表
     */
    List<SysUser> queryUsers(SysUser user, PageFilter pageFilter);

    SysUser selectUserByUserId(Integer userId);

    SysUser selectUserByUsername(String username);

    SysUser selectUserByEmail(String email);

    SysUser selectUserByOpenId(String openId);

    /**
     * 新增用户
     * 
     * @param user
     * @return
     */
    int add(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param user
     * @return
     */
    int update(SysUser user);

    /**
     * 生成验证码
     * 
     */
    SysUser generateCode(SysUser user);

    /**
     * 查询验证码是否有效
     * 
     * @param code
     * @param email
     * @return
     */
    int queryCaptcha(String code, String email);



    int updateUser(UpUserRequest request);


    UserResp getUserInfo(UserRequest request);

}