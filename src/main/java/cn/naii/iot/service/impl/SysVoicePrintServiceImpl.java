package cn.naii.iot.service.impl;

import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.dao.SysVoicePrintMapper;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.entity.SysVoicePrint;
import cn.naii.iot.request.VoicePrintRequest;
import cn.naii.iot.service.SysVoicePrintService;
import com.github.pagehelper.PageHelper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class SysVoicePrintServiceImpl implements SysVoicePrintService {
    @Resource
    private SysUserServiceImpl userService;

    @Resource
    private SysVoicePrintMapper sysVoicePrintMapper;

    @Override
    public List<SysVoicePrint> list(VoicePrintRequest request, PageFilter pageFilter) {
        validUser(request.getUserId(),request.getOpenId());
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        SysVoicePrint sysVoicePrint = new SysVoicePrint().setVoiceName(request.getVoiceName()).setUserId(request.getUserId());
        return sysVoicePrintMapper.select(sysVoicePrint);
    }

    @Override
    public int add(VoicePrintRequest request) {
        validUser(request.getUserId(),request.getOpenId());
        SysVoicePrint sysVoicePrint = new SysVoicePrint().setVoiceName(request.getVoiceName()).setCreatetime(new Date()).setUpdatetime(new Date()).setUserId(request.getUserId());
        return sysVoicePrintMapper.insert(sysVoicePrint);
    }

    @Override
    public int delete(VoicePrintRequest request) {
        validUser(request.getUserId(),request.getOpenId());
        if(request.getVoicePrintId() == null){
            return 0;
        }
        return sysVoicePrintMapper.deleteByPrimaryKey(request.getVoicePrintId());
    }

    private void validUser(String uid, String openId) {
        SysUser currentUser = userService.selectUserByUserId(Integer.valueOf(uid));
        if (currentUser == null) {
            throw new RuntimeException("用户信息不存在");
        }
        if (!currentUser.getOpenid().equals(openId)) {
            throw new RuntimeException("用户信息不匹配");
        }
    }
}
