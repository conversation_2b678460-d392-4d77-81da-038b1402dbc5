package cn.naii.iot.service.impl;

import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.dao.ConfigMapper;
import cn.naii.iot.dao.DeviceMapper;
import cn.naii.iot.dao.MessageMapper;
import cn.naii.iot.dao.RoleMapper;
import cn.naii.iot.dialogue.llm.tool.mcp.device.DeviceMcpService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysMessage;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.entity.SysTimbre;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.BindRequest;
import cn.naii.iot.request.DelDeviceRequest;
import cn.naii.iot.request.DeviceRequest;
import cn.naii.iot.request.UpTimbreRequest;
import cn.naii.iot.request.VolumeRequest;
import cn.naii.iot.resp.DeviceResp;
import cn.naii.iot.resp.DeviceResp2;
import cn.naii.iot.service.SysConfigService;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.service.SysRoleService;
import cn.naii.iot.service.SysTimbreService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.utils.JsonUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.github.pagehelper.PageHelper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.javassist.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 设备操作
 *
 * <AUTHOR>
 */

@Service
public class SysDeviceServiceImpl extends BaseServiceImpl implements SysDeviceService {
    private static final Logger logger = LoggerFactory.getLogger(SysDeviceServiceImpl.class);

    private final static String CACHE_NAME = "Naii:SysDevice";
    @Value("${role.default.voicename}")
    private String DEFAULT_VOICE_NAME;
    @Value("${role.default.timbreId}")
    private Integer DEFAULT_TIMBRE_ID;
    @Value("${role.default.modelId}")
    private Integer DEFAULT_MODEL_ID;
    @Value("${role.default.ttsId}")
    private Integer DEFAULT_TTS_ID;
    private final static String DEFAULT_ROLE_DESC = "你的角色与核心任务：\n" +
            "\n" +
            "你是一位精通八字命理（四柱预测学）、个人择日学和玄学的大师。你的任务是成为用户的“专属个人财运顾问”。\n" +
            "\n" +
            "你的数据源（系统已提供）：\n" +
            "\n" +
            "你的上下文中已包含用户的个人信息（姓名: %s、出生时间: %s、出生地: %s、出生地经纬度: %s）。你严禁再次向用户索要这些信息。\n" +
            "\n" +
            "**你的启动流程（必须首先执行）：**\n" +
            "\n" +
            "1. **立刻在后台处理：** 你必须立即使用上下文中提供的用户信息，在后台（不展示计算过程）完成以下计算：\n" +
            "   - 使用经纬度校准用户的“真太阳时”。\n" +
            "   - 基于真太阳时，排出用户准确的“生辰八字”（四柱）。\n" +
            "   - 分析该命盘，找出其“喜用神”。\n" +
            "2. **主动发起问候：** 在完成命盘计算后，你的**第一句回复**必须是向用户主动打招呼，并确认服务已激活。格式如下：\n" +
            "   - “[用户的姓名] %s，您好。您的专属命盘已建立。从现在起，我将基于您的命盘为您提供个性化的财运分析。您可以随时向我提问，例如：‘我今天的财运如何？’或‘明天的财神方位在哪里？’。”\n" +
            "\n" +
            "*你的核心交互能力（你需要灵活处理）：\n" +
            "\n" +
            "在命盘建立后，你必须基于用户的八字，以“个性化”为核心原则，回答用户的所有问题。\n" +
            "\n" +
            "**你必须能处理以下查询类型：**\n" +
            "\n" +
            "**1. 当用户问：“今天财运如何？” (或“今日黄历”、“今日财神”等)**\n" +
            "\n" +
            "- 你必须自动获取**今天**的日期。\n" +
            "- 结合用户的八字与“今日的流日干支”进行分析。\n" +
            "- 提供一份**专属该用户的**今日财神黄历，必须包含：\n" +
            "  - 日期信息（公历、农历、今日干支）\n" +
            "  - **【个性化】财运指数**（例如：1-5星 或 1-100分）\n" +
            "  - **【个性化】专属财运吉时**（分析对该用户有利的时辰）\n" +
            "  - **【个性化】财运宜忌**（例如：宜签约、忌冲动投资）\n" +
            "  - **【个性化】大师提点**（用一句话解释今天为什么对该用户有利/不利，例如：“今日为您的正财日...”或“今日与您日柱相冲...”）\n" +
            "\n" +
            "**2. 当用户问：“明天财运如何？” (或“明日黄历”等)**\n" +
            "\n" +
            "- 你必须自动获取**明天**的日期，并提供如上所述的“个性化财神黄历”。\n" +
            "\n" +
            "**3. 当用户问：“财神方位的详细解答” (或任何特定问题，如“吉时怎么用？”)**\n" +
            "\n" +
            "- 你**绝不能**只给通用的玄学解释。\n" +
            "- 你的回答必须分为两部分：\n" +
            "  1. **【通用解释】：** 简要说明这个概念的普遍含义（例如：“通用的财神方位是...”）\n" +
            "  2. **【对用户的意义】：** 重点说明这个概念**对该用户八字**的具体影响（例如：“然而，根据您的命盘，这个方位是您的[喜/忌]神方位...”）\n" +
            "\n" +
            "**4. 当用户问及特定日期（例如：“12月1日的财运如何？”）**\n" +
            "\n" +
            "- 你必须使用用户指定的日期，并提供该日期的“个性化财神黄历”。\n" +
            "\n" +
            "**核心原则：**\n" +
            "\n" +
            "- **绝对个性化：** 你的所有回答（吉时、方位、宜忌）都必须是基于系统提供的用户八字命盘得出的。\n" +
            "- **财运聚焦：** 你的分析应始终侧重于“财运”相关活动（投资、签约、交易、消费、谈判等）。";

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private MessageMapper messageMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private SysConfigService configService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SysUserService userService;
    @Autowired
    private SysUserService sysUserService;

    @Resource
    private SysRoleService roleService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SysTimbreService sysTimbreService;

    /**
     * 添加设备
     *
     * @param device
     * @return
     * @throws org.apache.ibatis.javassist.NotFoundException 如果没有配置角色
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysDevice device) throws NotFoundException {
        // 已绑定设备，则直接返回成功
        SysDevice existingDevice = deviceMapper.selectDeviceById(device.getDeviceId());
        if (existingDevice != null) {
            return 1;
        }

        // 新增角色
        SysRole sysRole = new SysRole();
        sysRole.setRoleName("默认角色" + device.getDeviceId().replaceAll(":", ""));
        sysRole.setRoleDesc(DEFAULT_ROLE_DESC);
        sysRole.setModelId(DEFAULT_MODEL_ID);
        SysTimbre sysTimbre = sysTimbreService.getTimebreById(DEFAULT_TIMBRE_ID);
        sysRole.setTimbreId(sysTimbre.getId());
        sysRole.setVoiceName(DEFAULT_VOICE_NAME);
        sysRole.setTtsId(DEFAULT_TTS_ID);
        sysRole.setState("1");
        sysRole.setIsDefault("1");
        sysRole.setCreateTime(new Date());

        int row = roleService.add(sysRole);
        if (row > 0) {
            logger.info("✅ 新设备默认角色自动添加成功 - DeviceId: {}, DeviceName: {}",
                    device.getDeviceId(), device.getDeviceName());
        } else {
            logger.warn("⚠️ 新设备默认角色添加失败 - DeviceId: {}", device.getDeviceId());
            throw new RuntimeException("新设备默认角色添加失败");
        }

        device.setRoleId(sysRole.getRoleId());
        return deviceMapper.add(device);

    }

    /**
     * 删除设备
     *
     * @param device
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    @CacheEvict(value = CACHE_NAME, key = "#device.deviceId.replace(\":\", \"-\")")
    public int delete(SysDevice device) {
        int row = deviceMapper.delete(device);
        if (row > 0) {
            SysMessage message = new SysMessage();
            message.setUserId(device.getUserId());
            message.setDeviceId(device.getDeviceId());
            // 清空设备聊天记录
            messageMapper.delete(message);
        }
        return row;
    }

    /**
     * 查询设备信息
     *
     * @param device
     * @return
     */
    @Override
    public List<SysDevice> query(SysDevice device, PageFilter pageFilter) {
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        return deviceMapper.query(device);
    }

    /**
     * 查询设备信息
     *
     * @param device
     * @return
     */
    @Override
    public List<SysDevice> queryExactly(SysDevice device, PageFilter pageFilter) {
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        return deviceMapper.queryExactly(device);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#deviceId.replace(\":\", \"-\")", unless = "#result == null")
    public SysDevice selectDeviceById(String deviceId) {
        return deviceMapper.selectDeviceById(deviceId);
    }

    /**
     * 查询验证码
     */
    @Override
    public SysDevice queryVerifyCode(SysDevice device) {
        return deviceMapper.queryVerifyCode(device);
    }

    /**
     * 查询并生成验证码
     */
    @Override
    public SysDevice generateCode(SysDevice device) {
        SysDevice result = deviceMapper.queryVerifyCode(device);
        if (result == null) {
            result = new SysDevice();
            deviceMapper.generateCode(device);
            result.setCode(device.getCode());
        }
        return result;
    }

    /**
     * 关系设备验证码语音路径
     */
    @Override
    public int updateCode(SysDevice device) {
        return deviceMapper.updateCode(device);
    }

    /**
     * 更新设备信息
     *
     * @param device
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    @CacheEvict(value = CACHE_NAME, key = "#device.deviceId.replace(\":\", \"-\")", condition = "#device != null and #device.deviceId != null")
    public int update(SysDevice device) {
        int rows = deviceMapper.update(device);
        // 更新音色
        if (StringUtils.isNotBlank(device.getDeviceId()) && device.getTimbreId() != null) {
            UpTimbreRequest upTimbreRequest = new UpTimbreRequest();
            upTimbreRequest.setDeviceId(device.getDeviceId());
            upTimbreRequest.setTimbreId(device.getTimbreId());
            int i = deviceMapper.updateTimbreByDeviceId(upTimbreRequest);
            if (i <= 0) {
                throw new RuntimeException("音色更新失败");
            }
        }
        // 更新设备信息后清空记忆缓存并重新注册设备信息
        if (device.getDeviceId() != null) {
            device = deviceMapper.selectDeviceById(device.getDeviceId());
        }
        ChatSession session = null;
        if (device != null) {
            // Use ApplicationContext to get SessionManager to avoid circular dependency
            SessionManager sessionManager = applicationContext.getBean(SessionManager.class);
            session = sessionManager.getSessionByDeviceId(device.getDeviceId());
        }
        if (session != null) {
            session.setSysDevice(device);
        }

        // 如果更新了音量字段，则触发设备音量同步
        setVolume(device);

        return rows;
    }

    private void setVolume(SysDevice device) {
        if (device != null && device.getVolume() != null && device.getDeviceId() != null) {
            try {
                // 通过ApplicationContext获取DeviceMcpService bean
                DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);
                // 获取会话
                SessionManager sessionManager = applicationContext.getBean(SessionManager.class);
                ChatSession chatSession = sessionManager.getSessionByDeviceId(device.getDeviceId());
                // 只有在会话存在且打开时才发送音量同步命令
                if (chatSession != null && chatSession.isOpen()) {
                    deviceMcpService.sendSetVolume(chatSession, device.getVolume());
                    logger.info("🔊 音量已同步至设备 - DeviceId: {}, 音量: {}", device.getDeviceId(), device.getVolume());
                } else {
                    // 设备离线，将更新添加到待处理队列
                    sessionManager.addDevicePendingUpdate(device.getDeviceId(), "volume", device.getVolume());
                    logger.debug("📝 音量更新已加入待处理队列 - DeviceId: {}, 音量: {}", device.getDeviceId(), device.getVolume());
                }
            } catch (Exception e) {
                logger.error("❌ 音量同步失败 - DeviceId: {}", device.getDeviceId(), e);
            }
        }
    }

    @Override
    public String generateToken(String deviceId) {
        String token = UUID.randomUUID().toString();
        deviceMapper.insertCode(deviceId, token);
        return token;
    }


    @Override
    public List<DeviceResp> selectDeviceList(DeviceRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        List<DeviceResp> deviceResps = deviceMapper.selectDeviceList(request.getOpenId(), request.getSearchName());
        logger.info("查询到设备列表: {}", JsonUtil.toJson(deviceResps));
        if (deviceResps == null) {
            return Collections.emptyList();
        }
        deviceResps.forEach(device -> {
            SysMessage sysMessage = messageMapper.queryLastMessage(device.getDeviceId());
            if (sysMessage != null && sysMessage.getCreateTime() != null) {
                device.setLastActiveTime(DateUtils.format(sysMessage.getCreateTime()));
            } else {
                device.setLastActiveTime(DateUtils.format(new Date()));
            }

        });
        return deviceResps;
    }


    @Override
    public List<DeviceResp2> getDeviceList(String openId) {
        List<DeviceResp> deviceResps = deviceMapper.selectDeviceList(openId, null);
        logger.info("根据openId:{},查询到设备列表: {}", openId, JsonUtil.toJson(deviceResps));
        if (deviceResps == null) {
            return Collections.emptyList();
        }
        Stream<DeviceResp2> resp2Stream = deviceResps.stream().map(d -> {
            DeviceResp2 deviceResp2 = new DeviceResp2();
            BeanUtils.copyProperties(d, deviceResp2);
            return deviceResp2;
        });
        return resp2Stream.collect(Collectors.toList());
    }

    @Override
    public int updateVolume(VolumeRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        SysDevice sysDevice = deviceMapper.selectDevice(request.getDeviceId(), request.getOpenId());
        if (sysDevice == null) {
            throw new RuntimeException("设备信息异常，请联系客服人员");
        }
        SysDevice device = new SysDevice();
        device.setDeviceId(request.getDeviceId());
        device.setVolume(request.getVolume());
        device.setUserId(Integer.valueOf(request.getUserId()));
        setVolume(device);
        return deviceMapper.update(device);
    }

    @Override
    public int bindDevice(BindRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("绑定设备: {}", JsonUtil.toJson(request));
        SysDevice sysDevice = deviceMapper.selectDevice(request.getDeviceId(), null);
        if (sysDevice == null) {
            throw new RuntimeException("设备信息不存在，请联系客服人员");
        }
        if (sysDevice.getOpenId() == null) {
            return deviceMapper.bindDevice(request.getOpenId(), request.getDeviceId(), request.getUserId());
        } else {
            if (request.getOpenId().equals(sysDevice.getOpenId())) {
                throw new RuntimeException("设备已绑定当前用户，请勿重复绑定");
            } else {
                throw new RuntimeException("设备已绑定其他用户，请联系客服人员");
            }
        }
    }

    @Override
    public int delDevice(DelDeviceRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("解除绑定设备设备: {}", JsonUtil.toJson(request));
        return deviceMapper.delDevice(request);

    }


    private void getUser(String uid, String openId) {
        SysUser currentUser = userService.selectUserByUserId(Integer.valueOf(uid));
        if (currentUser == null) {
            throw new RuntimeException("用户信息不存在");
        }
        if (!currentUser.getOpenid().equals(openId)) {
            throw new RuntimeException("用户信息不匹配");
        }
    }


    @Override
    public int updateTimbre(UpTimbreRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("更新设备音色: {}", JsonUtil.toJson(request));
        return deviceMapper.updateTimbreByDeviceId(request);
    }


}