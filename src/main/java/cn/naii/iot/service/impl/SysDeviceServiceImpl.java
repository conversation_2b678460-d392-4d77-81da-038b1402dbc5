package cn.naii.iot.service.impl;

import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.dao.ConfigMapper;
import cn.naii.iot.dao.DeviceMapper;
import cn.naii.iot.dao.MessageMapper;
import cn.naii.iot.dao.RoleMapper;
import cn.naii.iot.dialogue.llm.tool.mcp.device.DeviceMcpService;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysMessage;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.*;
import cn.naii.iot.resp.DeviceResp;
import cn.naii.iot.service.SysConfigService;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.utils.JsonUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.github.pagehelper.PageHelper;
import jakarta.annotation.Resource;
import org.apache.ibatis.javassist.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 设备操作
 *
 * <AUTHOR>
 *
 */

@Service
public class SysDeviceServiceImpl extends BaseServiceImpl implements SysDeviceService {
    private static final Logger logger = LoggerFactory.getLogger(SysDeviceServiceImpl.class);

    private final static String CACHE_NAME = "Naii:SysDevice";

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private MessageMapper messageMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private SysConfigService configService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SysUserService userService;
    @Autowired
    private SysUserService sysUserService;

    /**
     * 添加设备
     *
     * @param device
     * @return
     * @throws org.apache.ibatis.javassist.NotFoundException 如果没有配置角色
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysDevice device) throws NotFoundException {

        SysDevice existingDevice = deviceMapper.selectDeviceById(device.getDeviceId());
        if (existingDevice != null) {
            return 1;
        }

        // 查询是否有默认角色
        SysRole queryRole = new SysRole();
        queryRole.setUserId(device.getUserId());
        List<SysRole> roles = roleMapper.query(queryRole);

        if (roles.isEmpty()) {
            throw new NotFoundException("没有配置角色");
        }

        SysRole selectedRole = null;

        // 优先绑定默认角色
        for (SysRole role : roles) {
            if (("1").equals(role.getIsDefault())) {
                selectedRole = role;
                break;
            }
        }
        if (selectedRole == null) {
            selectedRole = roles.getFirst();
        }

        device.setRoleId(selectedRole.getRoleId());
        return deviceMapper.add(device);

    }

    /**
     * 删除设备
     *
     * @param device
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    @CacheEvict(value = CACHE_NAME, key = "#device.deviceId.replace(\":\", \"-\")")
    public int delete(SysDevice device) {
        int row = deviceMapper.delete(device);
        if (row > 0) {
            SysMessage message = new SysMessage();
            message.setUserId(device.getUserId());
            message.setDeviceId(device.getDeviceId());
            // 清空设备聊天记录
            messageMapper.delete(message);
        }
        return row;
    }

    /**
     * 查询设备信息
     *
     * @param device
     * @return
     */
    @Override
    public List<SysDevice> query(SysDevice device, PageFilter pageFilter) {
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        return deviceMapper.query(device);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "#deviceId.replace(\":\", \"-\")", unless = "#result == null")
    public SysDevice selectDeviceById(String deviceId) {
        return deviceMapper.selectDeviceById(deviceId);
    }

    /**
     * 查询验证码
     */
    @Override
    public SysDevice queryVerifyCode(SysDevice device) {
        return deviceMapper.queryVerifyCode(device);
    }

    /**
     * 查询并生成验证码
     */
    @Override
    public SysDevice generateCode(SysDevice device) {
        SysDevice result = deviceMapper.queryVerifyCode(device);
        if (result == null) {
            result = new SysDevice();
            deviceMapper.generateCode(device);
            result.setCode(device.getCode());
        }
        return result;
    }

    /**
     * 关系设备验证码语音路径
     */
    @Override
    public int updateCode(SysDevice device) {
        return deviceMapper.updateCode(device);
    }

    /**
     * 更新设备信息
     *
     * @param device
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    @CacheEvict(value = CACHE_NAME, key = "#device.deviceId.replace(\":\", \"-\")")
    public int update(SysDevice device) {
        int rows = deviceMapper.update(device);
        // 更新设备信息后清空记忆缓存并重新注册设备信息
        if (device.getDeviceId() != null) {
            device = deviceMapper.selectDeviceById(device.getDeviceId());
        }
        ChatSession session = null;
        if (device != null) {
            // Use ApplicationContext to get SessionManager to avoid circular dependency
            SessionManager sessionManager = applicationContext.getBean(SessionManager.class);
            session = sessionManager.getSessionByDeviceId(device.getDeviceId());
        }
        if (session != null) {
            session.setSysDevice(device);
        }

        // 如果更新了音量字段，则触发设备音量同步
        if (device != null && device.getVolume() != null && device.getDeviceId() != null) {
            try {
                // 通过ApplicationContext获取DeviceMcpService bean
                DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);
                // 获取会话
                SessionManager sessionManager = applicationContext.getBean(SessionManager.class);
                ChatSession chatSession = sessionManager.getSessionByDeviceId(device.getDeviceId());
                // 只有在会话存在且打开时才发送音量同步命令
                if (chatSession != null && chatSession.isOpen()) {
                    deviceMcpService.sendSetVolume(chatSession, device.getVolume());
                    logger.info("🔊 音量已同步至设备 - DeviceId: {}, 音量: {}", device.getDeviceId(), device.getVolume());
                } else {
                    // 设备离线，将更新添加到待处理队列
                    sessionManager.addDevicePendingUpdate(device.getDeviceId(), "volume", device.getVolume());
                    logger.debug("📝 音量更新已加入待处理队列 - DeviceId: {}, 音量: {}", device.getDeviceId(), device.getVolume());
                }
            } catch (Exception e) {
                logger.error("❌ 音量同步失败 - DeviceId: {}", device.getDeviceId(), e);
            }
        }

        return rows;
    }

    @Override
    public String generateToken(String deviceId) {
        String token = UUID.randomUUID().toString();
        deviceMapper.insertCode(deviceId, token);
        return token;
    }


    @Override
    public List<DeviceResp> selectDeviceList(DeviceRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        List<DeviceResp> deviceResps = deviceMapper.selectDeviceList(request.getOpenId(), request.getSearchName());
        logger.info("查询到设备列表: {}", JsonUtil.toJson(deviceResps));
        if (deviceResps == null) {
            return Collections.emptyList();
        }
        deviceResps.forEach(device -> {
            SysMessage sysMessage = messageMapper.queryLastMessage(device.getDeviceId());
            device.setLastActiveTime(DateUtils.format(sysMessage.getCreateTime()));
        });
        return deviceResps;
    }


    @Override
    public int updateVolume(VolumeRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        SysDevice sysDevice = deviceMapper.selectDevice(request.getDeviceId(), request.getOpenId());
        if (sysDevice == null) {
            throw new RuntimeException("设备信息异常，请联系客服人员");
        }
        SysDevice device = new SysDevice();
        device.setDeviceId(request.getDeviceId());
        device.setVolume(request.getVolume());
        device.setUserId(Integer.valueOf(request.getUserId()));
        return deviceMapper.update(device);
    }

    @Override
    public int bindDevice(BindRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("绑定设备: {}", JsonUtil.toJson(request));
        SysDevice sysDevice = deviceMapper.selectDevice(request.getDeviceId(), null);
        if (sysDevice == null) {
            throw new RuntimeException("设备信息不存在，请联系客服人员");
        }
        if (sysDevice.getOpenId() == null) {
            return deviceMapper.bindDevice(request.getOpenId(), request.getDeviceId(), request.getUserId());
        } else {
            if (request.getOpenId().equals(sysDevice.getOpenId())) {
                throw new RuntimeException("设备已绑定当前用户，请勿重复绑定");
            } else {
                throw new RuntimeException("设备已绑定其他用户，请联系客服人员");
            }
        }
    }

    @Override
    public int delDevice(DelDeviceRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("解除绑定设备设备: {}", JsonUtil.toJson(request));
        return deviceMapper.delDevice(request);

    }


    private void getUser(String uid, String openId) {
        SysUser currentUser = userService.selectUserByUserId(Integer.valueOf(uid));
        if (currentUser == null) {
            throw new RuntimeException("用户信息不存在");
        }
        if (!currentUser.getOpenid().equals(openId)) {
            throw new RuntimeException("用户信息不匹配");
        }
    }


    @Override
    public int updateTimbre(UpTimbreRequest request) {
        getUser(request.getUserId(), request.getOpenId());
        logger.info("更新设备音色: {}", JsonUtil.toJson(request));
        return deviceMapper.updateTimbre(request);
    }
}