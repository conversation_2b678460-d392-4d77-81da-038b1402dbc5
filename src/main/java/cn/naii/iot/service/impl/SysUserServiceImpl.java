package cn.naii.iot.service.impl;

import cn.naii.iot.common.exception.UserPasswordNotMatchException;
import cn.naii.iot.common.exception.UsernameNotFoundException;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.dao.ConfigMapper;
import cn.naii.iot.dao.DeviceMapper;
import cn.naii.iot.dao.RoleMapper;
import cn.naii.iot.dao.UserMapper;
import cn.naii.iot.dto.CountDTO;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.UpUserRequest;
import cn.naii.iot.request.UserRequest;
import cn.naii.iot.request.manage.TotalRequest;
import cn.naii.iot.resp.TotalInfoResp;
import cn.naii.iot.resp.TotalMessage;
import cn.naii.iot.resp.UserResp;
import cn.naii.iot.security.AuthenticationService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.utils.DateUtils;
import cn.naii.iot.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 用户操作
 *
 * <AUTHOR>
 */

@Service
public class SysUserServiceImpl extends BaseServiceImpl implements SysUserService {

    private static final Logger logger = LoggerFactory.getLogger(SysUserServiceImpl.class);

    private static final String dayOfMonthStart = DateUtils.dayOfMonthStart();
    private static final String dayOfMonthEnd = DateUtils.dayOfMonthEnd();

    @Resource
    private UserMapper userMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private AuthenticationService authenticationService;

    @Resource
    private DeviceMapper deviceMapper;

    /**
     * @param username
     * @param password
     * @return 用户登录信息
     * @throws UsernameNotFoundException
     * @throws UserPasswordNotMatchException
     */
    @Override
    public SysUser login(String username, String password)
            throws UsernameNotFoundException, UserPasswordNotMatchException {
        SysUser user = userMapper.selectUserByUsername(username);
        if (ObjectUtils.isEmpty(user)) {
            throw new UsernameNotFoundException();
        } else if (!authenticationService.isPasswordValid(password, user.getPassword())) {
            throw new UserPasswordNotMatchException();
        }
        return user;
    }

    /**
     * 用户信息查询
     *
     * @param username
     * @return 用户信息
     */
    @Override
    public SysUser query(String username) {
        return userMapper.query(username, dayOfMonthStart, dayOfMonthEnd);
    }

    /**
     * 用户列表查询
     *
     * @param user
     * @return 用户列表
     */
    @Override
    public List<SysUser> queryUsers(SysUser user, PageFilter pageFilter) {
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        return userMapper.queryUsers(user);
    }

    @Override
    public SysUser selectUserByUserId(Integer userId) {
        return userMapper.selectUserByUserId(userId);
    }

    @Override
    public SysUser selectUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public SysUser selectUserByEmail(String email) {
        return userMapper.selectUserByEmail(email);
    }

    @Override
    public SysUser selectUserByOpenId(String openId) {
        return userMapper.selectUserByOpenId(openId);
    }

    /**
     * 新增用户
     *
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysUser user) {
        return userMapper.add(user);
    }

    /**
     * 用户信息更改
     *
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int update(SysUser user) {
        return userMapper.update(user);
    }

    /**
     * 生成验证码
     */
    @Override
    public SysUser generateCode(SysUser user) {
        SysUser result = new SysUser();
        userMapper.generateCode(user);
        result.setCode(user.getCode());
        return result;
    }

    /**
     * 查询验证码是否有效
     *
     * @param code
     * @param email
     * @return
     */
    @Override
    public int queryCaptcha(String code, String email) {
        return userMapper.queryCaptcha(code, email);
    }

    @Override
    public int updateUser(UpUserRequest request) {
        logger.info("更新用户信息: {}", JsonUtil.toJson(request));
        getUser(request.getUserId(), request.getOpenId());
        SysUser sysUser = new SysUser();
        sysUser.setUserId(Integer.valueOf(request.getUserId()));
        sysUser.setGender(request.getGender());
        sysUser.setBirthPlace(request.getBirthPlace());
        sysUser.setCalendarType(request.getCalendarType());
        sysUser.setBirthDayTime(request.getBirthDayTime());
        sysUser.setNickname(request.getNickName());
        return userMapper.update(sysUser);
    }


    private SysUser getUser(String uid, String openId) {
        SysUser currentUser = this.selectUserByUserId(Integer.valueOf(uid));
        if (currentUser == null) {
            throw new RuntimeException("用户信息不存在");
        }
        if (!currentUser.getOpenid().equals(openId)) {
            throw new RuntimeException("用户信息不匹配");
        }
        return currentUser;
    }

    @Override
    public UserResp getUserInfo(UserRequest request) {
        SysUser user = getUser(request.getUserId(), request.getOpenId());
        UserResp userResp = new UserResp();
        BeanUtils.copyProperties(user, userResp);
        return userResp;
    }


    @Override
    public TotalInfoResp getTotalInfo(TotalRequest request) {
        TotalInfoResp resp = new TotalInfoResp();
        //获取用户和设备总数
        int countUser = userMapper.countUser();
        int countDevice = deviceMapper.countDevice(null,null);

        List<TotalMessage> results = new ArrayList<>();

        String startTime = request.getStartTime();
        String endTime = request.getEndTime();

        LocalDate startDate = LocalDate.parse(startTime);
        LocalDate endDate = LocalDate.parse(endTime);
        List<LocalDate> dateRange = generateDateRange(startDate, endDate);

        String startTimeStr = startTime + " 00:00:00";
        String endTimeStr = endDate.plusDays(1).toString() + " 00:00:00"; // 结束日期+1天

        List<CountDTO> deviceStats = deviceMapper.countDeviceByDate(startTimeStr, endTimeStr);
        List<CountDTO> userStats = userMapper.countUserByDate(startTimeStr, endTimeStr);


        Map<LocalDate, Integer> deviceMap = convertToMap(deviceStats);
        Map<LocalDate, Integer> userMap = convertToMap(userStats);

        for (LocalDate date : dateRange) {
            Integer deviceCount = deviceMap.getOrDefault(date, 0);
            Integer userCount = userMap.getOrDefault(date, 0);

            if (deviceCount > 0 || userCount > 0) {
                results.add(new TotalMessage(date, deviceCount, userCount));
            }
        }
        resp.setTotalInfoResp(results);
        resp.setTotalUser(countUser);
        resp.setTotalDevice(countDevice);
        return resp;
    }


    private List<LocalDate> generateDateRange(LocalDate start, LocalDate end) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate current = start;
        while (!current.isAfter(end)) {
            dates.add(current);
            current = current.plusDays(1);
        }
        return dates;
    }

    private Map<LocalDate, Integer> convertToMap(List<CountDTO> countList) {
        if (countList == null || countList.isEmpty()) {
            return new HashMap<>();
        }
        return countList.stream()
                .collect(Collectors.toMap(
                        CountDTO::getStat_date,
                        CountDTO::getCount_value
                ));
    }
}