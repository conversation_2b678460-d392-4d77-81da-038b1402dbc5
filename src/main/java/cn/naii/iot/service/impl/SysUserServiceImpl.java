package cn.naii.iot.service.impl;

import cn.naii.iot.common.exception.UserPasswordNotMatchException;
import cn.naii.iot.common.exception.UsernameNotFoundException;
import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.dao.ConfigMapper;
import cn.naii.iot.dao.RoleMapper;
import cn.naii.iot.dao.UserMapper;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.request.UpUserRequest;
import cn.naii.iot.request.UserRequest;
import cn.naii.iot.resp.UserResp;
import cn.naii.iot.security.AuthenticationService;
import cn.naii.iot.service.SysUserService;
import cn.naii.iot.utils.DateUtils;
import cn.naii.iot.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;


/**
 * 用户操作
 *
 * <AUTHOR>
 */

@Service
public class SysUserServiceImpl extends BaseServiceImpl implements SysUserService {

    private static final Logger logger = LoggerFactory.getLogger(SysUserServiceImpl.class);

    private static final String dayOfMonthStart = DateUtils.dayOfMonthStart();
    private static final String dayOfMonthEnd = DateUtils.dayOfMonthEnd();

    @Resource
    private UserMapper userMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private AuthenticationService authenticationService;

    /**
     * 
     * @param username
     * @param password
     * @return 用户登录信息
     * @throws UsernameNotFoundException
     * @throws UserPasswordNotMatchException
     */
    @Override
    public SysUser login(String username, String password)
            throws UsernameNotFoundException, UserPasswordNotMatchException {
        SysUser user = userMapper.selectUserByUsername(username);
        if (ObjectUtils.isEmpty(user)) {
            throw new UsernameNotFoundException();
        } else if (!authenticationService.isPasswordValid(password, user.getPassword())) {
            throw new UserPasswordNotMatchException();
        }
        return user;
    }

    /**
     * 用户信息查询
     *
     * @param username
     * @return 用户信息
     */
    @Override
    public SysUser query(String username) {
        return userMapper.query(username, dayOfMonthStart, dayOfMonthEnd);
    }

    /**
     * 用户列表查询
     *
     * @param user
     * @return 用户列表
     */
    @Override
    public List<SysUser> queryUsers(SysUser user, PageFilter pageFilter) {
        if (pageFilter != null) {
            PageHelper.startPage(pageFilter.getStart(), pageFilter.getLimit());
        }
        return userMapper.queryUsers(user);
    }

    @Override
    public SysUser selectUserByUserId(Integer userId) {
        return userMapper.selectUserByUserId(userId);
    }

    @Override
    public SysUser selectUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public SysUser selectUserByEmail(String email) {
        return userMapper.selectUserByEmail(email);
    }

    @Override
    public SysUser selectUserByOpenId(String openId) {
        return userMapper.selectUserByOpenId(openId);
    }

    /**
     * 新增用户
     *
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int add(SysUser user) {
        return userMapper.add(user);
    }

    /**
     * 用户信息更改
     *
     * @param user
     * @return
     */
    @Override
    @Transactional(transactionManager = "transactionManager")
    public int update(SysUser user) {
        return userMapper.update(user);
    }

    /**
     * 生成验证码
     */
    @Override
    public SysUser generateCode(SysUser user) {
        SysUser result = new SysUser();
        userMapper.generateCode(user);
        result.setCode(user.getCode());
        return result;
    }

    /**
     * 查询验证码是否有效
     *
     * @param code
     * @param email
     * @return
     */
    @Override
    public int queryCaptcha(String code, String email) {
        return userMapper.queryCaptcha(code, email);
    }

    @Override
    public int updateUser(UpUserRequest request) {
        logger.info("更新用户信息: {}", JsonUtil.toJson(request));
        getUser(request.getUserId(), request.getOpenId());
        SysUser sysUser = new SysUser();
        sysUser.setUserId(Integer.valueOf(request.getUserId()));
        sysUser.setGender(request.getGender());
        sysUser.setBirthPlace(request.getBirthPlace());
        sysUser.setCalendarType(request.getCalendarType());
        sysUser.setBirthDayTime(request.getBirthDayTime());
        sysUser.setNickname(request.getNickName());
        return userMapper.update(sysUser);
    }


    private SysUser getUser(String uid, String openId) {
        SysUser currentUser = this.selectUserByUserId(Integer.valueOf(uid));
        if (currentUser == null) {
            throw new RuntimeException("用户信息不存在");
        }
        if (!currentUser.getOpenid().equals(openId)) {
            throw new RuntimeException("用户信息不匹配");
        }
        return currentUser;
    }

    @Override
    public UserResp getUserInfo(UserRequest request) {
        SysUser user = getUser(request.getUserId(), request.getOpenId());
        UserResp userResp = new UserResp();
        BeanUtils.copyProperties(user, userResp);
        return userResp;
    }
}