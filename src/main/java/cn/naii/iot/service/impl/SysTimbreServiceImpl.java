package cn.naii.iot.service.impl;


import cn.naii.iot.dao.TimbreMapper;
import cn.naii.iot.entity.SysTimbre;
import cn.naii.iot.service.SysTimbreService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysTimbreServiceImpl implements SysTimbreService {


    @Resource
    private TimbreMapper timbreMapper;

    @Override
    public List<SysTimbre> getTimbreList() {
        return timbreMapper.select(null,null);
    }

    @Override
    public List<SysTimbre> getTimbreList(String provider, String value) {
        return timbreMapper.select(provider,value);
    }

    @Override
    public SysTimbre getTimebreById(Integer defaultTimbreId) {
        return timbreMapper.selectById(defaultTimbreId);
    }

}
