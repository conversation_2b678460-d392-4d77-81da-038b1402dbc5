package cn.naii.iot.service.impl;


import cn.naii.iot.dao.TimberMapper;
import cn.naii.iot.dto.SysTimbreDTO;
import cn.naii.iot.service.SysTimbreService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysTimbreServiceImpl implements SysTimbreService {


    @Resource
    private TimberMapper timberMapper;

    @Override
    public List<SysTimbreDTO> getTimbreList(Integer modelId) {
        return timberMapper.selectTimbreById(modelId);
    }
}
