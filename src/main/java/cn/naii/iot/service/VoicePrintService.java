package cn.naii.iot.service;

import cn.naii.iot.dao.VoicePrintMapper;
import cn.naii.iot.entity.LocationCoordinate;
import cn.naii.iot.entity.VoicePrint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VoicePrintService {

    @Autowired
    private VoicePrintMapper voicePrintMapper;

    @Autowired
    private LocationCoordinateService locationCoordinateService;

    /**
     * 插入用户声纹信息
     *
     * @param voicePrint 用户声纹实体对象
     * @return 影响的行数
     */
    public int insert(VoicePrint voicePrint) {
        return voicePrintMapper.insert(voicePrint);
    }

    /**
     * 根据ID删除用户声纹信息
     *
     * @param id 用户声纹ID
     * @return 影响的行数
     */
    public int deleteById(Long id) {
        return voicePrintMapper.deleteById(id);
    }

    /**
     * 根据ID更新用户声纹信息
     *
     * @param voicePrint 用户声纹实体对象
     * @return 影响的行数
     */
    public int updateById(VoicePrint voicePrint) {
        return voicePrintMapper.updateById(voicePrint);
    }

    /**
     * 根据ID查询用户声纹信息
     *
     * @param id 用户声纹ID
     * @return 用户声纹实体对象
     */
    public VoicePrint selectById(Long id) {
        VoicePrint voicePrint = voicePrintMapper.selectById(id);
        if (voicePrint != null) {
            fillLocationInfo(voicePrint);
        }
        return voicePrint;
    }

    /**
     * 查询所有用户声纹信息
     *
     * @return 用户声纹实体对象列表
     */
    public List<VoicePrint> selectAll() {
        List<VoicePrint> voicePrints = voicePrintMapper.selectAll();
        fillLocationInfoForList(voicePrints);
        return voicePrints;
    }

    /**
     * 条件查询用户声纹信息
     *
     * @param voicePrint 查询条件对象
     * @return 用户声纹实体对象列表
     */
    public List<VoicePrint> selectByCondition(VoicePrint voicePrint) {
        List<VoicePrint> result = voicePrintMapper.selectByCondition(voicePrint);
        fillLocationInfoForList(result);
        return result;
    }

    /**
     * 分页查询用户声纹信息
     *
     * @param page 页码
     * @param limit 每页数量
     * @param voicePrint 查询条件对象
     * @return 用户声纹实体对象列表
     */
    public List<VoicePrint> selectPage(int page, int limit, VoicePrint voicePrint) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", (page - 1) * limit);
        params.put("limit", limit);
        params.put("voicePrint", voicePrint);
        List<VoicePrint> result = voicePrintMapper.selectPage(params);
        fillLocationInfoForList(result);
        return result;
    }

    /**
     * 统计符合条件的用户声纹信息数量
     *
     * @param voicePrint 查询条件对象
     * @return 记录总数
     */
    public int countByCondition(VoicePrint voicePrint) {
        return voicePrintMapper.countByCondition(voicePrint);
    }

    /**
     * 填充单个VoicePrint对象的location信息
     *
     * @param voicePrint VoicePrint对象
     */
    private void fillLocationInfo(VoicePrint voicePrint) {
        if (voicePrint.getBirthPlace() != null) {
            LocationCoordinate location = locationCoordinateService.findById(voicePrint.getBirthPlace());
            voicePrint.setLocation(location);
        }
    }

    /**
     * 批量填充VoicePrint对象列表的location信息
     *
     * @param voicePrints VoicePrint对象列表
     */
    private void fillLocationInfoForList(List<VoicePrint> voicePrints) {
        if (voicePrints != null && !voicePrints.isEmpty()) {
            voicePrints.forEach(this::fillLocationInfo);
        }
    }
}
