package cn.naii.iot.service;

import cn.naii.iot.common.web.PageFilter;
import cn.naii.iot.entity.SysConfig;

import java.util.List;

/**
 * 配置
 * 
 * <AUTHOR>
 * 
 */
public interface SysConfigService {

  /**
   * 添加配置
   * 
   * @param config
   * @return
   */
  int add(SysConfig config);

  /**
   * 修改配置
   * 
   * @param config
   * @return
   */
  int update(SysConfig config);

  /**
   * 查询
   * 
   * @param config;
   * @return
   */
  List<SysConfig> query(SysConfig config, PageFilter pageFilter);

  /**
   * 查询配置
   * 
   * @param configId;
   * @return
   */
  SysConfig selectConfigById(Integer configId);

  /**
   * 查询默认配置
   */
  SysConfig selectModelType(String modelType);
}