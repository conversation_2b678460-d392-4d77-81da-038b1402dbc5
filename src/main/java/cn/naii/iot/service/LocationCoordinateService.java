package cn.naii.iot.service;

import cn.naii.iot.dao.LocationCoordinateMapper;
import cn.naii.iot.entity.LocationCoordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地理位置坐标服务类
 */
@Service
public class LocationCoordinateService {
    
    @Autowired
    private LocationCoordinateMapper locationCoordinateMapper;
    
    /**
     * 查询所有记录
     */
    public List<LocationCoordinate> findAll() {
        return locationCoordinateMapper.selectAll();
    }
    
    /**
     * 根据ID查询
     */
    public LocationCoordinate findById(Long id) {
        return locationCoordinateMapper.selectById(id);
    }
    
    /**
     * 根据条件查询
     */
    public List<LocationCoordinate> findByCondition(LocationCoordinate locationCoordinate) {
        return locationCoordinateMapper.selectByCondition(locationCoordinate);
    }
    
    /**
     * 插入记录
     */
    public boolean insert(LocationCoordinate locationCoordinate) {
        return locationCoordinateMapper.insert(locationCoordinate) > 0;
    }
    
    /**
     * 更新记录
     */
    public boolean update(LocationCoordinate locationCoordinate) {
        return locationCoordinateMapper.update(locationCoordinate) > 0;
    }
    
    /**
     * 删除记录
     */
    public boolean deleteById(Long id) {
        return locationCoordinateMapper.deleteById(id) > 0;
    }
}