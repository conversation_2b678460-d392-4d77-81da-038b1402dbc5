package cn.naii.iot.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "用户声纹信息")
public class VoicePrint extends Base<VoicePrint> {

    /**
     * 用户声纹ID，主键自增
     */
    @Schema(description = "用户声纹ID")
    private Long id;

    /**
     * 小程序用户表主键id
     */
    @Schema(description = "小程序用户表主键id")
    private Long wxUserId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 出生年月日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "出生年月日")
    private Date birth;

    /**
     * 出生地，存储的是经纬度管理表主键id
     */
    @Schema(description = "出生地ID")
    private Long birthPlace;

    /**
     * 声纹标识
     */
    @Schema(description = "声纹标识")
    private String voicePrint;

    /**
     * 性别：0未知，1男，2女
     */
    @Schema(description = "性别：0未知，1男，2女")
    private Integer gender;

    /**
     * 状态：0正常，1禁用
     */
    @Schema(description = "状态：0正常，1禁用")
    private Integer status;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 最后登录时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后登录时间")
    private Date lastLoginTime;


    @Schema(description = "位置坐标信息")
    private LocationCoordinate location;


}
