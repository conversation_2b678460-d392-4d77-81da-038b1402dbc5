package cn.naii.iot.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SysTimbre {


    @Schema(description = "语音ID")
    private Integer id;

    @Schema(description = "语音标签")
    private String label;

    @Schema(description = "语音内容")
    private String value;

    @Schema(description = "语音提供商")
    private String provider;

    @Schema(description = "语音性别")
    private String gender;

    @Schema(description = "语音模型id")
    private String modelId;

    @Schema(description = "火山引擎speakerId")
    private String speakerId;
}
