package cn.naii.iot.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 地理位置坐标实体类
 */
@Data
public class LocationCoordinate {
    
    /**
     * 经纬度ID，主键自增
     */
    private Long id;
    
    /**
     * 省份（直辖市,自治区）
     */
    private String province;
    
    /**
     * 城市（自治州）
     */
    private String city;
    
    /**
     * 自治县、县级市
     */
    private String county;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    @Override
    public String toString() {
        return "LocationCoordinate{" +
                "id=" + id +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", county='" + county + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", createTime=" + createTime +
                '}';
    }
}
