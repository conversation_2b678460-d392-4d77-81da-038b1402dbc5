package cn.naii.iot.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 简单的限流器实现
 * 使用滑动窗口算法限制请求频率
 */
public class RateLimiter {
    private static final Logger logger = LoggerFactory.getLogger(RateLimiter.class);

    private final int maxRequests;           // 最大请求数
    private final long windowMs;             // 时间窗口（毫秒）
    private final String name;               // 限流器名称
    
    private final long[] requestTimestamps;  // 请求时间戳数组
    private int currentIndex = 0;            // 当前索引
    private final Lock lock = new ReentrantLock();
    
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong blockedRequests = new AtomicLong(0);

    /**
     * 创建限流器
     * 
     * @param maxRequests 时间窗口内最大请求数
     * @param windowMs 时间窗口（毫秒）
     * @param name 限流器名称
     */
    public RateLimiter(int maxRequests, long windowMs, String name) {
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
        this.name = name;
        this.requestTimestamps = new long[maxRequests];
    }

    /**
     * 创建限流器（默认名称）
     * 
     * @param maxRequests 时间窗口内最大请求数
     * @param windowMs 时间窗口（毫秒）
     */
    public RateLimiter(int maxRequests, long windowMs) {
        this(maxRequests, windowMs, "RateLimiter");
    }

    /**
     * 尝试获取许可
     * 如果超过限流阈值，会阻塞等待直到可以执行
     */
    public void acquire() {
        lock.lock();
        try {
            totalRequests.incrementAndGet();
            
            long now = System.currentTimeMillis();
            long oldestAllowedTime = now - windowMs;
            
            // 获取最旧的请求时间戳
            long oldestRequestTime = requestTimestamps[currentIndex];
            
            // 如果最旧的请求还在时间窗口内，说明已达到限流阈值，需要等待
            if (oldestRequestTime > oldestAllowedTime) {
                long waitTime = oldestRequestTime - oldestAllowedTime;
                blockedRequests.incrementAndGet();
                
                logger.warn("⏱️ {} - 触发限流，需要等待 {} ms (当前窗口已有 {} 个请求)", 
                    name, waitTime, maxRequests);
                
                try {
                    TimeUnit.MILLISECONDS.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(name + " - 等待被中断", e);
                }
            }
            
            // 记录当前请求时间戳
            requestTimestamps[currentIndex] = System.currentTimeMillis();
            currentIndex = (currentIndex + 1) % maxRequests;
            
            if (totalRequests.get() % 10 == 0) {
                logger.debug("📊 {} - 统计: 总请求={}, 被限流={}, 限流率={:.2f}%", 
                    name, 
                    totalRequests.get(), 
                    blockedRequests.get(),
                    blockedRequests.get() * 100.0 / totalRequests.get());
            }
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 尝试获取许可（非阻塞）
     *
     * @return true 如果获取成功，false 如果超过限流阈值
     */
    public boolean tryAcquire() {
        lock.lock();
        try {
            totalRequests.incrementAndGet();

            long now = System.currentTimeMillis();
            long oldestAllowedTime = now - windowMs;

            // 获取最旧的请求时间戳
            long oldestRequestTime = requestTimestamps[currentIndex];

            // 如果最旧的请求还在时间窗口内，说明已达到限流阈值
            if (oldestRequestTime > oldestAllowedTime) {
                blockedRequests.incrementAndGet();
                logger.warn("⏱️ {} - 触发限流，拒绝请求 (当前窗口已有 {} 个请求)",
                    name, maxRequests);
                return false;
            }

            // 记录当前请求时间戳
            requestTimestamps[currentIndex] = System.currentTimeMillis();
            currentIndex = (currentIndex + 1) % maxRequests;

            return true;

        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取需要等待的时间（毫秒）
     *
     * @return 需要等待的毫秒数，如果不需要等待则返回0
     */
    public long getWaitTimeMs() {
        lock.lock();
        try {
            long now = System.currentTimeMillis();
            long oldestAllowedTime = now - windowMs;

            // 获取最旧的请求时间戳
            long oldestRequestTime = requestTimestamps[currentIndex];

            // 如果最旧的请求还在时间窗口内，说明已达到限流阈值
            if (oldestRequestTime > oldestAllowedTime) {
                return oldestRequestTime - oldestAllowedTime;
            }

            return 0;

        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取统计信息
     */
    public String getStats() {
        return String.format("%s - 总请求: %d, 被限流: %d, 限流率: %.2f%%",
            name,
            totalRequests.get(),
            blockedRequests.get(),
            blockedRequests.get() * 100.0 / Math.max(1, totalRequests.get()));
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalRequests.set(0);
        blockedRequests.set(0);
    }
}

