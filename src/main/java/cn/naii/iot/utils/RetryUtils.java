package cn.naii.iot.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * 通用重试工具类
 * 提供灵活的重试机制，支持自定义重试策略
 */
public class RetryUtils {
    private static final Logger logger = LoggerFactory.getLogger(RetryUtils.class);

    /**
     * 重试配置
     */
    public static class RetryConfig {
        private final int maxAttempts;
        private final long retryDelayMs;
        private final String operationName;
        private final Predicate<Exception> retryOnException;

        private RetryConfig(Builder builder) {
            this.maxAttempts = builder.maxAttempts;
            this.retryDelayMs = builder.retryDelayMs;
            this.operationName = builder.operationName;
            this.retryOnException = builder.retryOnException;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private int maxAttempts = 3;
            private long retryDelayMs = 1000;
            private String operationName = "操作";
            private Predicate<Exception> retryOnException = e -> true;

            public Builder maxAttempts(int maxAttempts) {
                this.maxAttempts = maxAttempts;
                return this;
            }

            public Builder retryDelayMs(long retryDelayMs) {
                this.retryDelayMs = retryDelayMs;
                return this;
            }

            public Builder operationName(String operationName) {
                this.operationName = operationName;
                return this;
            }

            public Builder retryOnException(Predicate<Exception> retryOnException) {
                this.retryOnException = retryOnException;
                return this;
            }

            public RetryConfig build() {
                return new RetryConfig(this);
            }
        }
    }

    /**
     * 执行带重试的操作
     *
     * @param callable 要执行的操作
     * @param config 重试配置
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Callable<T> callable, RetryConfig config) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= config.maxAttempts; attempt++) {
            try {
                return callable.call();
            } catch (Exception e) {
                lastException = e;
                
                // 检查是否应该重试
                if (!config.retryOnException.test(e)) {
                    logger.error("{} - 遇到不可重试的异常", config.operationName, e);
                    throw e;
                }
                
                // 如果还有重试机会
                if (attempt < config.maxAttempts) {
                    logger.warn("{} - 失败，正在重试 ({}/{}): {}", 
                        config.operationName, attempt, config.maxAttempts, e.getMessage());
                    
                    try {
                        TimeUnit.MILLISECONDS.sleep(config.retryDelayMs);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("{} - 重试等待被中断", config.operationName, ie);
                        throw new Exception(config.operationName + " - 重试等待被中断", ie);
                    }
                } else {
                    logger.error("{} - 已达到最大重试次数 ({})", config.operationName, config.maxAttempts, e);
                }
            }
        }
        
        throw new Exception(config.operationName + " - 失败，已重试 " + config.maxAttempts + " 次", lastException);
    }

    /**
     * 执行带重试的操作（返回 null 表示失败，不抛出异常）
     *
     * @param callable 要执行的操作
     * @param config 重试配置
     * @param <T> 返回值类型
     * @return 操作结果，失败返回 null
     */
    public static <T> T executeWithRetryOrNull(Callable<T> callable, RetryConfig config) {
        try {
            return executeWithRetry(callable, config);
        } catch (Exception e) {
            logger.error("{} - 最终失败", config.operationName, e);
            return null;
        }
    }

    /**
     * 执行带重试的操作（使用默认配置）
     *
     * @param callable 要执行的操作
     * @param operationName 操作名称
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Callable<T> callable, String operationName) throws Exception {
        RetryConfig config = RetryConfig.builder()
                .operationName(operationName)
                .build();
        return executeWithRetry(callable, config);
    }

    /**
     * 执行带重试的操作（使用默认配置，返回 null 表示失败）
     *
     * @param callable 要执行的操作
     * @param operationName 操作名称
     * @param <T> 返回值类型
     * @return 操作结果，失败返回 null
     */
    public static <T> T executeWithRetryOrNull(Callable<T> callable, String operationName) {
        try {
            return executeWithRetry(callable, operationName);
        } catch (Exception e) {
            logger.error("{} - 最终失败", operationName, e);
            return null;
        }
    }
}

