package cn.naii.iot.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HTTP请求工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES))
            .build();

    /**
     * 发送HTTP请求
     *
     * @param url        请求URL
     * @param method     请求方法
     * @param requestDto 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject sendRequest(String url, String method, JSONObject requestDto) throws IOException {
        return sendRequest(url, method, requestDto, null);
    }

    public static JSONObject sendRequest(String url, String method, JSONObject requestDto, Map<String, String> headers) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(requestDto));

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .method(method, body)
                .addHeader("Content-Type", "application/json");
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try (Response response = client.newCall(requestBuilder.build()).execute()) {
            ResponseBody responseBody = response.body();
            if (!response.isSuccessful()) {
                if (responseBody != null) {
                    String responseString = responseBody.string();
                    JSONObject result = handleResponseString(responseString);
                    String message = result.getString("message");
                    log.error("HTTP请求失败: {}, 响应内容: {}", response.code(), result);
                    throw new RuntimeException(message);
                }
                log.error("HTTP请求失败: {}", response.code());
                throw new IOException("HTTP请求失败，状态码: " + response.code());
            }
            return handleResponse(response);
        }
    }

    public static JSONObject sendRequestFromData(String url, String method, JSONObject requestDto, Map<String, String> headers, MultipartFile file) throws IOException {
        // 准备JSON部分的请求体
        MediaType jsonMediaType = MediaType.parse("application/json");
        // 构建Multipart请求体
        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);
        // 构建请求
        Request.Builder requestBuilder = new Request.Builder();
        // 添加文件部分（如果存在）
        if (file != null && !file.isEmpty()) {
            // 获取文件名和类型
            String fileName = file.getOriginalFilename();
            String fileContentType = file.getContentType();

            // 处理可能的空ContentType
            MediaType fileMediaType = fileContentType != null ?
                    MediaType.parse(fileContentType) :
                    MediaType.parse("application/octet-stream");

            // 创建文件请求体

            RequestBody fileRequestBody = RequestBody.create(fileMediaType, file.getBytes());
            multipartBuilder.addFormDataPart("file", fileName, fileRequestBody);
        }
        if (requestDto != null) {
            RequestBody jsonBody = RequestBody.create(jsonMediaType, JSON.toJSONString(requestDto));
            multipartBuilder.addFormDataPart("data", null, jsonBody);
            RequestBody requestBody = multipartBuilder.build();
            requestBuilder.url(url).method(method, requestBody);

        } else {
            // 构建URL并添加查询参数
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            urlBuilder.addQueryParameter("source", "datasets");
            RequestBody requestBody = multipartBuilder.build();
            requestBuilder.url(urlBuilder.build().toString()).method(method, requestBody);
        }


        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        try (Response response = client.newCall(requestBuilder.build()).execute()) {
            return handleResponse(response);
        }
    }


    /**
     * 发送带参数的GET请求
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject get(String url, Map<String, Object> params) throws IOException {
        return get(url, null, params);
    }

    /**
     * 发送带请求头和参数的GET请求
     *
     * @param url     请求URL
     * @param headers 请求头
     * @param params  请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject get(String url, Map<String, String> headers, Map<String, Object> params) throws IOException {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                String value = String.valueOf(entry.getValue());
                // 判断是否是已编码的URL字符串（如含有 %20、%3A 等）
                if (value.matches(".*%(20|21|22|23|24|25|26|27|28|29|2A|2B|2C|2F|3A|3B|3C|3D|3E|3F|40|5B|5C|5D|5E|5F|60|7B|7C|7D|7E).*")) {
                    urlBuilder.addEncodedQueryParameter(entry.getKey(), value);
                } else {
                    urlBuilder.addQueryParameter(entry.getKey(), value);
                }
            }
        }

        Request.Builder requestBuilder = new Request.Builder()
                .url(urlBuilder.build())
                .get();

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        try (Response response = client.newCall(requestBuilder.build()).execute()) {
            return handleResponse(response);
        }
    }

    /**
     * 发送POST请求
     *
     * @param url        请求URL
     * @param requestDto 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject post(String url, JSONObject requestDto) throws IOException {
        return sendRequest(url, "POST", requestDto);
    }

    public static JSONObject post(String url, JSONObject requestDto, Map<String, String> headers) throws IOException {
        return sendRequest(url, "POST", requestDto, headers);
    }

    public static JSONObject post(String url, JSONObject requestDto, Map<String, String> headers, MultipartFile file) throws IOException {
        return sendRequestFromData(url, "POST", requestDto, headers, file);
    }

    /**
     * 发送PUT请求
     *
     * @param url        请求URL
     * @param requestDto 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject put(String url, JSONObject requestDto) throws IOException {
        return sendRequest(url, "PUT", requestDto);
    }

    public static JSONObject put(String url, JSONObject requestDto, Map<String, String> headers) throws IOException {
        return sendRequest(url, "PUT", requestDto, headers);
    }

    /**
     * 发送DELETE请求
     *
     * @param url        请求URL
     * @param requestDto 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject delete(String url, JSONObject requestDto) throws IOException {
        return sendRequest(url, "DELETE", requestDto);
    }

    public static JSONObject delete(String url, JSONObject requestDto, Map<String, String> headers) throws IOException {
        return sendRequest(url, "DELETE", requestDto, headers);
    }

    /**
     * 发送PATCH请求
     *
     * @param url        请求URL
     * @param requestDto 请求参数
     * @return 响应数据
     * @throws IOException IO异常
     */
    public static JSONObject patch(String url, JSONObject requestDto) throws IOException {
        return sendRequest(url, "PATCH", requestDto);
    }

    public static JSONObject patch(String url, JSONObject requestDto, Map<String, String> headers) throws IOException {
        return sendRequest(url, "PATCH", requestDto, headers);
    }

    /**
     * 处理HTTP响应
     *
     * @param response HTTP响应
     * @return 响应数据
     * @throws IOException IO异常
     */
    private static JSONObject handleResponse(Response response) throws IOException {
        if (!response.isSuccessful()) {
            log.error("HTTP请求失败: {}", response.code());
            throw new IOException("HTTP请求失败，状态码: " + response.code());
        }

        ResponseBody responseBody = response.body();
        if (responseBody == null) {
            log.error("HTTP请求返回空响应");
            throw new IOException("HTTP请求返回空响应");
        }

        return handleResponseString(responseBody.string());
    }

    private static JSONObject handleResponseString(String responseString) {
        // 判断响应是否为JSONArray格式
        if (responseString.trim().startsWith("[")) {
            JSONObject wrapper = new JSONObject();
            wrapper.put("data", JSON.parseArray(responseString));
            return wrapper;
        }
        try {
            return JSON.parseObject(responseString);
        } catch (Exception e) {
            // 如果不是有效的JSON对象，则将其作为message字段返回
            JSONObject wrapper = new JSONObject();
            wrapper.put("message", responseString);
            return wrapper;
        }
    }

    /**
     * 执行带认证的HTTP请求
     *
     * @param method    HTTP方法
     * @param prefixUrl 基础URL
     * @param path      请求路径
     * @param body      请求体
     * @param params    查询参数
     * @param token     认证令牌
     * @return 响应结果
     */
    public static JSONObject executeAuthRequest(String method, String prefixUrl, String path, Object body, Map<String, Object> params, String token, MultipartFile file) {
        String url = prefixUrl.concat(path);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + token);

        try {
            String logBody = body != null ? JSONObject.toJSONString(body) : "";
            String logParams = params != null ? JSONObject.toJSONString(params) : "";
            log.info("请求URL：{}，body:{}，params:{}，headers：{}", url, logBody, logParams, headers);

            JSONObject response;
            switch (method.toUpperCase()) {
                case "GET":
                    response = get(url, headers, params);
                    break;
                case "POST":
                    response = post(url, body instanceof JSONObject ? (JSONObject) body : JSONObject.from(body), headers);
                    break;
                case "DELETE":
                    response = delete(url, body instanceof JSONObject ? (JSONObject) body : JSONObject.from(body), headers);
                    break;
                case "PATCH":
                    response = patch(url, body instanceof JSONObject ? (JSONObject) body : JSONObject.from(body), headers);
                    break;
                case "PUT":
                    response = put(url, body instanceof JSONObject ? (JSONObject) body : JSONObject.from(body), headers);
                    break;
                case "POSTFORMDATA":
                    response = post(url, body instanceof JSONObject ? (JSONObject) body : JSONObject.from(body), headers, file);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported HTTP method: " + method);
            }

            log.debug("调用{}，返回结果：{}", path, response);
            return response;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static JSONObject executeAuthRequest(String method, String prefixUrl, String path, Object body, Map<String, Object> params, String token) {
        return executeAuthRequest(method, prefixUrl, path, body, params, token, null);
    }
}
