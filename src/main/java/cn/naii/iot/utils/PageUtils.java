package cn.naii.iot.utils;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 * 用于处理 PageHelper 分页数据转换，保持分页信息完整性
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class PageUtils {
    
    /**
     * 转换 PageInfo 数据类型，保持分页信息不变
     * 
     * @param originalPageInfo 原始分页信息
     * @param converter 数据转换函数
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的分页信息
     */
    public static <T, R> PageInfo<R> convertPageInfo(PageInfo<T> originalPageInfo, Function<T, R> converter) {
        if (originalPageInfo == null) {
            return new PageInfo<>();
        }
        
        // 转换数据列表
        List<R> convertedList = originalPageInfo.getList().stream()
                .map(converter)
                .collect(Collectors.toList());
        
        // 创建新的 PageInfo
        PageInfo<R> newPageInfo = new PageInfo<>(convertedList);
        
        // 复制分页信息
        copyPageInfo(originalPageInfo, newPageInfo);
        
        return newPageInfo;
    }
    
    /**
     * 转换 PageInfo 数据类型，使用 BeanUtils 进行属性复制
     * 
     * @param originalPageInfo 原始分页信息
     * @param targetClass 目标类型
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的分页信息
     */
    public static <T, R> PageInfo<R> convertPageInfo(PageInfo<T> originalPageInfo, Class<R> targetClass) {
        return convertPageInfo(originalPageInfo, source -> {
            try {
                R target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                return target;
            } catch (Exception e) {
                throw new RuntimeException("转换对象失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 从 List 创建 PageInfo，保持原有分页信息
     * 
     * @param originalList 原始数据列表（必须是 PageHelper 查询结果）
     * @param converter 数据转换函数
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的分页信息
     */
    public static <T, R> PageInfo<R> convertFromList(List<T> originalList, Function<T, R> converter) {
        if (originalList == null) {
            return new PageInfo<>();
        }
        
        // 先创建原始数据的 PageInfo
        PageInfo<T> originalPageInfo = new PageInfo<>(originalList);
        
        // 转换数据
        return convertPageInfo(originalPageInfo, converter);
    }
    
    /**
     * 从 List 创建 PageInfo，使用 BeanUtils 进行属性复制
     * 
     * @param originalList 原始数据列表（必须是 PageHelper 查询结果）
     * @param targetClass 目标类型
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的分页信息
     */
    public static <T, R> PageInfo<R> convertFromList(List<T> originalList, Class<R> targetClass) {
        return convertFromList(originalList, source -> {
            try {
                R target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                return target;
            } catch (Exception e) {
                throw new RuntimeException("转换对象失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 复制分页信息
     * 
     * @param source 源分页信息
     * @param target 目标分页信息
     */
    private static void copyPageInfo(PageInfo<?> source, PageInfo<?> target) {
        target.setTotal(source.getTotal());                           // 总记录数
        target.setPages(source.getPages());                           // 总页数
        target.setPageNum(source.getPageNum());                       // 当前页
        target.setPageSize(source.getPageSize());                     // 每页大小
        target.setStartRow(source.getStartRow());                     // 起始行
        target.setEndRow(source.getEndRow());                         // 结束行
        target.setHasNextPage(source.isHasNextPage());                // 是否有下一页
        target.setHasPreviousPage(source.isHasPreviousPage());        // 是否有上一页
        target.setIsFirstPage(source.isIsFirstPage());                // 是否首页
        target.setIsLastPage(source.isIsLastPage());                  // 是否末页
        target.setNavigatePages(source.getNavigatePages());           // 导航页数
        target.setNavigatepageNums(source.getNavigatepageNums());     // 导航页码
        target.setNavigateFirstPage(source.getNavigateFirstPage());   // 导航首页
        target.setNavigateLastPage(source.getNavigateLastPage());     // 导航末页
        target.setPrePage(source.getPrePage());                       // 前一页
        target.setNextPage(source.getNextPage());                     // 后一页
    }
    
    /**
     * 创建空的分页信息
     * 
     * @param <T> 数据类型
     * @return 空的分页信息
     */
    public static <T> PageInfo<T> emptyPageInfo() {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setTotal(0);
        pageInfo.setPages(0);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        pageInfo.setStartRow(0);
        pageInfo.setEndRow(0);
        pageInfo.setHasNextPage(false);
        pageInfo.setHasPreviousPage(false);
        pageInfo.setIsFirstPage(true);
        pageInfo.setIsLastPage(true);
        return pageInfo;
    }
    
    /**
     * 打印分页信息（用于调试）
     * 
     * @param pageInfo 分页信息
     * @param title 标题
     */
    public static void printPageInfo(PageInfo<?> pageInfo, String title) {
        if (pageInfo == null) {
            System.out.println(title + ": null");
            return;
        }
        
        System.out.println("=== " + title + " ===");
        System.out.println("总记录数: " + pageInfo.getTotal());
        System.out.println("总页数: " + pageInfo.getPages());
        System.out.println("当前页: " + pageInfo.getPageNum());
        System.out.println("每页大小: " + pageInfo.getPageSize());
        System.out.println("起始行: " + pageInfo.getStartRow());
        System.out.println("结束行: " + pageInfo.getEndRow());
        System.out.println("是否有下一页: " + pageInfo.isHasNextPage());
        System.out.println("是否有上一页: " + pageInfo.isHasPreviousPage());
        System.out.println("是否首页: " + pageInfo.isIsFirstPage());
        System.out.println("是否末页: " + pageInfo.isIsLastPage());
        System.out.println("当前页数据量: " + (pageInfo.getList() != null ? pageInfo.getList().size() : 0));
        System.out.println("========================");
    }
    
    /**
     * 验证分页参数
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param maxPageSize 最大每页大小
     * @return 验证后的分页参数数组 [pageNum, pageSize]
     */
    public static int[] validatePageParams(Integer pageNum, Integer pageSize, int maxPageSize) {
        // 默认值
        int validPageNum = (pageNum != null && pageNum > 0) ? pageNum : 1;
        int validPageSize = (pageSize != null && pageSize > 0) ? pageSize : 10;
        
        // 限制最大页面大小
        validPageSize = Math.min(validPageSize, maxPageSize);
        
        return new int[]{validPageNum, validPageSize};
    }
    
    /**
     * 计算总页数
     * 
     * @param total 总记录数
     * @param pageSize 每页大小
     * @return 总页数
     */
    public static int calculateTotalPages(long total, int pageSize) {
        if (pageSize <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) total / pageSize);
    }
    
    /**
     * 计算起始行号
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 起始行号（从1开始）
     */
    public static int calculateStartRow(int pageNum, int pageSize) {
        return (pageNum - 1) * pageSize + 1;
    }
    
    /**
     * 计算结束行号
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param total 总记录数
     * @return 结束行号
     */
    public static int calculateEndRow(int pageNum, int pageSize, long total) {
        int startRow = calculateStartRow(pageNum, pageSize);
        int endRow = startRow + pageSize - 1;
        return (int) Math.min(endRow, total);
    }
}
