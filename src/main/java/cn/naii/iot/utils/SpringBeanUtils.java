package cn.naii.iot.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring Bean 静态获取工具类
 * 支持通过 Bean 类型、Bean 名称+类型获取 Spring 容器中的 Bean
 */
@Component
public class SpringBeanUtils implements ApplicationContextAware {

    /**
     * 静态持有 Spring 应用上下文实例（线程安全）
     */
    private static ApplicationContext applicationContext;

    /**
     * Spring 容器初始化时自动注入 ApplicationContext
     * 注：此方法由 Spring 调用，无需手动调用
     */
    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        if (SpringBeanUtils.applicationContext == null) {
            SpringBeanUtils.applicationContext = context;
        }
    }

    /**
     * 获取 Spring 应用上下文实例
     * @return ApplicationContext 实例
     * @throws IllegalStateException 若上下文未初始化
     */
    public static ApplicationContext getApplicationContext() {
        checkApplicationContextInitialized();
        return applicationContext;
    }

    /**
     * 根据 Bean 类型获取 Bean 实例（适用于单实例 Bean）
     * @param beanClass Bean 的类类型（如 UserService.class）
     * @param <T>       Bean 类型泛型
     * @return 对应的 Bean 实例
     * @throws BeansException        若获取 Bean 失败（如类型不存在、存在多个实例）
     * @throws IllegalStateException 若上下文未初始化
     */
    public static <T> T getBean(Class<T> beanClass) {
        checkApplicationContextInitialized();
        try {
            return applicationContext.getBean(beanClass);
        } catch (BeansException e) {
            throw new BeansException("获取 Bean 失败，Bean 类型：" + beanClass.getName(), e) {};
        }
    }

    /**
     * 根据 Bean 名称和类型获取 Bean 实例（适用于同类型多实例 Bean）
     * @param beanName  Bean 的名称（如 @Bean 注解的 name 属性、类名首字母小写，例：userService）
     * @param beanClass Bean 的类类型
     * @param <T>       Bean 类型泛型
     * @return 对应的 Bean 实例
     * @throws BeansException        若获取 Bean 失败（如名称不存在、类型不匹配）
     * @throws IllegalStateException 若上下文未初始化
     */
    public static <T> T getBean(String beanName, Class<T> beanClass) {
        checkApplicationContextInitialized();
        if (beanName == null || beanName.trim().isEmpty()) {
            throw new IllegalArgumentException("Bean 名称不能为空");
        }
        try {
            return applicationContext.getBean(beanName, beanClass);
        } catch (BeansException e) {
            throw new BeansException("获取 Bean 失败，Bean 名称：" + beanName + "，Bean 类型：" + beanClass.getName(), e) {};
        }
    }

    /**
     * 检查 ApplicationContext 是否已初始化
     * 若未初始化，抛出 IllegalStateException
     */
    private static void checkApplicationContextInitialized() {
        if (applicationContext == null) {
            throw new IllegalStateException("Spring ApplicationContext 未初始化！" +
                    "请确认：1. 工具类已被 @Component 注解 2. Spring 扫描路径包含该类 3. 调用时机在 Spring 容器启动后");
        }
    }
}