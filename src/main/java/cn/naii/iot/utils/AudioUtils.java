package cn.naii.iot.utils;

import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FrameRecorder;
import org.slf4j.Logger;

import java.io.DataOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AudioUtils {
    public static final String AUDIO_PATH = "audio/";
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(AudioUtils.class);
    public static final int FRAME_SIZE = 960;
    public static final int SAMPLE_RATE = 16000; // 采样率
    public static final int CHANNELS = 1; // 单声道
    public static final int BITRATE = 16000; // 16kbps比特率
    public static final int SAMPLE_FORMAT = avutil.AV_SAMPLE_FMT_S16; // 16位PCM
    public static final int OPUS_FRAME_DURATION_MS = 60; // OPUS帧持续时间（毫秒）

    /**
     * 将原始音频数据保存为MP3文件
     *
     * @param audio PCM音频数据
     * @return 文件名
     */
    public static String saveAsMp3(byte[] audio) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".mp3";
        String filePath = AUDIO_PATH + fileName;

        // 创建临时PCM文件
        String tempPcmPath = AUDIO_PATH + uuid + ".pcm";

        try {
            // 确保音频目录存在
            Files.createDirectories(Paths.get(AUDIO_PATH));

            // 先将PCM数据写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempPcmPath)) {
                fos.write(audio);
            }

            // 构建ffmpeg命令：将PCM转换为MP3
            String[] command = {
                    "ffmpeg",
                    "-f", "s16le", // 输入格式：16位有符号小端序PCM
                    "-ar", String.valueOf(SAMPLE_RATE), // 采样率
                    "-ac", String.valueOf(CHANNELS), // 声道数
                    "-i", tempPcmPath, // 输入文件
                    "-b:a", String.valueOf(BITRATE), // 比特率
                    "-f", "mp3", // 输出格式
                    "-q:a", "0", // 最高质量
                    filePath // 输出文件
            };

            // 执行命令
            Process process = Runtime.getRuntime().exec(command);

            // 读取错误输出以便调试
            StringBuilder errorOutput = new StringBuilder();
            try (InputStream errorStream = process.getErrorStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = errorStream.read(buffer)) != -1) {
                    errorOutput.append(new String(buffer, 0, bytesRead));
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                logger.error("ffmpeg转换失败，退出代码: {}，错误信息: {}", exitCode, errorOutput.toString());
                return null;
            }

            // 检查输出文件是否存在
            if (!Files.exists(Paths.get(filePath))) {
                logger.error("ffmpeg转换后的MP3文件不存在");
                return null;
            }

            return fileName;
        } catch (IOException | InterruptedException e) {
            logger.error("保存MP3文件时发生错误", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            return null;
        } finally {
            // 删除临时PCM文件
            try {
                Files.deleteIfExists(Paths.get(tempPcmPath));
            } catch (IOException e) {
                logger.warn("删除临时PCM文件失败", e);
            }
        }
    }

    public static String saveAsWav(byte[] audio) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ".wav";
        Path path = Path.of(AUDIO_PATH , fileName);
        saveAsWav(path, audio);
        return fileName;
    }
    /**
     * 将原始音频数据保存为WAV文件
     *
     * @param audioData 音频数据
     * @return 文件名
     */
    public static void saveAsWav(Path path, byte[] audioData) {

        // WAV文件参数
        int bitsPerSample = 16; // 16位采样

        try {
            // 确保音频目录存在
            Files.createDirectories(path.getParent());

            try (FileOutputStream fos = new FileOutputStream(path.toFile());
                 DataOutputStream dos = new DataOutputStream(fos)) {

                // 写入WAV文件头
                // RIFF头
                dos.writeBytes("RIFF");
                dos.writeInt(Integer.reverseBytes(36 + audioData.length)); // 文件长度
                dos.writeBytes("WAVE");

                // fmt子块
                dos.writeBytes("fmt ");
                dos.writeInt(Integer.reverseBytes(16)); // 子块大小
                dos.writeShort(Short.reverseBytes((short) 1)); // 音频格式 (1 = PCM)
                dos.writeShort(Short.reverseBytes((short) CHANNELS)); // 通道数
                dos.writeInt(Integer.reverseBytes(SAMPLE_RATE)); // 采样率
                dos.writeInt(Integer.reverseBytes(SAMPLE_RATE * CHANNELS * bitsPerSample / 8)); // 字节率
                dos.writeShort(Short.reverseBytes((short) (CHANNELS * bitsPerSample / 8))); // 块对齐
                dos.writeShort(Short.reverseBytes((short) bitsPerSample)); // 每个样本的位数

                // data子块
                dos.writeBytes("data");
                dos.writeInt(Integer.reverseBytes(audioData.length)); // 数据大小

                // 写入音频数据
                dos.write(audioData);
            }
        } catch (FrameRecorder.Exception e) {
            logger.error("编码WAV时发生错误", e);
        } catch (IOException e) {
            logger.error("写入WAV文件时发生错误", e);
        }
    }

    /**
     * 合并多个音频文件为一个WAV文件
     * 支持合并的格式： wav, mp3, pcm
     *
     * @param audioPaths 要合并的音频文件路径列表
     * @return 合并后的WAV文件名
     */
    public static void mergeAudioFiles(Path path, List<String> audioPaths) {
        if (audioPaths.size() == 1) {
            // TODO 考虑改为文件迁移mv 或rename
//            return Paths.get(audioPaths.getFirst()).getFileName().toString();
        }
//        var uuid = UUID.randomUUID().toString().replace("-", "");
//        var outputFileName = uuid + ".wav";
//        var outputPath = Paths.get(AUDIO_PATH, outputFileName).toString();

        try {
            // 确保音频目录存在
            Files.createDirectories(path.getParent());
            // 计算所有PCM数据的总大小
            var totalPcmSize = 0L;
            var audioChunks = new ArrayList<byte[]>();
            for (var audioPath : audioPaths) {
                var fullPath = audioPath.startsWith(AUDIO_PATH) ? audioPath : AUDIO_PATH + audioPath;
                byte[] pcmData = readAsPcm(fullPath);
                totalPcmSize += pcmData.length;
                audioChunks.add(pcmData);

            }

            // 创建输出WAV文件
            try (FileOutputStream fos = new FileOutputStream(path.toFile());
                 DataOutputStream dos = new DataOutputStream(fos)) {

                // 写入WAV文件头
                int bitsPerSample = 16; // 16位采样

                // RIFF头
                dos.writeBytes("RIFF");
                dos.writeInt(Integer.reverseBytes(36 + (int) totalPcmSize)); // 文件长度
                dos.writeBytes("WAVE");

                // fmt子块
                dos.writeBytes("fmt ");
                dos.writeInt(Integer.reverseBytes(16)); // 子块大小
                dos.writeShort(Short.reverseBytes((short) 1)); // 音频格式 (1 = PCM)
                dos.writeShort(Short.reverseBytes((short) CHANNELS)); // 通道数
                dos.writeInt(Integer.reverseBytes(SAMPLE_RATE)); // 采样率
                dos.writeInt(Integer.reverseBytes(SAMPLE_RATE * CHANNELS * bitsPerSample / 8)); // 字节率
                dos.writeShort(Short.reverseBytes((short) (CHANNELS * bitsPerSample / 8))); // 块对齐
                dos.writeShort(Short.reverseBytes((short) bitsPerSample)); // 每个样本的位数

                // data子块
                dos.writeBytes("data");
                dos.writeInt(Integer.reverseBytes((int) totalPcmSize)); // 数据大小

                // 依次写入每个文件的PCM数据
                for (var pcmData : audioChunks) {
                    dos.write(pcmData);
                }
            }
            // 目前采用的处理策略是删除已经合并了的文件。
            for (var audioPath : audioPaths) {
                var fullPath = audioPath.startsWith(AUDIO_PATH) ? audioPath : AUDIO_PATH + audioPath;
                Files.deleteIfExists(Paths.get(fullPath));
            }

        } catch (Exception e) {
            logger.error("合并音频文件时发生错误", e);
        }
    }

    /**
     * 从WAV文件中提取PCM数据
     *
     * @param wavPath WAV文件路径
     * @return PCM数据字节数组
     */
    public static byte[] wavToPcm(String wavPath) throws IOException {
        // 读取整个文件
        byte[] wavData = Files.readAllBytes(Paths.get(wavPath));
        return wavBytesToPcm(wavData);
    }

    /**
     * 从WAV字节数组中提取PCM数据（重载方法）
     *
     * @param wavBytes WAV文件的字节数组
     * @return PCM数据字节数组
     */
    public static byte[] wavToPcm(byte[] wavBytes) throws IOException {
        return wavBytesToPcm(wavBytes);
    }

    /**
     * 从WAV字节数据中提取PCM数据
     *
     * @param wavData WAV文件的字节数据
     * @return PCM数据字节数组
     */
    public static byte[] wavBytesToPcm(byte[] wavData) throws IOException {
        if (wavData == null || wavData.length < 44) { // WAV头至少44字节
            throw new IOException("无效的WAV数据");
        }

        // 检查WAV文件标识
        if (wavData[0] != 'R' || wavData[1] != 'I' || wavData[2] != 'F' || wavData[3] != 'F' ||
                wavData[8] != 'W' || wavData[9] != 'A' || wavData[10] != 'V' || wavData[11] != 'E') {
            throw new IOException("不是有效的WAV文件格式");
        }

        // 查找data子块
        int dataOffset = -1;
        for (int i = 12; i < wavData.length - 4; i++) {
            if (wavData[i] == 'd' && wavData[i + 1] == 'a' && wavData[i + 2] == 't' && wavData[i + 3] == 'a') {
                dataOffset = i + 8; // 跳过"data"和数据大小字段
                break;
            }
        }

        if (dataOffset == -1) {
            throw new IOException("在WAV文件中找不到data子块");
        }

        // 计算PCM数据大小
        int dataSize = wavData.length - dataOffset;

        // 提取PCM数据
        byte[] pcmData = new byte[dataSize];
        System.arraycopy(wavData, dataOffset, pcmData, 0, dataSize);

        return pcmData;
    }

    /**
     * 从文件读取PCM数据，自动处理WAV和MP3格式
     *
     * @param filePath 音频文件路径
     * @return PCM数据字节数组
     */
    public static byte[] readAsPcm(String filePath) throws IOException {
        if (filePath.toLowerCase().endsWith(".wav")) {
            return wavToPcm(filePath);
        } else if (filePath.toLowerCase().endsWith(".mp3")) {
            return mp3ToPcm(filePath);
        } else if (filePath.toLowerCase().endsWith(".pcm")) {
            // 直接读取PCM文件
            return Files.readAllBytes(Paths.get(filePath));
        } else {
            throw new IOException("不支持的音频格式: " + filePath);
        }
    }

    /**
     * 将MP3转换为PCM格式
     *
     * @param mp3Path MP3文件路径
     * @return PCM数据字节数组
     */
    public static byte[] mp3ToPcm(String mp3Path) throws IOException {
        return mp3ToPcmWithRetry(mp3Path, 3);
    }

    /**
     * 将MP3转换为PCM格式（带重试机制）
     *
     * @param mp3Path MP3文件路径
     * @param maxRetries 最大重试次数
     * @return PCM数据字节数组
     */
    private static byte[] mp3ToPcmWithRetry(String mp3Path, int maxRetries) throws IOException {
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return mp3ToPcmInternal(mp3Path);
            } catch (IOException e) {
                lastException = e;
                if (attempt < maxRetries) {
                    logger.warn("MP3转换失败，第{}次重试 (共{}次): {}", attempt, maxRetries, e.getMessage());
                    try {
                        // 重试前等待一小段时间，避免资源竞争
                        Thread.sleep(100 * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("重试等待被中断", ie);
                    }
                }
            }
        }

        logger.error("MP3转换失败，已重试{}次: {}", maxRetries, lastException.getMessage());
        throw lastException;
    }

    /**
     * 将MP3转换为PCM格式的内部实现
     *
     * @param mp3Path MP3文件路径
     * @return PCM数据字节数组
     */
    private static byte[] mp3ToPcmInternal(String mp3Path) throws IOException {
        String tempPcmPath = null;
        Process process = null;

        try {
            // 创建临时PCM文件
            tempPcmPath = AUDIO_PATH + UUID.randomUUID().toString().replace("-", "") + ".pcm";

            // 构建ffmpeg命令：将MP3转换为16kHz, 单声道, 16位PCM
            // 添加 -y 参数强制覆盖，-loglevel error 减少输出
            String[] command = {
                    "ffmpeg",
                    "-y", // 强制覆盖输出文件
                    "-i", mp3Path,
                    "-ar", String.valueOf(SAMPLE_RATE),
                    "-ac", String.valueOf(CHANNELS),
                    "-f", "s16le", // 16位有符号小端序PCM
                    "-loglevel", "error", // 只输出错误信息
                    tempPcmPath
            };

            // 执行命令
            process = Runtime.getRuntime().exec(command);

            // 异步读取错误输出，避免缓冲区满导致进程阻塞
            final Process finalProcess = process;
            final StringBuilder errorOutput = new StringBuilder();
            Thread errorReader = new Thread(() -> {
                try (InputStream errorStream = finalProcess.getErrorStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = errorStream.read(buffer)) != -1) {
                        errorOutput.append(new String(buffer, 0, bytesRead));
                    }
                } catch (IOException e) {
                    logger.debug("读取ffmpeg错误流时出错", e);
                }
            }, "ffmpeg-error-reader");
            errorReader.setDaemon(true);
            errorReader.start();

            // 等待进程完成，设置超时时间（30秒）
            boolean finished = process.waitFor(30, java.util.concurrent.TimeUnit.SECONDS);

            if (!finished) {
                // 超时，强制终止进程
                process.destroyForcibly();
                throw new IOException("ffmpeg转换超时（30秒），已强制终止进程");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                String errorMsg = errorOutput.length() > 0 ? errorOutput.toString() : "无错误信息";
                throw new IOException("ffmpeg转换失败，退出代码: " + exitCode + "，错误信息: " + errorMsg);
            }

            // 等待错误读取线程结束（最多1秒）
            errorReader.join(1000);

            // 检查输出文件是否存在
            Path pcmPath = Paths.get(tempPcmPath);
            if (!Files.exists(pcmPath)) {
                throw new IOException("ffmpeg转换后的PCM文件不存在: " + tempPcmPath);
            }

            // 检查文件大小
            long fileSize = Files.size(pcmPath);
            if (fileSize == 0) {
                throw new IOException("ffmpeg转换后的PCM文件为空: " + tempPcmPath);
            }

            // 读取生成的PCM文件
            byte[] pcmData = Files.readAllBytes(pcmPath);

            return pcmData;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            if (process != null) {
                process.destroyForcibly();
            }
            throw new IOException("ffmpeg处理被中断", e);
        } catch (IOException e) {
            throw e;
        } catch (Exception e) {
            throw new IOException("使用ffmpeg转换MP3失败: " + e.getMessage(), e);
        } finally {
            // 确保进程被清理
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }

            // 删除临时文件
            if (tempPcmPath != null) {
                try {
                    Files.deleteIfExists(Paths.get(tempPcmPath));
                } catch (IOException e) {
                    logger.warn("删除临时PCM文件失败: {}", tempPcmPath, e);
                }
            }
        }
    }

    /**
     * 重采样PCM音频数据（使用线性插值）
     *
     * @param pcmData 原始PCM数据（16位小端序）
     * @param sourceSampleRate 源采样率
     * @param targetSampleRate 目标采样率
     * @return 重采样后的PCM数据
     */
    public static byte[] resamplePcm(byte[] pcmData, int sourceSampleRate, int targetSampleRate) {
        if (sourceSampleRate == targetSampleRate) {
            return pcmData;
        }

        // 将字节数组转换为short数组（16位PCM）
        int sampleCount = pcmData.length / 2;
        short[] sourceSamples = new short[sampleCount];

        for (int i = 0; i < sampleCount; i++) {
            // 小端序：低字节在前
            int low = pcmData[i * 2] & 0xFF;
            int high = pcmData[i * 2 + 1] & 0xFF;
            sourceSamples[i] = (short) ((high << 8) | low);
        }

        // 计算目标样本数
        double ratio = (double) targetSampleRate / sourceSampleRate;
        int targetSampleCount = (int) (sampleCount * ratio);
        short[] targetSamples = new short[targetSampleCount];

        // 线性插值重采样
        for (int i = 0; i < targetSampleCount; i++) {
            double sourceIndex = i / ratio;
            int index1 = (int) sourceIndex;
            int index2 = Math.min(index1 + 1, sampleCount - 1);
            double fraction = sourceIndex - index1;

            // 线性插值
            double sample = sourceSamples[index1] * (1 - fraction) + sourceSamples[index2] * fraction;
            targetSamples[i] = (short) Math.round(sample);
        }

        // 将short数组转换回字节数组
        byte[] result = new byte[targetSampleCount * 2];
        for (int i = 0; i < targetSampleCount; i++) {
            short sample = targetSamples[i];
            result[i * 2] = (byte) (sample & 0xFF);        // 低字节
            result[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF); // 高字节
        }

        return result;
    }

    /**
     * 将WAV文件重采样为16kHz
     *
     * @param inputWavPath 输入WAV文件路径
     * @param outputWavPath 输出WAV文件路径（如果为null，则覆盖原文件）
     * @return 重采样后的WAV文件路径
     * @throws IOException 如果读取或写入文件失败
     */
    public static String resampleWavTo16kHz(String inputWavPath, String outputWavPath) throws IOException {
        // 读取WAV文件
        byte[] wavData = Files.readAllBytes(Paths.get(inputWavPath));

        if (wavData == null || wavData.length < 44) {
            throw new IOException("无效的WAV数据");
        }

        // 检查WAV文件标识
        if (wavData[0] != 'R' || wavData[1] != 'I' || wavData[2] != 'F' || wavData[3] != 'F' ||
                wavData[8] != 'W' || wavData[9] != 'A' || wavData[10] != 'V' || wavData[11] != 'E') {
            throw new IOException("不是有效的WAV文件格式");
        }

        // 读取原始采样率（位于偏移量24-27，小端序）
        int originalSampleRate = (wavData[24] & 0xFF) |
                                 ((wavData[25] & 0xFF) << 8) |
                                 ((wavData[26] & 0xFF) << 16) |
                                 ((wavData[27] & 0xFF) << 24);

        logger.info("原始采样率: {} Hz", originalSampleRate);

        // 如果已经是16kHz，直接返回
        if (originalSampleRate == SAMPLE_RATE) {
            logger.info("音频已经是16kHz，无需重采样");
            if (outputWavPath != null && !inputWavPath.equals(outputWavPath)) {
                Files.copy(Paths.get(inputWavPath), Paths.get(outputWavPath));
                return outputWavPath;
            }
            return inputWavPath;
        }

        // 提取PCM数据
        byte[] pcmData = wavBytesToPcm(wavData);

        // 重采样到16kHz
        byte[] resampledPcm = resamplePcm(pcmData, originalSampleRate, SAMPLE_RATE);

        // 确定输出路径
        String finalOutputPath = outputWavPath != null ? outputWavPath : inputWavPath;

        // 保存为新的WAV文件
        saveAsWav(Paths.get(finalOutputPath), resampledPcm);

        logger.info("WAV文件已重采样: {} Hz -> {} Hz, 输出: {}",
                    originalSampleRate, SAMPLE_RATE, finalOutputPath);

        return finalOutputPath;
    }

    /**
     * 将WAV文件重采样为16kHz（覆盖原文件）
     *
     * @param wavPath WAV文件路径
     * @return 重采样后的WAV文件路径
     * @throws IOException 如果读取或写入文件失败
     */
    public static String resampleWavTo16kHz(String wavPath) throws IOException {
        return resampleWavTo16kHz(wavPath, null);
    }

    /**
     * 检测音频文件格式并返回MIME类型
     *
     * @param filePath 音频文件路径
     * @return MIME类型字符串
     */
    public static String getMimeType(String filePath) {
        if (filePath.toLowerCase().endsWith(".mp3")) {
            return "audio/mpeg";
        } else if (filePath.toLowerCase().endsWith(".wav")) {
            return "audio/wav";
        } else if (filePath.toLowerCase().endsWith(".pcm")) {
            return "audio/x-pcm";
        } else if (filePath.toLowerCase().endsWith(".opus")) {
            return "audio/opus";
        } else {
            return "application/octet-stream";
        }
    }
}