package cn.naii.iot.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class UpUserRequest {

    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "微信openid")
    @NotBlank(message = "微信openid不能为空")
    private String openId;

    @Schema(description = "性别 0-未知,1-男,2-女")
    private Integer gender;

    @Schema(description = "日历类型 0-未知,1-农历,2-阳历")
    private Integer calendarType;

    @Schema(description = "出生日期")
    private String birthDayTime;

    @Schema(description = "出生地")
    private String birthPlace;

    @Schema(description="昵称")
    private String nickName;
}
