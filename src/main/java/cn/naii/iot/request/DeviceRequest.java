package cn.naii.iot.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class DeviceRequest {


    @Schema(description = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String userId;

    @Schema(description = "微信openId")
    @NotBlank(message = "openId不能为空")
    private String openId;

    @Schema(description = "设备名称")
    private String searchName;

}
