package cn.naii.iot.communication.server.websocket;

import cn.naii.iot.communication.common.ChatSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.TextMessage;

import java.io.IOException;
import java.util.concurrent.locks.ReentrantLock;

public class WebSocketSession extends ChatSession {
    private static final Logger logger = LoggerFactory.getLogger(WebSocketSession.class);
    /**
     * 当前会话的链接 session
     */
    protected org.springframework.web.socket.WebSocketSession session;

    /**
     * 发送消息的锁，防止并发发送导致 TEXT_PARTIAL_WRITING 状态冲突
     */
    private final ReentrantLock sendLock = new ReentrantLock();

    public WebSocketSession(String sessionId) {
        super(sessionId);
    }

    public WebSocketSession(org.springframework.web.socket.WebSocketSession session) {
        super(session.getId());
        this.session = session;
    }

    @Override
    public String getSessionId() {
        return session.getId();
    }

    public org.springframework.web.socket.WebSocketSession getSession() {
        return null;
    }

    @Override
    public void close() {
        if(session != null){
            try {
                session.close();
            } catch (IOException e) {
                logger.error("关闭WebSocket会话时发生错误 - SessionId: {}", getSessionId(), e);
            }
        }
    }

    @Override
    public boolean isOpen() {
        return session.isOpen();
    }

    @Override
    public boolean isAudioChannelOpen() {
        return session.isOpen();
    }

    @Override
    public void sendTextMessage(String message) {
        // 使用锁保证同一时间只有一个线程在发送消息
        sendLock.lock();
        try {
            // 双重检查：发送前确认连接是否打开
            if (!session.isOpen()) {
                logger.debug("⏸️ 连接已关闭，跳过消息发送 - SessionId: {}", getSessionId());
                return;
            }
            session.sendMessage(new TextMessage(message));
        } catch (IllegalStateException e) {
            // 捕获状态异常（如 TEXT_PARTIAL_WRITING）
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("TEXT_PARTIAL_WRITING")) {
                logger.warn("⚠️ 消息发送冲突（并发写入） - SessionId: {}, 将重试", getSessionId());
                // 短暂等待后重试一次
                try {
                    Thread.sleep(10);
                    if (session.isOpen()) {
                        session.sendMessage(new TextMessage(message));
                    }
                } catch (Exception retryEx) {
                    logger.error("❌ 消息发送重试失败 - SessionId: {}", getSessionId(), retryEx);
                }
            } else {
                logger.error("❌ 消息发送失败（状态异常） - SessionId: {}", getSessionId(), e);
            }
        } catch (IOException e) {
            // 捕获IO异常（如连接已关闭）
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("session has been closed")) {
                logger.debug("⏸️ 连接已关闭，消息发送失败 - SessionId: {}", getSessionId());
            } else {
                logger.error("❌ 消息发送失败（IO异常） - SessionId: {}", getSessionId(), e);
            }
        } finally {
            sendLock.unlock();
        }
    }

    @Override
    public void sendBinaryMessage(byte[] message) {
        // 使用锁保证同一时间只有一个线程在发送消息
        sendLock.lock();
        try {
            // 双重检查：发送前确认连接是否打开
            if (!session.isOpen()) {
                logger.debug("⏸️ 连接已关闭，跳过二进制消息发送 - SessionId: {}", getSessionId());
                return;
            }
            session.sendMessage(new BinaryMessage(message));
        } catch (IllegalStateException e) {
            // 捕获状态异常
            logger.error("❌ 二进制消息发送失败（状态异常） - SessionId: {}", getSessionId(), e);
        } catch (IOException e) {
            // 捕获IO异常
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("session has been closed")) {
                logger.debug("⏸️ 连接已关闭，二进制消息发送失败 - SessionId: {}", getSessionId());
            } else {
                logger.error("❌ 二进制消息发送失败（IO异常） - SessionId: {}", getSessionId(), e);
            }
        } finally {
            sendLock.unlock();
        }
    }
}
