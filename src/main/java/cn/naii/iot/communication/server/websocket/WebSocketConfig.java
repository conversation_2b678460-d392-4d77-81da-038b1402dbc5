package cn.naii.iot.communication.server.websocket;

import cn.naii.iot.utils.CmsUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketConfig.class);

    // 定义为public static以便其他类可以访问
    public static final String WS_PATH = "/ws/naii/v1/";

    @Resource
    private WebSocketHandler webSocketHandler;

    @Resource
    private CmsUtils cmsUtils;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketHandler, WS_PATH)
                .setAllowedOrigins("*");

        logger.info("==========================================================");
        logger.info("📡 WebSocket服务地址: {}", cmsUtils.getWebsocketAddress());
        logger.info("🔧 OTA服务地址: {}", cmsUtils.getOtaAddress());
        logger.info("🤪 接口地址: {}", cmsUtils.getServerAddress()+"/swagger-ui.html");
        logger.info("==========================================================");
    }
    
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxTextMessageBufferSize(8192);
        container.setMaxBinaryMessageBufferSize(1024 * 1024); // 1MB
        // 调整WebSocket容器空闲超时为180秒 (3分钟)
        // 确保大于应用层会话超时(120秒)和心跳超时(90秒)
        container.setMaxSessionIdleTimeout(180000L); // 180 seconds
        // 调整异步发送超时为15秒,适应弱网环境和大音频数据
        container.setAsyncSendTimeout(15000L); // 15 seconds
        return container;
    }
}