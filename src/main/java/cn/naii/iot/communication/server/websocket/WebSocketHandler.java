package cn.naii.iot.communication.server.websocket;

import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.MessageHandler;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.communication.domain.AudioParams;
import cn.naii.iot.communication.domain.DeviceStatusMessage;
import cn.naii.iot.communication.domain.HelloMessage;
import cn.naii.iot.communication.domain.HelloMessageResp;
import cn.naii.iot.communication.domain.Message;
import cn.naii.iot.dao.DeviceMapper;
import cn.naii.iot.dialogue.llm.tool.mcp.device.DeviceMcpService;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.utils.JsonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.IOException;
import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class WebSocketHandler extends AbstractWebSocketHandler {
    private static final Logger logger = LoggerFactory.getLogger(WebSocketHandler.class);

    @Resource
    private SessionManager sessionManager;

    @Resource
    private MessageHandler messageHandler;

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private DeviceMcpService deviceMcpService;

    @Resource
    private DeviceMapper deviceMapper;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        Map<String, String> headers = getHeadersFromSession(session);
        String deviceIdAuth = headers.get("device-id");
        String token = headers.get("Authorization");
        if (deviceIdAuth == null || deviceIdAuth.isEmpty()) {
            logger.error("❌ 连接拒绝：设备ID为空 - SessionId: {}", session.getId());
            try {
                session.close(CloseStatus.BAD_DATA.withReason("设备ID为空"));
            } catch (IOException e) {
                logger.error("关闭WebSocket连接失败 - SessionId: {}", session.getId(), e);
            }
            return;
        }
        // 创建会话并注册
        messageHandler.afterConnection(
                new cn.naii.iot.communication.server.websocket.WebSocketSession(session), deviceIdAuth);
        logger.info("🔗 WebSocket连接建立 - SessionId: {}, DeviceId: {}", session.getId(), deviceIdAuth);
    }
    

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        // 如果是正常关闭或服务端发起的关闭，尝试获取设备信息
        if (status.getCode() == CloseStatus.NORMAL.getCode() ||
            status.getCode() == CloseStatus.GOING_AWAY.getCode()) {
            logger.debug("🔌 连接关闭（服务端主动） - SessionId: {}, 状态: {}", sessionId, status);
            sessionManager.closeSessionByServer(sessionId);
        } else {
            // 客户端异常断开或其他情况
            logger.info("🔌 连接关闭 - SessionId: {}, 状态: {}", sessionId, status);
            sessionManager.closeSession(sessionId);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String sessionId = session.getId();

        // 更新用户活动时间 (用户主动发送消息)
        sessionManager.updateLastActivity(sessionId);

        SysDevice device = sessionManager.getDeviceConfig(sessionId);
        String payload = message.getPayload();

        logger.debug("📨 收到文本消息 - SessionId: {}, DeviceId: {}, Length: {} bytes, content: {}", sessionId,
                device != null ? device.getDeviceId() : "Unknown", payload.length(), payload);

        try {
            var msg = JsonUtil.fromJson(payload, Message.class);

            // 检查反序列化是否成功
            if (msg == null) {
                logger.warn("⚠️ 消息反序列化失败 - SessionId: {}, Payload: {}", sessionId, payload);
                return;
            }

            // 处理HelloMessage
            if (msg instanceof HelloMessage m) {
                handleHelloMessage(session, m);
                return;
            }

            // 处理DeviceStatusMessage（设备主动上报状态）
            if (msg instanceof DeviceStatusMessage m) {
                handleDeviceStatusMessage(sessionId, m);
                return;
            }

            // 其他消息需要设备已绑定角色
            if (device == null || device.getRoleId() == null) {
                // 设备未绑定，处理未绑定设备的消息
                messageHandler.handleUnboundDevice(sessionId, device, payload);
                return;
            }

            messageHandler.handleMessage(msg, sessionId);
        } catch (Exception e) {
            logger.error("❌ 文本消息处理失败 - SessionId: {}, DeviceId: {}", sessionId,
                    device != null ? device.getDeviceId() : "Unknown", e);
        }
    }

    /**
     * 处理设备状态消息
     * ESP32设备主动上报设备状态时调用
     *
     * 消息格式示例:
     * {
     *   "type": "device_status",
     *   "data": {
     *     "audio_speaker": { "volume": 70 },
     *     "screen": { "brightness": 100, "theme": "light" },
     *     "battery": { "level": 50, "charging": true },
     *     "network": { "type": "wifi", "ssid": "Xiaozhi", "signal": "strong" },
     *     "chip": { "temperature": 25 }
     *   }
     * }
     */
    private void handleDeviceStatusMessage(String sessionId, DeviceStatusMessage message) {
        // 更新用户活动时间
        sessionManager.updateLastActivity(sessionId);

        // 获取设备信息
        SysDevice device = sessionManager.getDeviceConfig(sessionId);
        if (device == null || "web_test".equals(device.getDeviceId())) {
            return;
        }

        // 解析设备状态数据
        com.fasterxml.jackson.databind.JsonNode data = message.getData();
        if (data == null) {
            logger.warn("⚠️ 设备状态数据为空 - DeviceId: {}", device.getDeviceId());
            return;
        }

        // 异步更新设备状态到数据库
        Thread.startVirtualThread(() -> {
            try {
                // 使用公共方法提取并更新设备状态
                SessionManager.DeviceStatusUpdateResult updateResult =
                    sessionManager.extractAndUpdateDeviceStatus(device, data);

                // 如果有变化，更新数据库
                if (updateResult.isUpdated()) {
                    // 注意：这里直接更新数据库，不调用 deviceService.update()
                    // 原因：deviceService.update() 会触发音量同步逻辑，导致并发冲突
                    // 设备主动上报的音量变化不需要再同步回设备
                    SysDevice updateDevice = new SysDevice()
                        .setDeviceId(device.getDeviceId())
                        .setBatteryStatus(device.getBatteryStatus())
                        .setSignalStrength(device.getSignalStrength())
                        .setNetworkStatus(device.getNetworkStatus())
                        .setVolume(device.getVolume())
                        .setState(SysDevice.DEVICE_STATE_ONLINE)
                        .setLastLogin(new Date().toString());

                    // 直接调用 mapper 更新，避免触发音量同步
                    deviceMapper.update(updateDevice);

                    // 更新会话中的设备信息
                    ChatSession chatSession = sessionManager.getSession(sessionId);
                    if (chatSession != null) {
                        chatSession.setSysDevice(device);
                    }

                    logger.info("📊 设备状态已更新（主动上报） - DeviceId: {}, 变化: {}",
                        device.getDeviceId(), updateResult.getUpdateInfo());
                }
            } catch (Exception e) {
                logger.error("❌ 设备状态上报处理失败 - DeviceId: {}", device.getDeviceId(), e);
            }
        });
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        String sessionId = session.getId();

        // 更新用户活动时间 (用户发送音频数据)
        sessionManager.updateLastActivity(sessionId);

        SysDevice device = sessionManager.getDeviceConfig(sessionId);
        if (device == null) {
            return;
        }
        // 移除高频DEBUG日志，音频帧接收不再记录
        messageHandler.handleBinaryMessage(sessionId, message.getPayload().array());
    }


    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        String sessionId = session.getId();
        // 检查是否是客户端正常关闭连接导致的异常
        if (isClientCloseRequest(exception)) {
            // 客户端主动关闭，记录为DEBUG级别
            logger.debug("🔌 连接关闭（客户端主动） - SessionId: {}", sessionId);
            sessionManager.closeSession(sessionId);
        } else if (isConnectionAbortedException(exception)) {
            // 连接中止异常（通常是心跳发送时连接已断开），使用DEBUG级别
            logger.debug("🔌 连接中止 - SessionId: {}", sessionId);
            sessionManager.closeSession(sessionId);
        } else {
            // 真正的传输错误
            logger.error("❌ WebSocket传输错误 - SessionId: {}", sessionId, exception);
            // 在传输错误时，不尝试通过已损坏的连接发送消息更新设备信息
            // 直接将设备状态更新为离线
            SysDevice device = sessionManager.getDeviceConfig(sessionId);
            if (device != null && !"web_test".equals(device.getDeviceId())) {
                Thread.startVirtualThread(() -> {
                    try {
                        deviceService.update(new SysDevice()
                                .setDeviceId(device.getDeviceId())
                                .setState(SysDevice.DEVICE_STATE_OFFLINE)
                                .setLastLogin(new Date().toString()));
                        logger.info("📴 设备已离线（传输错误） - DeviceId: {}", device.getDeviceId());
                    } catch (Exception e) {
                        logger.error("❌ 更新设备离线状态失败 - DeviceId: {}", device.getDeviceId(), e);
                    }
                });
            }
            // 传输错误时使用服务端关闭方法
            sessionManager.closeSessionByServer(sessionId);
        }
    }

    /**
     * 判断异常是否由客户端主动关闭连接导致
     */
    private boolean isClientCloseRequest(Throwable exception) {
        // 检查常见的客户端关闭连接导致的异常类型
        if (exception instanceof IOException) {
            String message = exception.getMessage();
            if (message != null) {
                return message.contains("Connection reset by peer") ||
                        message.contains("Broken pipe") ||
                        message.contains("Connection closed") ||
                        message.contains("远程主机强迫关闭了一个现有的连接");
            }
            // 处理EOFException，这通常是客户端关闭连接导致的
            return exception instanceof java.io.EOFException;
        }
        return false;
    }

    /**
     * 判断异常是否是连接中止异常（通常是心跳发送时连接已断开）
     */
    private boolean isConnectionAbortedException(Throwable exception) {
        if (exception instanceof IOException) {
            String message = exception.getMessage();
            if (message != null) {
                return message.contains("你的主机中的软件中止了一个已建立的连接") ||
                        message.contains("An established connection was aborted") ||
                        message.contains("Connection aborted");
            }
        }
        return false;
    }

    private void handleHelloMessage(WebSocketSession session, HelloMessage message) {
        var sessionId = session.getId();
        logger.info("收到hello消息 - SessionId: {}, JsonNode: {}", sessionId, message);

        if (message.getAudioParams() != null) {
            logger.info("客户端音频参数 - 格式: {}, 采样率: {}, 声道: {}, 帧时长: {}ms",
                    message.getAudioParams().getFormat(),
                    message.getAudioParams().getSampleRate(),
                    message.getAudioParams().getChannels(),
                    message.getAudioParams().getFrameDuration());
        }

        // 回复hello消息
        var resp = new HelloMessageResp()
                .setTransport("websocket")
                .setSessionId(sessionId)
                .setAudioParams(AudioParams.Opus);

        try {
            String responsePayload = JsonUtil.toJson(resp);
            logger.info("📤 发送hello响应消息 - SessionId: {}, Payload: {}", sessionId, responsePayload);

            // 获取ChatSession并使用其sendTextMessage方法，确保使用锁机制避免并发冲突
            ChatSession chatSession = sessionManager.getSession(sessionId);
            if (chatSession != null) {
                // 使用ChatSession的sendTextMessage方法，而不是直接使用WebSocketSession
                // 这样可以利用锁机制避免TEXT_PARTIAL_WRITING错误
                chatSession.sendTextMessage(responsePayload);

                SysDevice device = sessionManager.getDeviceConfig(sessionId);

                // 在发送hello响应后，统一处理设备初始化
                // 包括：获取设备信息、处理待处理更新(音量等)
                sessionManager.handleDeviceInitializationAfterHello(chatSession);

                // 如果客户端开启mcp协议，异步初始化MCP工具
                if (message.getFeatures() != null && message.getFeatures().getMcp()) {
                    Thread.startVirtualThread(() -> {
                        if (device != null && device.getRoleId() != null) {
                            deviceMcpService.initialize(chatSession);
                        }
                    });
                }
            } else {
                // 如果ChatSession不存在，降级使用WebSocketSession直接发送
                logger.warn("⚠️ ChatSession不存在，使用WebSocketSession直接发送 - SessionId: {}", sessionId);
                session.sendMessage(new TextMessage(responsePayload));
            }
        } catch (Exception e) {
            logger.error("❌ 发送hello响应失败", e);
        }
    }
    private Map<String, String> getHeadersFromSession(WebSocketSession session) {
        // 尝试从请求头获取设备ID
        String[] deviceKeys = {"device-id", "mac_address", "uuid", "Authorization"};

        Map<String, String> headers = new HashMap<>();

        for (String key : deviceKeys) {
            String value = session.getHandshakeHeaders().getFirst(key);
            if (value != null) {
                headers.put(key, value);
            }
        }
        // 尝试从URI参数中获取
        URI uri = session.getUri();
        if (uri != null) {
            String query = uri.getQuery();
            if (query != null) {
                for (String key : deviceKeys) {
                    String paramPattern = key + "=";
                    int startIdx = query.indexOf(paramPattern);
                    if (startIdx >= 0) {
                        startIdx += paramPattern.length();
                        int endIdx = query.indexOf('&', startIdx);
                        headers.put(key, endIdx >= 0 ? query.substring(startIdx, endIdx) : query.substring(startIdx));
                    }
                }
            }
        }
        return headers;
    }


}