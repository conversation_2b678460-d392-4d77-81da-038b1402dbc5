package cn.naii.iot.communication.common;

import cn.naii.iot.communication.domain.DeviceMcpMessage;
import cn.naii.iot.dialogue.llm.tool.ToolsSessionHolder;
import cn.naii.iot.dialogue.llm.tool.mcp.device.DeviceMcpService;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.enums.ListenMode;
import cn.naii.iot.event.ChatSessionCloseEvent;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.utils.JsonUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * WebSocket会话管理服务
 * 负责管理所有WebSocket连接的会话状态
 * 使用JDK 21虚拟线程实现异步处理
 * TODO 重构计划：可能没必要作为Service由Spring管理，而是由Handler处理。
 * TODO 实际底层驱动力来自于Handler，后续服务都是基于Session而不需要SessionManager的。
 */
@Service
public class SessionManager {
    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);

    // 设置用户不活跃超时时间为120秒
    private static final long INACTIVITY_TIMEOUT_SECONDS = 120;

    // 心跳间隔时间为30秒
    private static final long HEARTBEAT_INTERVAL_SECONDS = 30;

    // 心跳超时时间为10秒
    private static final long HEARTBEAT_TIMEOUT_SECONDS = 10;

    // 心跳响应超时时间为120秒 (4个心跳周期)
    private static final long HEARTBEAT_RESPONSE_TIMEOUT_SECONDS = 120;

    // 用于存储所有连接的会话信息
    private final Map<String, ChatSession> sessions = new java.util.concurrent.ConcurrentHashMap<>();

    // 存储验证码生成状态
    private final Map<String, Boolean> captchaState = new java.util.concurrent.ConcurrentHashMap<>();

    // 存储设备待处理的更新操作
    private final Map<String, DevicePendingUpdate> pendingUpdates = new java.util.concurrent.ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    @Lazy
    private SysDeviceService deviceService;

    /**
     * 初始化方法，启动定时检查不活跃会话的任务
     */
    @PostConstruct
    public void init() {
        // 项目启动时，将所有设备状态设置为离线
        // 延迟执行设备状态重置，避免循环依赖
        scheduler.schedule(() -> {
            try {
                SysDevice device = new SysDevice();
                device.setState(SysDevice.DEVICE_STATE_OFFLINE);
                // 不设置deviceId，这样会更新所有设备
                int updatedRows = deviceService.update(device);
                logger.info("🚀 系统启动 - 重置 {} 个设备为离线状态", updatedRows);
            } catch (Exception e) {
                logger.error("❌ 系统启动失败 - 设备状态重置失败", e);
            }
        }, 1, TimeUnit.SECONDS);

        // 每10秒检查一次不活跃的会话
        scheduler.scheduleAtFixedRate(this::checkInactiveSessions, 10, 10, TimeUnit.SECONDS);

        // 每30秒发送一次心跳
        scheduler.scheduleAtFixedRate(this::sendHeartbeatToAllSessions, 30, HEARTBEAT_INTERVAL_SECONDS, TimeUnit.SECONDS);

        // 每10秒检查一次心跳超时
        scheduler.scheduleAtFixedRate(this::checkHeartbeatTimeout, 10, 10, TimeUnit.SECONDS);

        logger.info("⏰ 定时任务已启动 - 不活跃检查: {}s, 心跳间隔: {}s",
            INACTIVITY_TIMEOUT_SECONDS, HEARTBEAT_INTERVAL_SECONDS);
    }



    /**
     * 更新单个会话的设备信息
     * 
     * @param chatSession 会话
     */
    public void updateDeviceInfoForSession(ChatSession chatSession) {
        try {
            String sessionId = chatSession.getSessionId();
            // 直接从会话中获取设备信息，而不是通过sessions映射
            SysDevice device = chatSession.getSysDevice();

            if (device == null || device.getDeviceId() == null) {
                return;
            }

            // 对于deviceId为web_test的设备，不进行数据库更新
            if ("web_test".equals(device.getDeviceId())) {
                return;
            }

            if (device.getRoleId() == null) {
                return;
            }

            // 检查会话是否有效，在连接关闭时，会话可能已经关闭，不应再尝试发送消息
            if (!chatSession.isOpen()) {
                return;
            }

            // 通过ApplicationContext获取DeviceMcpService
            DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);

            // 发送设备信息请求
            DeviceMcpMessage deviceMcpMessage = deviceMcpService.sendDeviceInfo(chatSession);
            if (deviceMcpMessage != null) {
                // 更新设备信息到数据库
                updateDeviceInformation(device, deviceMcpMessage, chatSession);
            } else {
                logger.warn("⚠️ MCP设备信息请求失败 - DeviceId: {}", device.getDeviceId());
            }
        } catch (Exception e) {
            logger.error("❌ 设备信息更新异常 - SessionId: {}", chatSession.getSessionId(), e);
        }
    }

    /**
     * 向所有在线会话发送心跳
     */
    private void sendHeartbeatToAllSessions() {
        Thread.startVirtualThread(() -> {
            try {
                final int[] totalSessions = {0};
                final int[] successCount = {0};
                final int[] failureCount = {0};

                sessions.values().stream()
                    .filter(ChatSession::isOpen)
                    .filter(session -> {
                        SysDevice device = session.getSysDevice();
                        return device != null && !"web_test".equals(device.getDeviceId());
                    })
                    .forEach(session -> {
                        totalSessions[0]++;
                        boolean success = sendHeartbeatToSession(session);
                        if (success) {
                            successCount[0]++;
                        } else {
                            failureCount[0]++;
                        }
                    });

                if (totalSessions[0] > 0) {
                    logger.debug("💓 心跳发送完成 - 总数: {}, 成功: {}, 失败: {}",
                        totalSessions[0], successCount[0], failureCount[0]);
                }
            } catch (Exception e) {
                logger.error("发送心跳时出错", e);
            }
        });
    }

    /**
     * 向单个会话发送心跳
     * @param session 会话
     * @return 是否成功
     */
    private boolean sendHeartbeatToSession(ChatSession session) {
        try {
            // 所有设备统一发送简单心跳,不再定时获取设备信息
            // 设备信息变动时会主动上报
            return sendSimpleHeartbeat(session);
        } catch (Exception e) {
            logger.warn("向会话 {} 发送心跳失败", session.getSessionId(), e);
            return false;
        }
    }

    /**
     * 发送MCP心跳 (获取设备信息)
     *
     * @deprecated 已废弃 - 设备信息变动时会主动上报,不再需要定时获取
     * 保留此方法以备将来需要时使用
     */
    @Deprecated
    private boolean sendMcpHeartbeat(ChatSession session) {
        try {
            DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);

            // 使用CompletableFuture实现非阻塞超时
            CompletableFuture<DeviceMcpMessage> future = CompletableFuture.supplyAsync(() -> {
                return deviceMcpService.sendDeviceInfo(session);
            });

            // 等待最多10秒 (比原来的30秒短,避免阻塞)
            DeviceMcpMessage response = future.get(HEARTBEAT_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (response != null) {
                // 心跳成功
                session.setLastHeartbeatResponseTime(Instant.now());
                session.getConsecutiveHeartbeatFailures().set(0);

                // 更新设备信息到数据库
                SysDevice device = session.getSysDevice();
                updateDeviceInformation(device, response, session);

                return true;
            } else {
                // 心跳失败
                int failures = session.getConsecutiveHeartbeatFailures().incrementAndGet();
                logger.warn("💔 MCP心跳失败 - DeviceId: {}, 连续失败: {}次",
                    session.getSysDevice().getDeviceId(), failures);
                return false;
            }
        } catch (TimeoutException e) {
            int failures = session.getConsecutiveHeartbeatFailures().incrementAndGet();
            logger.warn("⏰ MCP心跳超时 - DeviceId: {}, 连续失败: {}次",
                session.getSysDevice().getDeviceId(), failures);
            return false;
        } catch (Exception e) {
            int failures = session.getConsecutiveHeartbeatFailures().incrementAndGet();
            logger.warn("❌ MCP心跳异常 - DeviceId: {}, 连续失败: {}次",
                session.getSysDevice().getDeviceId(), failures, e);
            return false;
        }
    }

    /**
     * 发送简单心跳 (ping消息)
     * 用于保持WebSocket连接活跃,检测连接是否断开
     * 设备信息变动时会主动上报,不再需要通过心跳获取设备信息
     */
    private boolean sendSimpleHeartbeat(ChatSession session) {
        try {
            // 双重检查：发送前再次确认连接是否打开
            if (!session.isOpen()) {
                logger.debug("⏸️ 会话已关闭，跳过心跳发送 - DeviceId: {}", session.getSysDevice().getDeviceId());
                return false;
            }

            // 发送一个简单的JSON消息作为心跳
            String heartbeatMessage = "{\"type\":\"ping\",\"timestamp\":" + System.currentTimeMillis() + "}";
            session.sendTextMessage(heartbeatMessage);

            // 简单心跳不需要等待响应,发送成功即认为连接存活
            session.setLastHeartbeatResponseTime(Instant.now());
            session.getConsecutiveHeartbeatFailures().set(0);

            return true;
        } catch (Exception e) {
            // 心跳发送失败（通常是连接已断开），使用DEBUG级别避免日志噪音
            int failures = session.getConsecutiveHeartbeatFailures().incrementAndGet();
            String errorMsg = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            logger.debug("💔 简单心跳失败 - DeviceId: {}, 连续失败: {}次, 原因: {}",
                session.getSysDevice().getDeviceId(), failures, errorMsg);
            return false;
        }
    }

    /**
     * 检查心跳超时
     * 如果连续N次心跳失败,则关闭连接
     */
    private void checkHeartbeatTimeout() {
        Thread.startVirtualThread(() -> {
            try {
                Instant now = Instant.now();
                sessions.values().forEach(session -> {
                    try {
                        // 检查连续心跳失败次数
                        int failures = session.getConsecutiveHeartbeatFailures().get();
                        if (failures >= ChatSession.MAX_HEARTBEAT_FAILURES) {
                            logger.warn("💀 心跳超时，关闭连接 - SessionId: {}, DeviceId: {}, 连续失败: {}次",
                                session.getSessionId(),
                                session.getSysDevice() != null ? session.getSysDevice().getDeviceId() : "unknown",
                                failures);
                            closeSession(session);
                            return;
                        }

                        // 检查心跳响应时间 (作为额外保护)
                        Instant lastHeartbeat = session.getLastHeartbeatResponseTime();
                        if (lastHeartbeat != null) {
                            Duration heartbeatDuration = Duration.between(lastHeartbeat, now);
                            // 如果超过120秒没有心跳响应 (4个心跳周期)
                            if (heartbeatDuration.getSeconds() > HEARTBEAT_RESPONSE_TIMEOUT_SECONDS) {
                                logger.warn("💀 心跳无响应，关闭连接 - SessionId: {}, DeviceId: {}, 最后心跳: {}秒前",
                                    session.getSessionId(),
                                    session.getSysDevice() != null ? session.getSysDevice().getDeviceId() : "unknown",
                                    heartbeatDuration.getSeconds());
                                closeSession(session);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("❌ 心跳超时检查异常 - SessionId: {}", session.getSessionId(), e);
                    }
                });
            } catch (Exception e) {
                logger.error("❌ 心跳超时检查失败", e);
            }
        });
    }


    /**
     * 提取设备状态信息并更新到SysDevice对象
     *
     * @param device 设备对象
     * @param deviceInfo 设备状态JSON数据
     * @return DeviceStatusUpdateResult 包含是否有更新和更新信息
     */
    public DeviceStatusUpdateResult extractAndUpdateDeviceStatus(SysDevice device, com.fasterxml.jackson.databind.JsonNode deviceInfo) {
        boolean updated = false;
        StringBuilder updateInfo = new StringBuilder();

        // 1. 提取音量 (audio_speaker.volume)
        com.fasterxml.jackson.databind.JsonNode audioSpeaker = deviceInfo.get("audio_speaker");
        if (audioSpeaker != null && audioSpeaker.has("volume")) {
            String newVolume = String.valueOf(audioSpeaker.get("volume").asInt());
            if (!Objects.equals(device.getVolume(), newVolume)) {
                String oldVolume = device.getVolume();
                device.setVolume(newVolume);
                updated = true;
                updateInfo.append(String.format("音量: %s->%s ", oldVolume, newVolume));
            }
        }

        // 2. 提取电池状态 (battery.level + battery.charging)
        com.fasterxml.jackson.databind.JsonNode battery = deviceInfo.get("battery");
        if (battery != null && battery.has("level")) {
            int level = battery.get("level").asInt();
            // 检查是否有charging字段
            String newBatteryStatus;
            if (battery.has("charging")) {
                boolean charging = battery.get("charging").asBoolean();
                newBatteryStatus = level + "%" + (charging ? "(充电中)" : "");
            } else {
                // 如果没有charging字段，只显示电量百分比
                newBatteryStatus = String.valueOf(level);
            }

            if (!Objects.equals(device.getBatteryStatus(), newBatteryStatus)) {
                String oldBatteryStatus = device.getBatteryStatus();
                device.setBatteryStatus(newBatteryStatus);
                updated = true;
                updateInfo.append(String.format("电池: %s->%s ", oldBatteryStatus, newBatteryStatus));
            }
        }

        // 3. 提取信号强度和联网状态 (network.signal, network.status)
        com.fasterxml.jackson.databind.JsonNode network = deviceInfo.get("network");
        if (network != null) {
            if (network.has("signal")) {
                String newSignal = network.get("signal").asText();
                if (!Objects.equals(device.getSignalStrength(), newSignal)) {
                    String oldSignal = device.getSignalStrength();
                    device.setSignalStrength(newSignal);
                    updated = true;
                    updateInfo.append(String.format("信号: %s->%s ", oldSignal, newSignal));
                }
            }
            if (network.has("status")) {
                String newStatus = network.get("status").asText();
                if (!Objects.equals(device.getNetworkStatus(), newStatus)) {
                    String oldStatus = device.getNetworkStatus();
                    device.setNetworkStatus(newStatus);
                    updated = true;
                    updateInfo.append(String.format("联网状态: %s->%s ", oldStatus, newStatus));
                }
            }
        }

        return new DeviceStatusUpdateResult(updated, updateInfo.toString().trim());
    }

    /**
     * 设备状态更新结果
     */
    @Getter
    public static class DeviceStatusUpdateResult {
        private final boolean updated;
        private final String updateInfo;

        public DeviceStatusUpdateResult(boolean updated, String updateInfo) {
            this.updated = updated;
            this.updateInfo = updateInfo;
        }

    }

    /**
     * 更新设备信息到数据库 (从MCP消息中提取)
     *
     * @param device 设备对象
     * @param deviceMcpMessage MCP消息
     * @param chatSession 会话
     */
    private void updateDeviceInformation(SysDevice device, DeviceMcpMessage deviceMcpMessage, ChatSession chatSession) {
        try {
            // 对于deviceId为web_test的设备，不进行数据库更新
            if (device != null && "web_test".equals(device.getDeviceId())) {
                return;
            }

            Map<String, Object> result = deviceMcpMessage.getPayload().getResult();
            if (!org.springframework.util.CollectionUtils.isEmpty(result)) {
                // 检查content字段是否存在且不为null
                Object contentObj = result.get("content");
                if (contentObj != null) {
                    // 解析设备信息JSON
                    com.fasterxml.jackson.databind.JsonNode deviceInfo = parseDeviceInfo(contentObj.toString());
                    if (deviceInfo != null) {
                        // 提取并更新设备状态
                        DeviceStatusUpdateResult updateResult = extractAndUpdateDeviceStatus(device, deviceInfo);

                        // 如果没有任何更新，则直接返回
                        if (!updateResult.isUpdated()) {
                            return;
                        }

                        logger.info("📊 设备状态变化（MCP响应） - DeviceId: {}, 变化: {}",
                            device.getDeviceId(), updateResult.getUpdateInfo());
                    }
                }
            }

            // 更新设备信息前检查device和deviceId是否为空
            if (device != null && device.getDeviceId() != null) {
                // 通过ApplicationContext获取SysDeviceService
                SysDeviceService sysDeviceService = applicationContext.getBean(SysDeviceService.class);
                sysDeviceService.update(new SysDevice()
                        .setDeviceId(device.getDeviceId())
                        .setBatteryStatus(device.getBatteryStatus())
                        .setSignalStrength(device.getSignalStrength())
                        .setNetworkStatus(device.getNetworkStatus())
                        .setVolume(device.getVolume())
                        .setState(SysDevice.DEVICE_STATE_ONLINE) // 确保设备状态为在线
                        .setLastLogin(new java.util.Date().toString()));
            }
        } catch (Exception e) {
            logger.error("❌ 设备信息数据库更新失败 - DeviceId: {}",
                device != null ? device.getDeviceId() : "unknown", e);
        }
    }
    
    /**
     * 解析设备信息，处理多种可能的输入格式
     *
     * @param contentStr 原始内容字符串
     * @return 解析后的设备信息JsonNode，如果解析失败返回null
     */
    private com.fasterxml.jackson.databind.JsonNode parseDeviceInfo(String contentStr) {
        logger.debug("原始content字符串: {}", contentStr);

        try {
            // 解析类似 [{type=text, text={...}}] 格式的字符串
            if (contentStr.startsWith("[{") && contentStr.endsWith("}]")) {
                // 移除外层的方括号
                String innerContent = contentStr.substring(1, contentStr.length() - 1);

                // 提取text部分的内容
                // 查找text=后面的内容
                int textStartIndex = innerContent.indexOf("text=");
                if (textStartIndex != -1) {
                    // 跳过"text="部分
                    textStartIndex += 5;

                    // 查找text值的结束位置（考虑嵌套的大括号）
                    int braceCount = 0;
                    int textEndIndex = textStartIndex;

                    // 如果text值以{开始
                    if (innerContent.charAt(textStartIndex) == '{') {
                        braceCount = 1;
                        textEndIndex = textStartIndex + 1;

                        while (braceCount > 0 && textEndIndex < innerContent.length()) {
                            char c = innerContent.charAt(textEndIndex);
                            if (c == '{') {
                                braceCount++;
                            } else if (c == '}') {
                                braceCount--;
                            }
                            textEndIndex++;
                        }

                        // 提取text字段中的JSON字符串
                        String textJson = innerContent.substring(textStartIndex, textEndIndex);
                        logger.debug("提取的text JSON: {}", textJson);

                        // 解析内部的JSON结构
                        return JsonUtil.OBJECT_MAPPER.readTree(textJson);
                    }
                }
            } else {
                // 如果是正常的JSON字符串，则按原来的方式处理
                logger.debug("处理后的content字符串: {}", contentStr);
                // 解析JSON
                com.fasterxml.jackson.databind.JsonNode contentArray = JsonUtil.OBJECT_MAPPER.readTree(contentStr);

                if (contentArray.isArray() && !contentArray.isEmpty()) {
                    com.fasterxml.jackson.databind.JsonNode firstContent = contentArray.get(0);
                    // 检查text字段是否存在且不为null
                    if (firstContent != null && firstContent.has("text")) {
                        // 获取text字段中的JSON字符串
                        String textJson = firstContent.get("text").asText();
                        // 解析内部的JSON结构
                        return JsonUtil.OBJECT_MAPPER.readTree(textJson);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析设备信息内容失败: {}", contentStr, e);
        }

        return null;
    }

    /**
     * 销毁方法，关闭定时任务执行器
     */
    @PreDestroy
    public void destroy() {
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("不活跃会话检查任务已关闭");
    }

    /**
     * 检查不活跃的会话并关闭它们
     * 只检查用户活动时间,不受心跳影响
     * 使用虚拟线程实现异步处理
     */
    private void checkInactiveSessions() {
        Thread.startVirtualThread(() -> {
            Instant now = Instant.now();
            sessions.values().forEach(session -> {
                Instant lastUserActivity = session.getLastActivityTime();
                if (lastUserActivity != null) {
                    Duration inactiveDuration = Duration.between(lastUserActivity, now);
                    // 用户120秒无活动则关闭
                    if (inactiveDuration.getSeconds() > INACTIVITY_TIMEOUT_SECONDS) {
                        logger.info("😴 用户无活动，关闭连接 - SessionId: {}, DeviceId: {}, 无活动时长: {}秒",
                            session.getSessionId(),
                            session.getSysDevice() != null ? session.getSysDevice().getDeviceId() : "unknown",
                            inactiveDuration.getSeconds());
                        closeSession(session);
                    }
                }
            });
        });
    }

    /**
     * 更新会话的最后有效活动时间
     * 这个方法应该只在检测到实际的用户活动时调用，如语音输入或明确的交互
     *
     * @param sessionId 会话ID
     */
    public void updateLastActivity(String sessionId) {
        ChatSession session = sessions.get(sessionId);
        if(session != null){
            session.setLastActivityTime(Instant.now());
        }
    }

    /**
     * 注册新的会话
     *
     * @param sessionId 会话ID
     * @param chatSession  会话
     */
    public void registerSession(String sessionId, ChatSession chatSession) {
        sessions.put(sessionId, chatSession);
        logger.info("📝 会话已注册 - SessionId: {}, Type: {}", sessionId, chatSession.getClass().getSimpleName());
    }

    /**
     * 注册设备配置
     *
     * @param sessionId 会话ID
     * @param device    设备信息
     */
    public void registerDevice(String sessionId, SysDevice device) {
        // 先检查是否已存在该sessionId的配置
        ChatSession chatSession = sessions.get(sessionId);
        if(chatSession != null){
            chatSession.setSysDevice(device);
            updateLastActivity(sessionId); // 更新活动时间

            // 注意：待处理的更新已移至 handleHelloMessage 中
            // 确保在hello响应后再发送MCP消息
        }
    }

    /**
     * 服务端主动关闭WebSocket会话
     * 在关闭前发送设备信息请求以获取最新状态
     *
     * @param sessionId 会话ID
     */
    public void closeSessionByServer(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            // 注释掉：连接关闭时不应再尝试发送MCP请求
            // 原因：此时连接可能已经处于关闭状态，发送消息会导致异常
            // if (chatSession.isOpen()) {
            //     try {
            //         updateDeviceInfoForSession(chatSession);
            //     } catch (Exception e) {
            //         logger.warn("服务端主动关闭连接时更新设备信息失败 - SessionId: {}", sessionId, e);
            //     }
            // }
            closeSession(chatSession);
        }
    }

    /**
     * 关闭并清理WebSocket会话
     *
     * @param sessionId 会话ID
     */
    public void closeSession(String sessionId){
        ChatSession chatSession = sessions.get(sessionId);
        if(chatSession != null) {
            closeSession(chatSession);
        }
    }

    /**
     * 关闭并清理WebSocket会话
     * 使用虚拟线程实现异步处理
     *
     * @param chatSession 聊天session
     */
    public void closeSession(ChatSession chatSession) {
        if(chatSession == null){
            return;
        }

        // 防止重复关闭
        if (chatSession.getAttribute("closing") != null) {
            logger.debug("会话 {} 正在关闭中，跳过重复关闭", chatSession.getSessionId());
            return;
        }
        chatSession.setAttribute("closing", true);

        try {
            // 1. 先发布事件(让监听器有机会访问会话)
            applicationContext.publishEvent(new ChatSessionCloseEvent(chatSession));

            // 2. 清理音频流
            Sinks.Many<byte[]> sink = chatSession.getAudioSinks();
            if (sink != null) {
                sink.tryEmitComplete();
            }

            // 3. 重置会话状态
            chatSession.setStreamingState(false);
            chatSession.setAudioSinks(null);

            // 4. 关闭底层连接
            chatSession.close();

            // 5. 最后从Map中移除
            sessions.remove(chatSession.getSessionId());

            logger.info("🗑️ 会话已关闭 - SessionId: {}, Type: {}",
                chatSession.getSessionId(), chatSession.getClass().getSimpleName());
        } catch (Exception e) {
            logger.error("❌ 会话资源清理失败 - SessionId: {}",
                    chatSession.getSessionId(), e);
        }
    }

    /**
     * 在hello响应后统一处理设备初始化
     * 包括：获取设备信息、处理待处理更新等
     *
     * @param chatSession 会话
     */
    public void handleDeviceInitializationAfterHello(ChatSession chatSession) {
        if (chatSession == null) {
            return;
        }

        SysDevice device = chatSession.getSysDevice();
        if (device == null || device.getDeviceId() == null) {
            return;
        }

        // 对于web_test设备，不进行MCP操作
        if ("web_test".equals(device.getDeviceId())) {
            return;
        }

        // 只有绑定了角色的设备才进行初始化
        if (device.getRoleId() == null) {
            return;
        }

        // 检查会话是否有效
        if (!chatSession.isOpen()) {
            return;
        }

        // 使用虚拟线程异步执行，避免阻塞hello响应
        Thread.startVirtualThread(() -> {
            try {
                DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);

                // 1. 首先获取设备信息
//                logger.info("📱 开始设备初始化 - DeviceId: {}", device.getDeviceId());
//                updateDeviceInfoForSession(chatSession);

                // 2. 然后处理待处理的更新（音量等）
                handlePendingUpdatesOnDeviceOnline(chatSession, device);
                logger.info("✅ 设备初始化完成 - DeviceId: {}", device.getDeviceId());
            } catch (Exception e) {
                logger.error("❌ 设备初始化失败 - DeviceId: {}", device.getDeviceId(), e);
            }
        });
    }

    /**
     * 处理设备上线时的待处理更新
     * @param chatSession 会话
     * @param device 设备
     */
    private void handlePendingUpdatesOnDeviceOnline(ChatSession chatSession, SysDevice device) {
        DevicePendingUpdate pendingUpdate = pendingUpdates.get(device.getDeviceId());
        if (pendingUpdate != null && pendingUpdate.hasPendingUpdates()) {
            try {
                // 获取DeviceMcpService
                DeviceMcpService deviceMcpService = applicationContext.getBean(DeviceMcpService.class);

                // 处理所有待处理的更新
                Map<String, Object> updates = pendingUpdate.getAndClearPendingUpdates();

                // 发送音量更新（如果存在）
                Object volume = updates.get("volume");
                if (volume != null) {
                    deviceMcpService.sendSetVolume(chatSession, volume);
                    logger.info("📢 同步待处理更新 - DeviceId: {}, 音量: {}", device.getDeviceId(), volume);
                }

                // 可以在这里添加其他属性的处理逻辑
                // 从待处理更新队列中移除
                pendingUpdates.remove(device.getDeviceId());

            } catch (Exception e) {
                logger.error("❌ 待处理更新同步失败 - DeviceId: {}", device.getDeviceId(), e);
            }
        }
    }

    /**
     * 添加设备待处理更新
     * @param deviceId 设备ID
     * @param attributeName 属性名
     * @param value 属性值
     */
    public void addDevicePendingUpdate(String deviceId, String attributeName, Object value) {
        // 只对离线设备添加待处理更新
        ChatSession session = getSessionByDeviceId(deviceId);
        if (session == null || !session.isOpen()) {
            DevicePendingUpdate pendingUpdate = pendingUpdates.computeIfAbsent(deviceId, DevicePendingUpdate::new);
            pendingUpdate.addPendingUpdate(attributeName, value);
            logger.debug("设备 {} 离线，添加待处理更新: {} = {}", deviceId, attributeName, value);
        }
    }

    /**
     * 设置会话完成后是否关闭
     *
     * @param sessionId 会话ID
     * @param close     是否关闭
     */
    public void setCloseAfterChat(String sessionId, boolean close) {
        ChatSession chatSession = sessions.get(sessionId);
        if(chatSession != null){
            chatSession.setCloseAfterChat(close);
        }
    }

    /**
     * 获取会话完成后是否关闭
     *
     * @param sessionId 会话ID
     * @return 是否关闭
     */
    public boolean isCloseAfterChat(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if(chatSession != null){
            return chatSession.isCloseAfterChat();
        }else{
            return true;
        }
    }

//    /**
//     * 缓存配置信息
//     *
//     * @param configId 配置ID
//     * @param config   配置信息
//     */
//    public void cacheConfig(Integer configId, SysConfig config) {
//        if (configId != null && config != null) {
//            configCache.put(configId, config);
//        }
//    }
//
//    /**
//     * 删除配置
//     *
//     * @param configId 配置ID
//     */
//    public void removeConfig(Integer configId) {
//        configCache.remove(configId);
//    }

    /**
     * 获取所有活动会话
     *
     * @return 所有活动会话列表
     */
    public List<ChatSession> getAllSessions() {
        return sessions.values().stream().collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取会话
     *
     * @param sessionId 会话ID
     * @return WebSocket会话
     */
    public ChatSession getSession(String sessionId) {
        return sessions.get(sessionId);
    }

    /**
     * 获取会话
     *
     * @param deviceId 设备ID
     * @return 会话ID
     */
    public ChatSession getSessionByDeviceId(String deviceId) {
        for (ChatSession chatSession : sessions.values()) {
            if (chatSession.getSysDevice() != null && deviceId.equals(chatSession.getSysDevice().getDeviceId())) {
                return chatSession;
            }
        }
        return null;
    }

    /**
     * 获取设备配置
     *
     * @param sessionId 会话ID
     * @return 设备配置
     */
    public SysDevice getDeviceConfig(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getSysDevice();
        }
        return null;
    }

    /**
     * 获取会话的function holder
     *
     * @param sessionId 会话ID
     * @return FunctionSessionHolder
     */
    public ToolsSessionHolder getFunctionSessionHolder(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getFunctionSessionHolder();
        }
        return null;
    }

    /**
     * 获取用户的可用角色列表
     *
     * @param sessionId 会话ID
     * @return 角色列表
     */
    public List<SysRole> getAvailableRoles(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getSysRoleList();
        }
        return null;
    }

    /**
     * 音乐播放状态
     *
     * @param sessionId 会话ID
     * @param isPlaying 是否正在播放音乐
     */
    public void setMusicPlaying(String sessionId, boolean isPlaying) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setMusicPlaying(isPlaying);
        }
    }

    /**
     * 是否在播放音乐
     *
     * @param sessionId 会话ID
     * @return 是否正在播放音乐
     */
    public boolean isMusicPlaying(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isMusicPlaying();
        }
        return false;
    }

    /**
     * 播放状态
     *
     * @param sessionId 会话ID
     * @param isPlaying 是否正在说话
     */
    public void setPlaying(String sessionId, boolean isPlaying) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setPlaying(isPlaying);
        }
    }

    /**
     * 是否在播放音乐
     *
     * @param sessionId 会话ID
     * @return 是否正在播放音乐
     */
    public boolean isPlaying(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isPlaying();
        }
        return false;
    }

    /**
     * 设备状态
     *
     * @param sessionId
     * @param mode  设备状态 auto/realTime
     */
    public void setMode(String sessionId, ListenMode mode) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setMode(mode);
        }
    }

    /**
     * 获取设备状态
     *
     * @param sessionId
     */
    public ListenMode getMode(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getMode();
        }
        return ListenMode.Auto;
    }

    /**
     * 设置流式识别状态
     *
     * @param sessionId   会话ID
     * @param isStreaming 是否正在流式识别
     */
    public void setStreamingState(String sessionId, boolean isStreaming) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setStreamingState(isStreaming);
        }
        updateLastActivity(sessionId); // 更新活动时间
    }

    /**
     * 获取流式识别状态
     *
     * @param sessionId 会话ID
     * @return 是否正在流式识别
     */
    public boolean isStreaming(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.isStreamingState();
        }
        return false;
    }

    /**
     * 创建音频数据流
     *
     * @param sessionId 会话ID
     */
    public void createAudioStream(String sessionId) {
        // 指定缓冲区大小为256个音频帧
        // 假设每帧20ms Opus音频，256帧 = 5.12秒音频缓冲
        // 这样可以应对短暂的网络抖动，同时避免无限缓冲导致OOM
        Sinks.Many<byte[]> sink = Sinks.many().multicast().onBackpressureBuffer(256);
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setAudioSinks(sink);
        }
    }

    /**
     * 获取音频数据流
     *
     * @param sessionId 会话ID
     * @return 音频数据流
     */
    public Sinks.Many<byte[]> getAudioStream(String sessionId) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            return chatSession.getAudioSinks();
        }
        return null;
    }

    /**
     * 发送音频数据
     *
     * @param sessionId 会话ID
     * @param data 音频数据
     */
    public void sendAudioData(String sessionId, byte[] data) {
        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession == null) {
            logger.warn("🚫 会话不存在，无法发送音频 - SessionId: {}", sessionId);
            return;
        }

        Sinks.Many<byte[]> sink = chatSession.getAudioSinks();
        if (sink == null) {
            logger.warn("🚫 音频流未创建，无法发送音频 - SessionId: {}", sessionId);
            return;
        }

        // 检查会话是否正在关闭
        if (chatSession.getAttribute("closing") != null) {
            logger.debug("⏸️ 会话正在关闭，跳过音频发送 - SessionId: {}", sessionId);
            return;
        }

        Sinks.EmitResult result = sink.tryEmitNext(data);
        if (result.isFailure()) {
            logger.warn("⚠️ 音频数据发送失败 - SessionId: {}, Reason: {}", sessionId, result);
            // 根据失败原因处理
            switch (result) {
                case FAIL_OVERFLOW:
                    // 背压溢出，记录错误
                    logger.error("💥 音频流背压溢出 - SessionId: {}, 数据大小: {} bytes", sessionId, data.length);
                    break;
                case FAIL_CANCELLED:
                    // 流已取消，清理资源
                    logger.warn("🛑 音频流已取消 - SessionId: {}", sessionId);
                    closeAudioStream(sessionId);
                    break;
                case FAIL_TERMINATED:
                    // 流已终止，清理资源
                    logger.warn("🔚 音频流已终止 - SessionId: {}", sessionId);
                    closeAudioStream(sessionId);
                    break;
                case FAIL_NON_SERIALIZED:
                    // 并发访问问题
                    logger.error("⚡ 音频流并发访问冲突 - SessionId: {}", sessionId);
                    break;
                case FAIL_ZERO_SUBSCRIBER:
                    // 没有订阅者
                    logger.debug("📭 音频流无订阅者 - SessionId: {}", sessionId);
                    break;
                default:
                    logger.error("❌ 音频数据发送未知错误 - SessionId: {}, Result: {}", sessionId, result);
            }
        }
    }

    /**
     * 完成音频流
     *
     * @param sessionId 会话ID
     */
    public void completeAudioStream(String sessionId) {
        Sinks.Many<byte[]> sink = getAudioStream(sessionId);
        if (sink != null) {
            sink.tryEmitComplete();
        }
    }

    /**
     * 关闭音频流
     *
     * @param sessionId 会话ID
     */
    public void closeAudioStream(String sessionId) {
        Sinks.Many<byte[]> sink = getAudioStream(sessionId);

        ChatSession chatSession = sessions.get(sessionId);
        if (chatSession != null) {
            chatSession.setAudioSinks(null);
        }
    }

    /**
     * 标记设备正在生成验证码
     *
     * @param deviceId 设备ID
     * @return 如果设备之前没有在生成验证码，返回true；否则返回false
     */
    public boolean markCaptchaGeneration(String deviceId) {
        return captchaState.putIfAbsent(deviceId, Boolean.TRUE) == null;
    }

    /**
     * 取消设备验证码生成标记
     *
     * @param deviceId 设备ID
     */
    public void unmarkCaptchaGeneration(String deviceId) {
        captchaState.remove(deviceId);
    }

}
