package cn.naii.iot.communication.common;

import lombok.Data;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备待处理更新队列
 * 用于存储设备离线时的变更操作，待设备上线后再发送给设备
 */
@Data
public class DevicePendingUpdate {
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 待更新的属性
     */
    private Map<String, Object> pendingUpdates = new ConcurrentHashMap<>();
    
    /**
     * 最后更新时间
     */
    private Instant lastUpdatedTime;
    
    /**
     * 更新次数
     */
    private int updateCount = 0;
    
    public DevicePendingUpdate(String deviceId) {
        this.deviceId = deviceId;
        this.lastUpdatedTime = Instant.now();
    }
    
    /**
     * 添加待更新的属性
     * @param key 属性名
     * @param value 属性值
     */
    public void addPendingUpdate(String key, Object value) {
        pendingUpdates.put(key, value);
        lastUpdatedTime = Instant.now();
        updateCount++;
    }
    
    /**
     * 获取并清除待更新的属性
     * @return 待更新的属性映射
     */
    public Map<String, Object> getAndClearPendingUpdates() {
        Map<String, Object> updates = new ConcurrentHashMap<>(pendingUpdates);
        pendingUpdates.clear();
        updateCount = 0;
        lastUpdatedTime = Instant.now();
        return updates;
    }
    
    /**
     * 检查是否有待处理的更新
     * @return 如果有待处理的更新返回true，否则返回false
     */
    public boolean hasPendingUpdates() {
        return !pendingUpdates.isEmpty();
    }
}