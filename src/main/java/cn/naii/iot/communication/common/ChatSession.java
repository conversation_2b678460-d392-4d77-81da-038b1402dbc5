package cn.naii.iot.communication.common;

import cn.naii.iot.communication.domain.iot.IotDescriptor;
import cn.naii.iot.dialogue.llm.memory.Conversation;
import cn.naii.iot.dialogue.llm.tool.ToolsSessionHolder;
import cn.naii.iot.dialogue.llm.tool.mcp.device.DeviceMcpHolder;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.enums.ListenMode;
import cn.naii.iot.utils.AudioUtils;
import lombok.Data;
import org.springframework.ai.tool.ToolCallback;
import reactor.core.Disposable;
import reactor.core.publisher.Sinks;

import java.nio.file.Path;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public abstract class ChatSession {
    /**
     * 当前会话的sessionId
     */
    protected String sessionId;
    /**
     * 设备信息
     */
    protected SysDevice sysDevice;
    /**
     * 设备可用角色列表
     */
    protected List<SysRole> sysRoleList;
    /**
     * 一个Session在某个时刻，只有一个活跃的Conversation。
     * 当切换角色时，Conversation应该释放新建。切换角色一般是不频繁的。
     * -- SETTER --
     *  设置 Conversation，需要与当前活跃角色一致。
     *  当切换角色时，会释放当前 Conversation，并新建一个对应于新角色的Conversation。
     *
     *
     * -- GETTER --
     *  获取与当前活跃角色一致的 Conversation。
     *
     @param conversation
      * @return

     */
    protected Conversation conversation;
    /**
     * 设备iot信息
     */
    protected Map<String, IotDescriptor> iotDescriptors = new HashMap<>();
    /**
     * 当前session的function控制器
     */
    protected ToolsSessionHolder toolsSessionHolder;

    /**
     * 当前语音发送完毕后，是否关闭session
     */
    protected boolean closeAfterChat;
    /**
     * 是否正在播放音乐
     */
    protected boolean musicPlaying;
    /**
     * 是否正在说话
     */
    protected boolean playing;
    /**
     * 设备状态（auto, realTime)
     */
    protected ListenMode mode;
    /**
     * 会话的音频数据流
     */
    protected Sinks.Many<byte[]> audioSinks;
    /**
     * 会话是否正在进行流式识别
     */
    protected boolean streamingState;
    /**
     * 会话的最后用户活动时间 (用户主动操作，如发送消息、音频等)
     */
    protected Instant lastActivityTime;

    /**
     * 最后一次心跳响应时间 (用于检测连接是否存活)
     */
    protected Instant lastHeartbeatResponseTime;

    /**
     * 连续心跳失败次数
     */
    protected AtomicInteger consecutiveHeartbeatFailures = new AtomicInteger(0);

    /**
     * 最后一次接收音频帧的时间 (用于检测音频帧丢失)
     */
    protected Instant lastAudioFrameTime;

    /**
     * 最大允许的连续心跳失败次数 (3次失败 = 90秒无响应)
     */
    public static final int MAX_HEARTBEAT_FAILURES = 3;

    /**
     * 会话属性存储
     */
    protected final ConcurrentHashMap<String, Object> attributes = new ConcurrentHashMap<>();
    /**
     * 存储运行中的流
     */
    protected ConcurrentHashMap<String, Disposable> disposables = new ConcurrentHashMap<>();

    /**
     * 是否流式输出语音
     */
    protected Boolean isStream;

    /**
     * TTS服务
     */
    protected TtsService ttsService;

    // --------------------设备mcp-------------------------
    private DeviceMcpHolder deviceMcpHolder = new DeviceMcpHolder();

    public ChatSession(String sessionId) {
        this.sessionId = sessionId;
        this.lastActivityTime = Instant.now();
        this.lastHeartbeatResponseTime = Instant.now();
    }

    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    public Object getAttribute(String key) {
        return attributes.get(key);
    }

    public void setAssistantTimeMillis(Long assistantTimeMillis) {
        setAttribute("assistantTimeMillis", assistantTimeMillis);
    }

    public Long getAssistantTimeMillis() {
        return (Long) getAttribute("assistantTimeMillis");
    }

    public void setUserTimeMillis(Long userTimeMillis) {
        setAttribute("userTimeMillis", userTimeMillis);
    }

    public Long getUserTimeMillis() {
        return (Long) getAttribute("userTimeMillis");
    }

    /**
     * 音频文件约定路径为：audio/{device-id}/{role-id}/{timestamp}-user.wav
     * {device-id}/{role-id}/{timestamp}-user 能确定唯一性，不会有并发的麻烦。
     * 除非多设备在嵌入式软件里强行修改mac地址（deviceId目前是基于mac地址的)
     * 
     * @param who
     * @return
     */
    private Path getAudioPath(String who, Long timeMillis) {

        Instant instant = Instant.ofEpochMilli(timeMillis).truncatedTo(ChronoUnit.SECONDS);

        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        String datetime = localDateTime.format(DateTimeFormatter.ISO_DATE_TIME).replace(":", "");
        SysDevice device = this.getSysDevice();
        // 判断设备ID是否有不适合路径的特殊字符，它很可能是mac地址需要转换。
        String deviceId = device.getDeviceId().replace(":", "-");
        String roleId = device.getRoleId().toString();
        String filename = "%s-%s.wav".formatted(datetime, who);
        Path path = Path.of(AudioUtils.AUDIO_PATH, deviceId, roleId, filename);
        return path;
    }

    public Path getUserAudioPath() {
        return getAudioPath("user", this.getUserTimeMillis());
    }

    public Path getAssistantAudioPath() {
        return getAudioPath("assistant", getAssistantTimeMillis());
    }

    public ToolsSessionHolder getFunctionSessionHolder() {
        return toolsSessionHolder;
    }

    public void setFunctionSessionHolder(ToolsSessionHolder toolsSessionHolder) {
        this.toolsSessionHolder = toolsSessionHolder;
    }

    public List<ToolCallback> getToolCallbacks() {
        return toolsSessionHolder.getAllFunction();
    }

    /**
     * 会话连接是否打开中
     * 
     * @return
     */
    public abstract boolean isOpen();

    /**
     * 音频通道是否打开可用
     * 
     * @return
     */
    public abstract boolean isAudioChannelOpen();

    public abstract void close();

    public abstract void sendTextMessage(String message);

    public abstract void sendBinaryMessage(byte[] message);

    /**
     * 设置 Conversation，需要与当前活跃角色一致。
     * 当切换角色时，会释放当前 Conversation，并新建一个对应于新角色的Conversation。
     * 
     * @param conversation
     */
    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    /**
     * 获取与当前活跃角色一致的 Conversation。
     * 
     * @return
     */
    public Conversation getConversation() {
        return conversation;
    }

    public ConcurrentHashMap<String, Disposable> getDisposables() {
        return disposables;
    }

    public void setDisposables(ConcurrentHashMap<String, Disposable> disposables) {
        this.disposables = disposables;
    }

    public TtsService getTtsService() {
        return ttsService;
    }

    public void setTtsService(TtsService ttsService) {
        this.ttsService = ttsService;
    }

    public Boolean getStream() {
        return isStream;
    }

    public void setStream(Boolean stream) {
        isStream = stream;
    }
}
