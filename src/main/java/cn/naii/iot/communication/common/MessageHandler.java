package cn.naii.iot.communication.common;

import cn.naii.iot.communication.domain.AbortMessage;
import cn.naii.iot.communication.domain.DeviceMcpMessage;
import cn.naii.iot.communication.domain.GoodbyeMessage;
import cn.naii.iot.communication.domain.IotMessage;
import cn.naii.iot.communication.domain.ListenMessage;
import cn.naii.iot.communication.domain.Message;
import cn.naii.iot.dialogue.llm.factory.ChatModelFactory;
import cn.naii.iot.dialogue.llm.memory.Conversation;
import cn.naii.iot.dialogue.llm.memory.ConversationFactory;
import cn.naii.iot.dialogue.llm.tool.ToolsGlobalRegistry;
import cn.naii.iot.dialogue.llm.tool.ToolsSessionHolder;
import cn.naii.iot.dialogue.service.AudioService;
import cn.naii.iot.dialogue.service.DialogueService;
import cn.naii.iot.dialogue.service.IotService;
import cn.naii.iot.dialogue.service.VadService;
import cn.naii.iot.dialogue.stt.factory.SttServiceFactory;
import cn.naii.iot.dialogue.tts.factory.TtsServiceFactory;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.enums.ListenState;
import cn.naii.iot.service.SysConfigService;
import cn.naii.iot.service.SysDeviceService;
import cn.naii.iot.service.SysRoleService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(MessageHandler.class);

    @Resource
    private SysDeviceService deviceService;

    @Resource
    private AudioService audioService;

    @Resource
    private TtsServiceFactory ttsService;

    @Resource
    private VadService vadService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private SysConfigService configService;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private IotService iotService;

    @Resource
    private TtsServiceFactory ttsFactory;

    @Resource
    private SttServiceFactory sttFactory;

    @Autowired
    private ConversationFactory conversationFactory;

    @Resource
    private ChatModelFactory chatModelFactory;

    @Resource
    private ToolsGlobalRegistry toolsGlobalRegistry;

    @Resource
    private SysRoleService roleService;

    // 用于存储设备ID和验证码生成状态的映射
    private final Map<String, Boolean> captchaGenerationInProgress = new ConcurrentHashMap<>();

    /**
     * 处理连接建立事件.
     *
     * @param chatSession
     * @param deviceIdAuth
     */
    public void afterConnection(ChatSession chatSession, String deviceIdAuth) {
        String deviceId = deviceIdAuth;
        String sessionId = chatSession.getSessionId();
        // 注册会话
        sessionManager.registerSession(sessionId, chatSession);

        SysDevice device = Optional.ofNullable(deviceService.selectDeviceById(deviceId)).orElse(new SysDevice());
        device.setDeviceId(deviceId);
        device.setSessionId(sessionId);
        sessionManager.registerDevice(sessionId, device);

        // 立即设置设备信息，确保会话中包含设备信息
        chatSession.setSysDevice(device);

        // 如果已绑定，则初始化其他内容
        if (!ObjectUtils.isEmpty(device) && device.getRoleId() != null) {
            //这里需要放在虚拟线程外
            ToolsSessionHolder toolsSessionHolder = new ToolsSessionHolder(chatSession.getSessionId(),
                    device, toolsGlobalRegistry);
            chatSession.setFunctionSessionHolder(toolsSessionHolder);
            // 从数据库获取角色描述。device.getRoleId()表示当前设备的当前活跃角色，或者上次退出时的活跃角色。
            SysRole role = roleService.selectRoleById(device.getRoleId());
            Conversation conversation = conversationFactory.initConversation(device, role, sessionId);
            chatSession.setConversation(conversation);

            //以上同步处理结束后，再启动虚拟线程进行设备初始化，确保chatSession中已设置的sysDevice信息
            Thread.startVirtualThread(() -> {
                try {
                    if (role.getSttId() != null) {
                        SysConfig sttConfig = configService.selectConfigById(role.getSttId());
                        if (sttConfig != null) {
                            sttFactory.getSttService(sttConfig);// 提前初始化，加速后续使用
                        }
                    }
                    if (role.getTtsId() != null) {
                        SysConfig ttsConfig = configService.selectConfigById(role.getTtsId());
                        if (ttsConfig != null) {
                            ttsFactory.getTtsService(ttsConfig, role.getVoiceName());// 提前初始化，加速后续使用
                        }
                    }
                    if (role.getModelId() != null) {
                        chatModelFactory.takeChatModel(chatSession);// 提前初始化，加速后续使用
                        // 注册全局函数
                        toolsSessionHolder.registerGlobalFunctionTools(chatSession);
                    }

                    // 更新设备状态为在线
                    deviceService.update(new SysDevice()
                            .setDeviceId(deviceId)
                            .setState(SysDevice.DEVICE_STATE_ONLINE)
                            .setLastLogin(new Date().toString()));
                    // 注意：updateDeviceInfoForSession 已移至 handleHelloMessage 中
                    // 确保在发送hello响应后再发送MCP消息
                    logger.info("📱 设备已上线 - DeviceId: {}, RoleId: {}", deviceId, device.getRoleId());
                } catch (Exception e) {
                    logger.error("❌ 设备初始化失败 - DeviceId: {}", deviceId, e);
                    try {
                        sessionManager.closeSession(sessionId);
                    } catch (Exception ex) {
                        logger.error("❌ 连接关闭失败 - SessionId: {}", sessionId, ex);
                    }
                }
            });

        }
    }


    /**
     * 处理连接关闭事件.
     *
     * @param sessionId
     */
    public void afterConnectionClosed(String sessionId) {
        ChatSession chatSession = sessionManager.getSession(sessionId);
        if (chatSession == null) {
            return;
        }

        // 连接关闭时更新设备信息到数据库
        SysDevice device = sessionManager.getDeviceConfig(sessionId);
        if (device != null && !"web_test".equals(device.getDeviceId())) {
            Thread.startVirtualThread(() -> {
                try {
                    deviceService.update(new SysDevice()
                            .setDeviceId(device.getDeviceId())
                            .setState(SysDevice.DEVICE_STATE_OFFLINE)
                            .setLastLogin(new Date().toString()));
                    logger.info("📴 设备已离线 - DeviceId: {}", device.getDeviceId());
                } catch (Exception e) {
                    logger.error("❌ 设备离线状态更新失败 - DeviceId: {}", device.getDeviceId(), e);
                }
            });
        }
        // 清理会话
        sessionManager.closeSession(sessionId);
    }

    /**
     * 处理音频数据
     *
     * @param sessionId
     * @param opusData
     */
    public void handleBinaryMessage(String sessionId, byte[] opusData) {
        ChatSession chatSession = sessionManager.getSession(sessionId);
        if ((chatSession == null || !chatSession.isOpen()) && !vadService.isSessionInitialized(sessionId)) {
            return;
        }

        // 监控音频帧接收间隔
        if (chatSession != null) {
            Instant now = Instant.now();
            Instant lastFrameTime = chatSession.getLastAudioFrameTime();

            if (lastFrameTime != null) {
                long gapMs = Duration.between(lastFrameTime, now).toMillis();
                // Opus 20ms帧，如果间隔超过50ms可能丢帧或网络抖动
                if (gapMs > 500) {
                    logger.warn("⚠️ 音频帧间隔异常 - SessionId: {}, 间隔: {}ms, 数据大小: {} bytes",
                        sessionId, gapMs, opusData.length);
                }
            }

            chatSession.setLastAudioFrameTime(now);

            // 记录音频接收时间（仅在首次接收时记录）
            if (lastFrameTime == null) {
                dialogueService.markAudioReceived(sessionId);
            }
        }

        // 委托给DialogueService处理音频数据
        dialogueService.processAudioData(chatSession, opusData);

    }

    public void handleUnboundDevice(String sessionId, SysDevice device, String payload) {
        String deviceId;
        if (device == null || device.getDeviceId() == null) {
            logger.error("❌ 设备信息缺失，无法处理未绑定设备 - SessionId: {}", sessionId);
            return;
        }
        deviceId = device.getDeviceId();
        ChatSession chatSession = sessionManager.getSession(sessionId);
        if (chatSession == null || !chatSession.isOpen()) {
            return;
        }
        // 检查是否已经在处理中，使用CAS操作保证线程安全
        Boolean previous = captchaGenerationInProgress.putIfAbsent(deviceId, true);
        if (previous != null && previous) {
            return; // 已经在处理中
        }

        Thread.startVirtualThread(() -> {
            try {
                // 设备已注册但未配置模型
                if (device.getDeviceName() != null && device.getRoleId() == null) {
                    String message = "设备未配置角色，请到角色配置页面完成配置后开始对话";

                    String audioFilePath = ttsService.getDefaultTtsService().textToSpeech(message);
                    audioService.sendAudioMessage(chatSession, new DialogueService.Sentence(message, audioFilePath), true,
                            true);

                    // 延迟一段时间后再解除标记
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    captchaGenerationInProgress.remove(deviceId);
                    return;
                }
                if ("web_test".equals(deviceId)) {
                    // web_test设备，直接朗读payload内容，不生成验证码
                    String message = "你好，我是小智"; // 默认消息

                    // 尝试从payload中提取text字段
                    if (payload != null && !payload.trim().isEmpty()) {
                        try {
                            com.fasterxml.jackson.databind.JsonNode jsonNode =
                                cn.naii.iot.utils.JsonUtil.OBJECT_MAPPER.readTree(payload);

                            // 检查是否有text字段
                            if (jsonNode.has("text")) {
                                String textValue = jsonNode.get("text").asText();
                                if (textValue != null && !textValue.trim().isEmpty()) {
                                    message = textValue;
                                }
                            }
                        } catch (Exception e) {
                            logger.warn("⚠️ 解析payload失败，使用默认消息 - Payload: {}", payload, e);
                            // 如果解析失败，使用整个payload作为消息
                            message = payload;
                        }
                    }

                    String audioFilePath = ttsService.getDefaultTtsService().textToSpeech(message);
                    audioService.sendAudioMessage(chatSession,
                            new DialogueService.Sentence(message, audioFilePath), true, true);

                    logger.info("📢 web_test设备直接朗读 - Message: {}", message);
                } else {
                    // 普通设备，生成验证码
                    SysDevice codeResult = deviceService.generateCode(device);
                    String audioFilePath;
                    if (!StringUtils.hasText(codeResult.getAudioPath())) {
                        String codeMessage = "请到设备管理页面添加设备，输入验证码" + codeResult.getCode();
                        audioFilePath = ttsService.getDefaultTtsService().textToSpeech(codeMessage);
                        codeResult.setDeviceId(deviceId);
                        codeResult.setSessionId(sessionId);
                        codeResult.setAudioPath(audioFilePath);
                        deviceService.updateCode(codeResult);
                    } else {
                        audioFilePath = codeResult.getAudioPath();
                    }

                    audioService.sendAudioMessage(chatSession,
                            new DialogueService.Sentence(codeResult.getCode(), codeResult.getAudioPath()), true, true);
                }

                // 延迟一段时间后再解除标记
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                captchaGenerationInProgress.remove(deviceId);

            } catch (Exception e) {
                logger.error("处理未绑定设备失败", e);
                captchaGenerationInProgress.remove(deviceId);
            }
        });
    }

    private void handleListenMessage(ChatSession chatSession, ListenMessage message) {
        String sessionId = chatSession.getSessionId();
        chatSession.setMode(message.getMode());

        // 根据state处理不同的监听状态
        switch (message.getState()) {
            case ListenState.Start:
                // 开始监听，准备接收音频数据
                logger.info("🎤 开始监听 - SessionId: {}, Mode: {}", sessionId, message.getMode());
                // 初始化VAD会话
                vadService.initSession(sessionId);
                break;

            case ListenState.Stop:
                // 停止监听
                logger.info("🛑 停止监听 - SessionId: {}", sessionId);
                // 关闭音频流
                sessionManager.completeAudioStream(sessionId);
                sessionManager.closeAudioStream(sessionId);
                sessionManager.setStreamingState(sessionId, false);
                // 重置VAD会话
                vadService.resetSession(sessionId);
                break;

            case ListenState.Text:
                // 检测聊天文本输入
                if (audioService.isPlaying(sessionId)) {
                    dialogueService.abortDialogue(chatSession, message.getMode().getValue());
                }
                logger.info("💬 用户文本输入 - SessionId: {}, Text: {}", sessionId, message.getText());
                dialogueService.handleText(chatSession, message.getText(), null);
                break;

            case ListenState.Detect:
                // 检测到唤醒词
                logger.info("👂 唤醒词检测 - SessionId: {}, Text: {}", sessionId, message.getText());
                dialogueService.handleWakeWord(chatSession, message.getText());
                break;

            default:
                logger.warn("⚠️ 未知监听状态 - SessionId: {}, State: {}", sessionId, message.getState());
        }
    }

    private void handleAbortMessage(ChatSession session, AbortMessage message) {
        logger.info("🚫 用户中断对话 - SessionId: {}, Reason: {}", session.getSessionId(), message.getReason());
        dialogueService.abortDialogue(session, message.getReason());
    }

    private void handleIotMessage(ChatSession chatSession, IotMessage message) {
        String sessionId = chatSession.getSessionId();

        // 处理设备描述信息
        if (message.getDescriptors() != null) {
            logger.debug("📋 设备描述信息 - SessionId: {}", sessionId);
            iotService.handleDeviceDescriptors(sessionId, message.getDescriptors());
        }

        // 处理设备状态更新
        if (message.getStates() != null) {
            logger.debug("📊 设备状态更新 - SessionId: {}", sessionId);
            iotService.handleDeviceStates(sessionId, message.getStates());
        }
    }

    private void handleGoodbyeMessage(ChatSession session, GoodbyeMessage message) {
        logger.info("👋 收到goodbye消息 - SessionId: {}, Type: {}", session.getSessionId(), message.getType());
        sessionManager.closeSession(session);
    }

    private void handleDeviceMcpMessage(ChatSession chatSession, DeviceMcpMessage message) {
        logger.debug("📨 MCP消息 - SessionId: {}", chatSession.getSessionId());
        Long mcpRequestId = message.getPayload().getId();
        CompletableFuture<DeviceMcpMessage> future = chatSession.getDeviceMcpHolder().getMcpPendingRequests().get(mcpRequestId);
        if (future != null) {
            future.complete(message);
            chatSession.getDeviceMcpHolder().getMcpPendingRequests().remove(mcpRequestId);
        }
    }

    public void handleMessage(Message msg, String sessionId) {
        var chatSession = sessionManager.getSession(sessionId);

        switch (msg) {
            case ListenMessage m -> handleListenMessage(chatSession, m);
            case IotMessage m -> handleIotMessage(chatSession, m);
            case AbortMessage m -> handleAbortMessage(chatSession, m);
            case GoodbyeMessage m -> handleGoodbyeMessage(chatSession, m);
            case DeviceMcpMessage m -> handleDeviceMcpMessage(chatSession, m);
            default -> {
                logger.warn("⚠️ 未知消息类型 - SessionId: {}, Type: {}", sessionId, msg.getClass().getSimpleName());
            }
        }
    }
}