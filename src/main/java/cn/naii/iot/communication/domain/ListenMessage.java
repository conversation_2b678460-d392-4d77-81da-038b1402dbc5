package cn.naii.iot.communication.domain;

import cn.naii.iot.enums.ListenMode;
import cn.naii.iot.enums.ListenState;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public final class ListenMessage extends Message {
    public ListenMessage(){
        super("listen");
    }

    private ListenState state;
    private ListenMode mode;
    private String text;
}
