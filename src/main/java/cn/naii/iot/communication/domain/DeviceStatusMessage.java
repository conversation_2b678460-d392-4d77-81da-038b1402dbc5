package cn.naii.iot.communication.domain;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备状态消息
 * ESP32设备主动上报设备状态时使用
 */
@Data
@EqualsAndHashCode(callSuper = true)
public final class DeviceStatusMessage extends Message {
    public DeviceStatusMessage() {
        super("device_status");
    }

    /**
     * 设备状态数据（使用JsonNode保持灵活性）
     */
    private JsonNode data;
}

