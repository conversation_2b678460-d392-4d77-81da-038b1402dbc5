package cn.naii.iot.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户设备信息表
 * 
 * <AUTHOR>
 * 
 */
@Data
@Schema(description = "设备信息")
public class DeviceResp{


    @Schema(description = "设备ID")
    private String deviceId;


    @Schema(description = "设备名称")
    private String deviceName;


    @Schema(description = "设备状态")
    private String state;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "最后在线时间")
    private String lastLogin;


    @Schema(description = "WiFi名称")
    private String wifiName;

    @Schema(description = "音量")
    private String volume;

    @Schema(description = "电池状态")
    private String batteryStatus;

    @Schema(description = "信号强度")
    private String signalStrength;
    /**
     * 联网状态
     */
    @Schema(description = "联网状态")
    private String networkStatus;
    @Schema(description = "最后活悦时间")
    private String lastActiveTime;

    @Schema(description = "角色ID")
    private Integer roleId;

    @Schema(description = "音色ID")
    private Integer timbreId;

}