package cn.naii.iot.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户表
 *
 * <AUTHOR>
 */
@Data
public class UserResp {


    private Integer userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "状态 1-正常,0-禁用")
    private String state;

    @Schema(description = "微信openId")
    private String openid;


    @Schema(description = "性别 0-未知,1-男,2-女")
    private Integer gender;

    @Schema(description = "日历类型 0-未知,1-农历,2-阳历")
    private Integer calendarType;


    @Schema(description = "生日")
    private String birthDayTime;

    @Schema(description = "出生地")
    private String birthPlace;
}
