package cn.naii.iot.resp;

import cn.naii.iot.entity.SysRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 管理端-设备表resp
 * 
 * <AUTHOR>
 * 
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({ "startTime", "endTime", "start", "limit", "userId", "code" })
public class SysDeviceResp extends SysRole {


    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "会话ID")
    private String sessionId;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String deviceName;

    /**
     * 设备状态
     */
    @Schema(description = "设备状态")
    private String state;

    /**
     * 设备对话次数
     */
    @Schema(description = "设备对话次数")
    private Integer totalMessage;

    /**
     * 验证码
     */
    @Schema(description = "验证码")
    private String code;

    /**
     * 音频文件
     */
    @Schema(description = "音频文件路径")
    private String audioPath;

    /**
     * 最后在线时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最后在线时间")
    private String lastLogin;

    /**
     * WiFi名称
     */
    @Schema(description = "WiFi名称")
    private String wifiName;

    /**
     * IP
     */
    @Schema(description = "IP地址")
    private String ip;

    /**
     * 芯片型号
     */
    @Schema(description = "芯片型号")
    private String chipModelName;

    /**
     * 芯片类型
     */
    @Schema(description = "芯片类型")
    private String type;

    /**
     * 固件版本
     */
    @Schema(description = "固件版本")
    private String version;

    /**
     * 可用全局function的名称列表(逗号分割)，为空则使用所有全局function
     */
    @Schema(description = "可用全局function的名称列表")
    private String functionNames;

    /**
     * 地理位置
     */
    @Schema(description = "地理位置")
    private String location;
    
    /**
     * 设备音量
     */
    @Schema(description = "设备音量")
    private String volume;
    
    /**
     * 设备电池状态
     */
    @Schema(description = "设备电池状态")
    private String batteryStatus;
    
    /**
     * 信号强度
     */
    @Schema(description = "信号强度")
    private String signalStrength;
    /**
     * 联网状态
     */
    @Schema(description = "联网状态")
    private String networkStatus;


    @Schema(description = "微信openId")
    private String openId;


    @Schema(description = "姓名")
    private String name;


    @Schema(description = "用户名")
    private String userName;


    @Schema(description = "添加设备时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addDeviceTime;


    @Schema(description = "最后活跃时间")
    private String lastActiveTime;

    @Schema(description = "音色名称")
    private String timbreValue;
}