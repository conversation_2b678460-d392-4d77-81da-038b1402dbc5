<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.DeviceMapper">

    <sql id="deviceSql">
        sys_device.deviceId,
        sys_device.deviceName,
        sys_device.ip,
        sys_device.wifiName,
        sys_device.chipModelName,
        sys_device.type,
        sys_device.version,
        sys_device.state,
        sys_device.roleId,
        sys_device.userId,
        sys_device.lastLogin,
        sys_device.createTime,
        sys_device.location,
        sys_device.openId,
        sys_device.volume,
        sys_device.battery_status AS batteryStatus,
        sys_device.signal_strength AS signalStrength,
        sys_device.network_status AS networkStatus,
        sys_device.add_device_time AS addDeviceTime
    </sql>

    <sql id="roleSql">
        sys_role.roleName,
        sys_role.roleDesc,
        sys_role.voiceName,
        sys_role.modelId,
        sys_role.sttId,
        sys_role.ttsId,
        sys_role.vadSpeechTh,
        sys_role.vadSilenceTh,
        sys_role.vadEnergyTh,
        sys_role.vadSilenceMs
    </sql>

    <select id="query" resultType="cn.naii.iot.entity.SysDevice">
        SELECT
        <include refid="deviceSql"/>,
        <include refid="roleSql"/>,
        (SELECT COUNT(*) FROM sys_message WHERE sys_message.deviceId = sys_device.deviceId AND sys_message.state = '1')
        AS totalMessage,
        sys_timbre.value as timbreValue
        FROM
        sys_device
        LEFT JOIN sys_role ON sys_device.roleId = sys_role.roleId
        LEFT JOIN sys_timbre ON sys_role.timbre_id = sys_timbre.id
        WHERE
        1 = 1
        <if test="userId == -1">AND sys_device.userId is not null</if>
        <if test="userId != null and userId != -1">AND sys_device.userId = #{userId}</if>
        <if test="deviceId != null and deviceId != ''">AND deviceId like concat('%', #{deviceId},'%')</if>
        <if test="deviceName != null and deviceName != ''">AND deviceName LIKE CONCAT('%', #{deviceName}, '%')</if>
        <if test="roleName != null and roleName != ''">AND roleName LIKE CONCAT('%', #{roleName}, '%')</if>
        <if test="state != null and state != ''">AND sys_device.state = #{state}</if>
        <if test="timbreValue != null and timbreValue != ''">AND sys_timbre.value like concat('%',#{timbreValue},'%')</if>
    </select>

    <select id="queryExactly" resultType="cn.naii.iot.entity.SysDevice">
        SELECT
        <include refid="deviceSql"/>,
        <include refid="roleSql"/>,
        (SELECT COUNT(*) FROM sys_message WHERE sys_message.deviceId = sys_device.deviceId AND sys_message.state = '1')
        AS totalMessage,
        sys_timbre.value as timbreValue
        FROM
        sys_device
        LEFT JOIN sys_role ON sys_device.roleId = sys_role.roleId
        LEFT JOIN sys_timbre ON sys_role.timbre_id = sys_timbre.id
        WHERE
        1 = 1
        <if test="userId == -1">AND sys_device.userId is not null</if>
        <if test="userId != null and userId != -1">AND sys_device.userId = #{userId}</if>
        <if test="deviceId != null and deviceId != ''">AND deviceId = #{deviceId}</if>
        <if test="deviceName != null and deviceName != ''">AND deviceName LIKE CONCAT('%', #{deviceName}, '%')</if>
        <if test="roleName != null and roleName != ''">AND roleName LIKE CONCAT('%', #{roleName}, '%')</if>
        <if test="state != null and state != ''">AND sys_device.state = #{state}</if>
        <if test="timbreValue != null and timbreValue != ''">AND sys_timbre.value like concat('%',#{timbreValue},'%')</if>
    </select>

    <select id="selectDeviceById" resultType="cn.naii.iot.entity.SysDevice">
        SELECT
        <include refid="deviceSql"/>
        FROM
        sys_device
        WHERE
        deviceId = #{deviceId}
    </select>

    <select id="queryVerifyCode" parameterType="cn.naii.iot.entity.SysDevice" resultType="cn.naii.iot.entity.SysDevice">
        SELECT
        code, audioPath, deviceId, type
        FROM
        sys_code
        WHERE
        1 = 1
        <if test="deviceId != null and deviceId != ''">AND deviceId = #{deviceId}</if>
        <if test="sessionId != null and sessionId != ''">AND sessionId = #{sessionId}</if>
        <if test="code != null and code != ''">AND code = #{code}</if>
        <if test="createTime != null">AND createTime &gt;= #{createTime}</if>
        <if test="createTime == null">AND createTime &gt;= DATE_SUB(NOW(),INTERVAL 10 MINUTE)</if>
        ORDER BY createTime DESC
        LIMIT 1
    </select>

    <update id="updateCode" parameterType="cn.naii.iot.entity.SysDevice">
        UPDATE
            sys_code
        SET audioPath = #{audioPath}
        WHERE deviceId = #{deviceId}
          AND sessionId = #{sessionId}
          AND code = #{code}
    </update>

    <insert id="generateCode" parameterType="cn.naii.iot.entity.SysDevice">
        <selectKey keyProperty="code" order="BEFORE" resultType="java.lang.String">
            SELECT LPAD(FLOOR(RAND() * 1000000), 6, '0') as code
        </selectKey>
        INSERT INTO sys_code (deviceId, sessionId, type, code, createTime)
        VALUES (#{deviceId}, #{sessionId}, #{type}, #{code}, NOW())
    </insert>

    <insert id="insertCode">
        INSERT INTO sys_code (deviceId, type, code, createTime)
        VALUES (#{deviceId}, 'access_token', #{code}, NOW())
    </insert>

    <update id="update" parameterType="cn.naii.iot.entity.SysDevice">
        UPDATE
        sys_device
        <set>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="deviceName != null and deviceName != ''">deviceName = #{deviceName},</if>
            <if test="wifiName != null and wifiName != ''">wifiName = #{wifiName},</if>
            <if test="chipModelName != null and chipModelName != ''">chipModelName = #{chipModelName},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="version != null and version != ''">version = #{version},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="lastLogin != null and lastLogin != ''">lastLogin = NOW(),</if>
            <if test="roleId != null and roleId != ''">roleId = #{roleId},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="volume != null and volume != ''">volume = #{volume},</if>
            <if test="batteryStatus != null and batteryStatus != ''">battery_status = #{batteryStatus},</if>
            <if test="signalStrength != null and signalStrength != ''">signal_strength = #{signalStrength},</if>
            <if test="networkStatus != null and networkStatus != ''">network_status = #{networkStatus},</if>
        </set>
        WHERE
        1 = 1
        <if test="userId != null and userId != ''">AND userId = #{userId}</if>
        <if test="deviceId != null and deviceId != ''">AND deviceId = #{deviceId}</if>
    </update>

    <insert id="add" useGeneratedKeys="true" keyProperty="deviceName" parameterType="cn.naii.iot.entity.SysDevice">
        INSERT INTO sys_device (deviceId, deviceName, type, userId, roleId, volume, battery_status, signal_strength,
                                network_status)
        VALUES (#{deviceId},
                #{deviceName},
                #{type},
                #{userId},
                #{roleId},
                #{volume},
                #{batteryStatus},
                #{signalStrength},
                #{networkStatus})
    </insert>


    <delete id="delete" parameterType="cn.naii.iot.entity.SysDevice">
        DELETE
        FROM sys_device
        WHERE deviceId = #{deviceId}
          AND userId = #{userId}
    </delete>

    <update id="delDevice" parameterType="cn.naii.iot.request.DelDeviceRequest">
        UPDATE sys_device
        set openId=null,
            userId=null,
            add_device_time=null
        where deviceId = #{deviceId}
          and openId = #{openId}
          and userId = #{userId}
    </update>


    <update id="bindDevice">
        UPDATE sys_device
        set openid=#{openId},
            userid=#{userId},
            add_device_time = now()
        where deviceId = #{deviceId}
    </update>


    <select id="selectDeviceList" resultType="cn.naii.iot.resp.DeviceResp">
        SELECT
        <include refid="deviceSql"/>,
        sys_role.timbre_id as timbreId
        FROM sys_device LEFT JOIN sys_role on sys_device.roleid = sys_role.roleid
        WHERE sys_device.openid = #{openId}
        <if test="searchName != null and searchName != ''">
            and sys_device.deviceName LIKE CONCAT('%', #{searchName}, '%')
        </if>
    </select>

    <select id="selectDevice" resultType="cn.naii.iot.entity.SysDevice">
        SELECT
        <include refid="deviceSql"/>
        FROM sys_device WHERE 1=1

        <if test="deviceId != null and deviceId != ''">
            and deviceId = #{deviceId}
        </if>
        <if test="openId != null and openId != ''">
            and openid = #{openId}
        </if>
    </select>

    <update id="updateTimbre" parameterType="cn.naii.iot.request.UpTimbreRequest">
        UPDATE sys_device
        set roleid = #{roleId}
        where deviceId = #{deviceId}
          and userid = #{userId}
          and openid = #{openId}
    </update>


    <select id="countDevice" resultType="int">
        SELECT count(1) FROM sys_device
        where 1=1
        <if test="state != null and state != ''">
            and state = #{state}
        </if>
        <if test="userId != null and userId != ''">
            and userid = #{userId}
        </if>
    </select>

    <select id="countDeviceByDate" resultType="cn.naii.iot.dto.CountDTO">
        SELECT
            DATE(add_device_time) as stat_date,
            COUNT(*) as count_value
        FROM sys_device
        WHERE add_device_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE(add_device_time)
        HAVING COUNT(*) > 0;
    </select>
    <update id="updateTimbreByDeviceId" parameterType="cn.naii.iot.request.UpTimbreRequest">
        UPDATE sys_role AS r
            JOIN sys_device AS d ON r.roleid  = d.roleid
            JOIN sys_timbre AS t ON t.id = #{timbreId}
        SET r.timbre_id = #{timbreId},r.voicename = t.speaker_id
        WHERE d.deviceid =  #{deviceId};
    </update>
</mapper>