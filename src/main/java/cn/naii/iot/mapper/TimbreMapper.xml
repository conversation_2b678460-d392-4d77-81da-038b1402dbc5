<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.TimbreMapper">

    <select id="select" resultType="cn.naii.iot.entity.SysTimbre">
        SELECT
            id,
            label,
            value,
            provider,
            gender,
            model_id as modelId,
            speaker_id as speakerId
        FROM
            sys_timbre
        <where>
            <if test="value != null and value.trim() != ''">AND value LIKE CONCAT('%',#{value},'%') </if>
            <if test="provider != null and provider.trim() != ''">AND provider = #{provider}</if>
        </where>
    </select>
    <select id="selectById" resultType="cn.naii.iot.entity.SysTimbre">
        SELECT
            id,
            label,
            value,
            provider,
            gender,
            model_id as modelId,
            speaker_id as speakerId
        FROM
            sys_timbre
        where
            id = #{id}
    </select>

</mapper>