<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.TemplateMapper">
    
    <sql id="templateSql">
        sys_template.userId, sys_template.templateId, sys_template.templateName, sys_template.templateDesc, sys_template.templateContent, sys_template.category, sys_template.isDefault, sys_template.state, sys_template.createTime, sys_template.updateTime
    </sql>
    
    <select id="query" resultType="cn.naii.iot.entity.SysTemplate">
        SELECT
        <include refid="templateSql"/>
        FROM
            sys_template
        WHERE
            sys_template.state = 1
            AND sys_template.userId = #{userId}
            <if test="templateName != null and templateName != ''">
                AND template_name like concat('%', #{templateName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        ORDER BY isDefault, createTime DESC
    </select>
    
    <select id="selectTemplateById" parameterType="Integer" resultType="cn.naii.iot.entity.SysTemplate">
        SELECT
        <include refid="templateSql"/>
        FROM
            sys_template
        WHERE templateId = #{templateId}
    </select>
    
    <insert id="add" parameterType="cn.naii.iot.entity.SysTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into sys_template (
            userId,
            templateName,
            templateDesc,
            templateContent,
            category,
            isDefault,
            state,
            createTime,
            updateTime
        ) values (
            #{userId},
            #{templateName},
            #{templateDesc},
            #{templateContent},
            #{category},
            #{isDefault},
            #{state},
            now(),
            now()
        )
    </insert>

    <update id="update" parameterType="cn.naii.iot.entity.SysTemplate">
        UPDATE sys_template
        <set>
            <if test="templateName != null">templateName = #{templateName},</if>
            <if test="templateDesc != null">templateDesc = #{templateDesc},</if>
            <if test="templateContent != null">templateContent = #{templateContent},</if>
            <if test="category != null">category = #{category},</if>
            <if test="isDefault != null">isDefault = #{isDefault},</if>
            <if test="state != null">state = #{state},</if>
        </set>
        WHERE templateId = #{templateId} and userId = #{userId}
    </update>
    
    <update id="resetDefault" parameterType="cn.naii.iot.entity.SysTemplate">
        UPDATE sys_template SET isDefault = '0' WHERE isDefault = 1 AND userId = #{userId}
    </update>

    
</mapper>