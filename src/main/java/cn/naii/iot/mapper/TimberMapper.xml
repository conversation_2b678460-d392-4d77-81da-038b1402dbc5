<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.TimberMapper">


    <select id="selectTimbreById" resultType="cn.naii.iot.dto.SysTimbreDTO">
        SELECT
            st.id as timbreId,
            st.label,
            st.value,
            st.provider,
            st.gender,
            st.model_id as modelId,
            sr.roleid as roleId
        FROM
            sys_timbre st
                left join sys_role sr on st.id =sr.timbre_id
        WHERE
            st.model_id = #{modelId}
    </select>

</mapper>