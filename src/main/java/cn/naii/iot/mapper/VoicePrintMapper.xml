<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.VoicePrintMapper">

    <!-- 结果映射 -->
    <resultMap id="VoicePrintResultMap" type="cn.naii.iot.entity.VoicePrint">
        <id column="id" property="id"/>
        <result column="wx_user_id" property="wxUserId"/>
        <result column="user_name" property="userName"/>
        <result column="nick_name" property="nickName"/>
        <result column="birth" property="birth"/>
        <result column="birth_place" property="birthPlace"/>
        <result column="voice_print" property="voicePrint"/>
        <result column="gender" property="gender"/>
        <result column="status" property="status"/>
        <result column="mobile" property="mobile"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">voiceprint_user</sql>

    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        id,
        wx_user_id,
        user_name,
        nick_name,
        birth,
        birth_place,
        voice_print,
        gender,
        status,
        mobile,
        create_time,
        update_time,
        user_id,
        start_time,
        end_time,
        last_login_time
    </sql>

    <!-- 插入语句 -->
    <insert id="insert" parameterType="cn.naii.iot.entity.VoicePrint" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wxUserId != null and wxUserId != ''">wx_user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="birth != null">birth,</if>
            <if test="birthPlace != null">birth_place,</if>
            <if test="voicePrint != null and voicePrint != ''">voice_print,</if>
            <if test="gender != null">gender,</if>
            <if test="status != null">status,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="wxUserId != null and wxUserId != ''">#{wxUserId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="birth != null">#{birth},</if>
            <if test="birthPlace != null">#{birthPlace},</if>
            <if test="voicePrint != null and voicePrint != ''">#{voicePrint},</if>
            <if test="gender != null">#{gender},</if>
            <if test="status != null">#{status},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
        </trim>
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM <include refid="tableName"/> WHERE id = #{id}
    </delete>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="cn.naii.iot.entity.VoicePrint">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="wxUserId != null and wxUserId != ''">wx_user_id = #{wxUserId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="birth != null">birth = #{birth},</if>
            <if test="birthPlace != null">birth_place = #{birthPlace},</if>
            <if test="voicePrint != null and voicePrint != ''">voice_print = #{voicePrint},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="status != null">status = #{status},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="long" resultMap="VoicePrintResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id}
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="VoicePrintResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
    </select>

    <!-- 条件查询 -->
    <select id="selectByCondition" parameterType="cn.naii.iot.entity.VoicePrint" resultMap="VoicePrintResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        <where>
            <if test="id != null">AND id = #{id}</if>
            <if test="wxUserId != null and wxUserId != ''">AND wx_user_id = #{wxUserId}</if>
            <if test="userName != null and userName != ''">AND user_name = #{userName}</if>
            <if test="nickName != null and nickName != ''">AND nick_name = #{nickName}</if>
            <if test="birth != null">AND birth = #{birth}</if>
            <if test="birthPlace != null">AND birth_place = #{birthPlace}</if>
            <if test="voicePrint != null and voicePrint != ''">AND voice_print = #{voicePrint}</if>
            <if test="gender != null">AND gender = #{gender}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="mobile != null and mobile != ''">AND mobile = #{mobile}</if>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="startTime != null">AND create_time >= #{startTime}</if>
            <if test="endTime != null">AND create_time &lt;= #{endTime}</if>
            <if test="lastLoginTime != null">AND last_login_time = #{lastLoginTime}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询 -->
    <select id="selectPage" parameterType="map" resultMap="VoicePrintResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        <where>
            <if test="voicePrint != null and voicePrint.userName != null and voicePrint.userName != ''">
                AND user_name LIKE CONCAT('%', #{voicePrint.userName}, '%')
            </if>
            <if test="voicePrint != null and voicePrint.mobile != null and voicePrint.mobile != ''">
                AND mobile LIKE CONCAT('%', #{voicePrint.mobile}, '%')
            </if>
            <if test="voicePrint != null and voicePrint.status != null">
                AND status = #{voicePrint.status}
            </if>
            <if test="voicePrint != null and voicePrint.startTime != null">
                AND create_time >= #{voicePrint.startTime}
            </if>
            <if test="voicePrint != null and voicePrint.endTime != null">
                AND create_time &lt;= #{voicePrint.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询总数 -->
    <select id="countByCondition" parameterType="cn.naii.iot.entity.VoicePrint" resultType="int">
        SELECT COUNT(*) FROM <include refid="tableName"/>
        <where>
            <if test="userName != null and userName != ''">AND user_name LIKE CONCAT('%', #{userName}, '%')</if>
            <if test="mobile != null and mobile != ''">AND mobile LIKE CONCAT('%', #{mobile}, '%')</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="startTime != null">AND create_time >= #{startTime}</if>
            <if test="endTime != null">AND create_time &lt;= #{endTime}</if>
        </where>
    </select>

</mapper>
