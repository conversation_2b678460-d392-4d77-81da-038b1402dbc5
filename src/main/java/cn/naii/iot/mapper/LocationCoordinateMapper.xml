<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.LocationCoordinateMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="cn.naii.iot.entity.LocationCoordinate">
        <id column="id" property="id"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 表字段 -->
    <sql id="Base_Column_List">
        id, province, city, county, longitude, latitude, create_time
    </sql>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM location_coordinate
        ORDER BY id DESC
    </select>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM location_coordinate
        WHERE id = #{id}
    </select>

    <!-- 根据条件查询 -->
    <select id="selectByCondition" parameterType="cn.naii.iot.entity.LocationCoordinate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM location_coordinate
        <where>
            <if test="province != null and province != ''">
                province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="county != null and county != ''">
                AND county = #{county}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="cn.naii.iot.entity.LocationCoordinate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO location_coordinate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="county != null">county,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            create_time,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="county != null">#{county},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            NOW(),
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="cn.naii.iot.entity.LocationCoordinate">
        UPDATE location_coordinate
        <set>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="county != null">county = #{county},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM location_coordinate WHERE id = #{id}
    </delete>

</mapper>
