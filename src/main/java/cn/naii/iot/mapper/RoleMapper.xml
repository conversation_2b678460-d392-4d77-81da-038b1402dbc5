<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.RoleMapper">

    <sql id="roleSql">
        sys_role.roleId, sys_role.avatar, sys_role.roleName, sys_role.roleDesc, sys_role.voiceName,
        sys_role.modelId, sys_role.sttId, sys_role.ttsId,
        sys_role.vadSpeechTh, sys_role.vadSilenceTh, sys_role.vadEnergyTh, sys_role.vadSilenceMs,
        sys_role.userId, sys_role.state, sys_role.isDefault, sys_role.createTime
    </sql>

    <sql id="modelSql">
        model_config.configId AS modelId, model_config.configName AS modelName, model_config.provider AS modelProvider
    </sql>

    <sql id="sttSql">
        stt_config.configId AS sttId
    </sql>

    <sql id="ttsSql">
        tts_config.configId AS ttsId, tts_config.provider AS ttsProvider, tts_config.appId, tts_config.apiKey, tts_config.apiSecret, tts_config.ak, tts_config.sk
    </sql>

    <select id="query" resultType="cn.naii.iot.entity.SysRole">
        SELECT
        <include refid="roleSql"></include>,
        <include refid="modelSql"></include>,
        <include refid="sttSql"></include>,
        <include refid="ttsSql"></include>,
        (SELECT COUNT(*) FROM sys_device WHERE sys_device.roleId = sys_role.roleId) AS totalDevice
        FROM
            sys_role
            LEFT JOIN sys_config tts_config ON sys_role.ttsId = tts_config.configId AND tts_config.configType = 'tts'
            LEFT JOIN sys_config model_config ON sys_role.modelId = model_config.configId AND model_config.configType = 'llm'
            LEFT JOIN sys_config stt_config ON sys_role.sttId = stt_config.configId AND stt_config.configType = 'stt'
        WHERE
            sys_role.state = 1
            <if test="userId != null and userId != ''">AND sys_role.userId = #{userId}</if>
            <if test="roleId != null and roleId != ''">AND sys_role.roleId = #{roleId}</if>
            <if test="roleName != null and roleName != ''">AND sys_role.roleName LIKE CONCAT('%', #{roleName}, '%')</if>
            <if test="isDefault != null and isDefault != ''">AND sys_role.isDefault = #{isDefault}</if>
    </select>

    <select id="queryList" resultType="cn.naii.iot.resp.SysRoleResp">
        SELECT
        <include refid="roleSql"></include>,
        <include refid="modelSql"></include>,
        <include refid="sttSql"></include>,
        <include refid="ttsSql"></include>,
        (SELECT COUNT(*) FROM sys_device WHERE sys_device.roleId = sys_role.roleId) AS totalDevice,
        sys_timbre.value as timbreValue,
        sys_role.timbre_id as timbreId
        FROM
        sys_role
        LEFT JOIN sys_config tts_config ON sys_role.ttsId = tts_config.configId AND tts_config.configType = 'tts'
        LEFT JOIN sys_config model_config ON sys_role.modelId = model_config.configId AND model_config.configType = 'llm'
        LEFT JOIN sys_config stt_config ON sys_role.sttId = stt_config.configId AND stt_config.configType = 'stt'
        LEFT JOIN sys_timbre on sys_role.timbre_id = sys_timbre.id
        WHERE
        sys_role.state = 1
        <if test="userId != null and userId != ''">AND sys_role.userId = #{userId}</if>
        <if test="roleId != null and roleId != ''">AND sys_role.roleId = #{roleId}</if>
        <if test="roleName != null and roleName != ''">AND sys_role.roleName LIKE CONCAT('%', #{roleName}, '%')</if>
        <if test="isDefault != null and isDefault != ''">AND sys_role.isDefault = #{isDefault}</if>
    </select>

    <update id="update" parameterType="cn.naii.iot.entity.SysRole">
        UPDATE
            sys_role
        <set>
            avatar = #{avatar},
            <if test="roleName != null and roleName != ''">roleName = #{roleName},</if>
            <if test="roleDesc != null and roleDesc != ''">roleDesc = #{roleDesc},</if>
            <if test="voiceName != null and voiceName != ''">voiceName = #{voiceName},</if>
            <if test="isDefault != null and isDefault != ''">isDefault = #{isDefault},</if>
            <if test="modelId != null and modelId != ''">modelId = #{modelId},</if>
            <if test="timbreId != null and timbreId != ''">timbre_id = #{timbreId},</if>
            <if test="ttsId != null and ttsId != ''">
                <choose>
                    <when test="ttsId == -1">ttsId = null,</when>
                    <otherwise>ttsId = #{ttsId},</otherwise>
                </choose>
            </if>
            <if test="sttId != null and sttId != ''">
                <choose>
                    <when test="sttId == -1">sttId = null,</when>
                    <otherwise>sttId = #{sttId},</otherwise>
                </choose>
            </if>
            <if test="state != null and state != ''">state = #{state},</if>
        </set>
        WHERE
            roleId = #{roleId}
    </update>

    <update id="resetDefault" parameterType="cn.naii.iot.entity.SysRole">
        UPDATE
            sys_role
        <set>
            isDefault = '0'
        </set>
        WHERE
            userId = #{userId}
    </update>

    <insert id="add" useGeneratedKeys="true" keyProperty="roleId" parameterType="cn.naii.iot.entity.SysRole">
        INSERT INTO sys_role ( avatar, roleName, roleDesc, voiceName, modelId, ttsId, sttId, userId, isDefault,state,timbre_id,createtime ) VALUES (
            #{avatar},
            #{roleName},
            #{roleDesc},
            #{voiceName},
            #{modelId},
            <choose>
                <when test="ttsId == -1">null</when>
                <otherwise>#{ttsId}</otherwise>
            </choose>,
            <choose>
                <when test="sttId == -1">null</when>
                <otherwise>#{sttId}</otherwise>
            </choose>,
            #{userId},
            #{isDefault},
            #{state},
            #{timbreId},
            #{createTime}
        )
    </insert>

    <select id="selectRoleById" resultType="cn.naii.iot.entity.SysRole">
        SELECT
        <include refid="roleSql"></include>
        FROM
            sys_role
        WHERE
            roleId = #{roleId}
    </select>

</mapper>