<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.UserMapper">

    <sql id="userSql">
        sys_user.userId,
                       sys_user.username,
                       sys_user.`name`,
                       sys_user.tel,
                       sys_user.email,
                       sys_user.avatar,
                       sys_user.password,
                       sys_user.state,
                       sys_user.isAdmin,
                       sys_user.loginIp,
                       sys_user.loginTime,
                       sys_user.createTime,
                       sys_user.openid,
                       sys_user.nickname,
                       sys_user.gender,
                       sys_user.birth_place as birthPlace,
                       sys_user.birth_day_time as birthDayTime,
                       sys_user.calendar_type as calendarType
    </sql>

    <select id="selectUserByUsername" resultType="cn.naii.iot.entity.SysUser">
        SELECT
        <include refid="userSql"></include>
        FROM
            sys_user
        WHERE
            username = #{username}
    </select>

    <select id="selectUserByEmail" resultType="cn.naii.iot.entity.SysUser">
        SELECT
        <include refid="userSql"></include>
        FROM
            sys_user
        WHERE
            email = #{email}
    </select>

    <select id="query" resultType="cn.naii.iot.entity.SysUser">
        SELECT
        <include refid="userSql"></include>,
        (
            SELECT count(*) FROM sys_device WHERE sys_device.userId = sys_user.userId
        ) AS totalDevice,
        (
            SELECT
                count(*)
            FROM
                sys_message
                LEFT JOIN sys_device ON sys_device.deviceId = sys_message.deviceId
            WHERE
                sys_device.userId = sys_user.userId
                <if test="startTime != null">
                    AND sys_message.createTime &gt;= #{startTime} AND sys_message.createTime &lt;= #{endTime}
                </if>
        ) AS totalMessage,
        (
            SELECT count(*) FROM sys_device WHERE sys_device.userId = sys_user.userId
        ) AS aliveNumber
        FROM
            sys_user
        WHERE
            username = #{username}
    </select>

    <select id="queryUsers" parameterType="cn.naii.iot.entity.SysUser" resultType="cn.naii.iot.entity.SysUser">
        SELECT
            sys_user.userId,
            sys_user.`name`,
            <!-- 电话号码脱敏：显示前3位和后4位，中间用****替代 -->
            CASE 
                WHEN LENGTH(sys_user.tel) &gt; 7 THEN 
                    CONCAT(LEFT(sys_user.tel, 3), '****', RIGHT(sys_user.tel, 4))
                ELSE sys_user.tel
            END AS tel,
            <!-- 邮箱脱敏：显示@前面的前3位和@后面的所有内容，中间用***替代 -->
            CASE 
                WHEN sys_user.email LIKE '%@%' THEN 
                    CONCAT(
                        LEFT(sys_user.email, LEAST(3, LOCATE('@', sys_user.email) - 1)),
                        '***',
                        SUBSTRING(sys_user.email, LOCATE('@', sys_user.email))
                    )
                ELSE sys_user.email
            END AS email,
            sys_user.avatar,
            sys_user.state,
            sys_user.isAdmin,
            sys_user.loginIp,
            sys_user.loginTime,
            sys_user.createTime,
            sys_user.nickname,
        (
            SELECT count(*) FROM sys_device WHERE sys_device.userId = sys_user.userId
        ) AS totalDevice,
        (
            SELECT
                count(*)
            FROM
                sys_message
                JOIN sys_device ON sys_device.deviceId = sys_message.deviceId
            WHERE
                sys_device.userId = sys_user.userId
                <if test="startTime != null">
                    AND sys_message.createTime &gt;= #{startTime} AND sys_message.createTime &lt;= #{endTime}
                </if>
        ) AS totalMessage,
        (
            SELECT count(*) FROM sys_device WHERE sys_device.userId = sys_user.userId and sys_device.state = 1
        ) AS aliveNumber
        FROM
            sys_user
        WHERE
            1 = 1
            <if test="email != null and email != ''">AND sys_user.email = #{email}</if>
            <if test="tel != null and tel != ''">AND sys_user.tel = #{tel}</if>
            <if test="name != null and name != ''">AND name LIKE CONCAT('%', #{name}, '%')</if>
        ORDER BY createTime DESC
    </select>


    <select id="selectUserByUserId" resultType="cn.naii.iot.entity.SysUser">
        SELECT
        <include refid="userSql"></include>
        FROM
            sys_user
        WHERE
            userId = #{userId}
    </select>

    <insert id="add" parameterType="cn.naii.iot.entity.SysUser">
        INSERT INTO sys_user ( username, `name`, tel, email, password, state, isAdmin,openid)
        VALUES
            (#{username}, #{name}, #{tel}, #{email}, #{password}, '1', '0',#{openid})
    </insert>

    <update id="update" parameterType="cn.naii.iot.entity.SysUser">
        UPDATE
            sys_user
        <set>
            <if test="name != null and name != ''">`name` = #{name},</if>
            <if test="tel != null and tel != ''">tel = #{tel},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="state != null and state != ''">state = ${state},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="calendarType != null and calendarType != ''">calendar_type = #{calendarType},</if>
            <if test="birthDayTime != null">birth_day_time = #{birthDayTime},</if>
            <if test="birthPlace != null and birthPlace != ''">birth_place = #{birthPlace},</if>
        </set>

        <where>
            <choose>
                <when test="userId != null">
                    userId = #{userId}
                </when>
                <otherwise>
                    username = #{username}
                    <if test="email != null and email != ''">AND email = #{email}</if>
                </otherwise>
            </choose>
        </where>
    </update>

    <insert id="generateCode" parameterType="cn.naii.iot.entity.SysUser">
        <selectKey keyProperty="code" order="BEFORE" resultType="java.lang.String">
            SELECT LPAD(FLOOR(RAND() * 1000000), 6, '0') as code
        </selectKey>
        INSERT INTO sys_code (email, code, createTime)
        VALUES (#{email}, #{code}, NOW())
    </insert>

    <select id="queryCaptcha" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            sys_code
        WHERE
            code = #{code}
            AND email = #{email}
            AND createTime &gt;= DATE_SUB(NOW(),INTERVAL 10 MINUTE)
            ORDER BY createTime
            DESC
            LIMIT 1
    </select>

    <select id="selectUserByOpenId" resultType="cn.naii.iot.entity.SysUser">
        select
        <include refid="userSql"></include>
        from sys_user
        where openid = #{openid} LIMIT 1
    </select>

</mapper>