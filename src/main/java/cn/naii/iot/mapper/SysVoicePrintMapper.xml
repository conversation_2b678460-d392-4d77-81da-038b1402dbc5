<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.naii.iot.dao.SysVoicePrintMapper">
  <resultMap id="BaseResultMap" type="cn.naii.iot.entity.SysVoicePrint">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voice_name" jdbcType="VARCHAR" property="voiceName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_voice_print
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="cn.naii.iot.entity.SysVoicePrint">
    insert into sys_voice_print ( voice_name, user_id,
      createtime, updatetime)
    values (#{voiceName,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{createtime,jdbcType=TIMESTAMP}, #{updatetime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.naii.iot.entity.SysVoicePrint">
    update sys_voice_print
    set voice_name = #{voiceName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      updatetime = #{updatetime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select id, voice_name, user_id, createtime, updatetime
    from sys_voice_print
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, voice_name, user_id, createtime, updatetime
    from sys_voice_print
  </select>
  <select id="select" resultMap="BaseResultMap">
    SELECT
    id,
    voice_name,
    user_id,
    createtime,
    updatetime
    FROM
    sys_voice_print
    <where>
      <if test="voiceName != null and voiceName.trim() != ''">AND voice_name LIKE CONCAT('%',#{voiceName},'%') </if>
      <if test="userId != null and userId.trim() != ''">AND user_id = #{userId}</if>
    </where>
  </select>
</mapper>