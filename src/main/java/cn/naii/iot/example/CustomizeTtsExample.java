package cn.naii.iot.example;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.dialogue.tts.factory.TtsServiceFactory;
import cn.naii.iot.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自定义TTS服务使用示例
 * 
 * 演示如何在实际项目中集成和使用CustomizeTtsService
 * 包括配置、调用和错误处理的最佳实践
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Component
public class CustomizeTtsExample {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomizeTtsExample.class);
    
    @Autowired
    private TtsServiceFactory ttsServiceFactory;
    
    /**
     * 示例1：基本的流式TTS使用
     */
    public void basicStreamingTtsExample() {
        logger.info("🎬 开始基本流式TTS示例");
        
        try {
            // 1. 配置TTS服务
            SysConfig config = createTtsConfig();
            String voiceName = "zh-CN-XiaoyiNeural";
            
            // 2. 获取TTS服务实例
            TtsService ttsService = ttsServiceFactory.getTtsService(config, voiceName);
            
            // 3. 准备测试文本
            String text = "欢迎使用自定义WebSocket流式TTS服务！这是一个演示如何集成实时语音合成功能的示例。";
            
            // 4. 创建简单的回调处理器
            StreamTtsCallback callback = new StreamTtsCallback() {
                private final AtomicInteger chunkCount = new AtomicInteger(0);
                private long startTime = System.currentTimeMillis();
                
                @Override
                public void onStart() {
                    startTime = System.currentTimeMillis();
                    logger.info("🎬 TTS流式处理开始");
                }
                
                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    int chunk = chunkCount.incrementAndGet();
                    logger.info("📦 接收音频块 #{} - 大小: {} bytes", chunk, audioChunk.length);
                    
                    // 在这里处理音频数据
                    processAudioChunk(audioChunk, chunk);
                }
                
                @Override
                public void onComplete() {
                    long duration = System.currentTimeMillis() - startTime;
                    logger.info("✅ TTS处理完成 - 总块数: {}, 耗时: {}ms", 
                        chunkCount.get(), duration);
                }
                
                @Override
                public void onError(Throwable error) {
                    logger.error("❌ TTS处理失败", error);
                }
            };
            
            // 5. 执行流式TTS
            ttsService.streamTextToSpeech(text, callback);
            
        } catch (Exception e) {
            logger.error("基本流式TTS示例执行失败", e);
        }
    }
    
    /**
     * 示例2：异步流式TTS处理
     */
    public CompletableFuture<Void> asyncStreamingTtsExample(String text) {
        logger.info("🎬 开始异步流式TTS示例 - 文本长度: {}", text.length());
        
        return CompletableFuture.runAsync(() -> {
            try {
                SysConfig config = createTtsConfig();
                TtsService ttsService = ttsServiceFactory.getTtsService(config, "zh-CN-XiaoyiNeural");
                
                // 创建异步回调处理器
                StreamTtsCallback callback = new AsyncAudioProcessor();
                
                ttsService.streamTextToSpeech(text, callback);
                
            } catch (Exception e) {
                logger.error("异步流式TTS处理失败", e);
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 示例3：批量文本TTS处理
     */
    public void batchTtsExample(String[] texts) {
        logger.info("🎬 开始批量TTS处理 - 文本数量: {}", texts.length);
        
        try {
            SysConfig config = createTtsConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "zh-CN-XiaoyiNeural");
            
            for (int i = 0; i < texts.length; i++) {
                final int index = i;
                final String text = texts[i];
                
                logger.info("📝 处理文本 #{}: {}", index + 1, text);
                
                StreamTtsCallback callback = new StreamTtsCallback() {
                    @Override
                    public void onStart() {
                        logger.info("🎬 开始处理文本 #{}", index + 1);
                    }
                    
                    @Override
                    public void onAudioChunk(byte[] audioChunk) throws Exception {
                        // 为每个文本添加标识
                        processAudioChunkWithId(audioChunk, index);
                    }
                    
                    @Override
                    public void onComplete() {
                        logger.info("✅ 文本 #{} 处理完成", index + 1);
                    }
                    
                    @Override
                    public void onError(Throwable error) {
                        logger.error("❌ 文本 #{} 处理失败", index + 1, error);
                    }
                };
                
                ttsService.streamTextToSpeech(text, callback);
                
                // 添加间隔避免服务器过载
                Thread.sleep(100);
            }
            
        } catch (Exception e) {
            logger.error("批量TTS处理失败", e);
        }
    }
    
    /**
     * 示例4：与WebSocket客户端集成
     */
    public void webSocketIntegrationExample(String sessionId, String text) {
        logger.info("🎬 开始WebSocket集成示例 - 会话: {}", sessionId);
        
        try {
            SysConfig config = createTtsConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "zh-CN-XiaoyiNeural");
            
            // 创建WebSocket集成回调
            StreamTtsCallback callback = new WebSocketTtsCallback(sessionId);
            
            ttsService.streamTextToSpeech(text, callback);
            
        } catch (Exception e) {
            logger.error("WebSocket集成示例失败", e);
        }
    }
    
    /**
     * 创建TTS配置
     */
    private SysConfig createTtsConfig() {
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        
        // 从环境变量或配置文件读取
        config.setApiUrl(System.getProperty("tts.websocket.url", "ws://localhost:8080"));
        config.setApiKey(System.getProperty("tts.api.key", "demo-api-key"));
        
        return config;
    }
    
    /**
     * 处理音频块数据
     */
    private void processAudioChunk(byte[] audioChunk, int chunkIndex) {
        // 示例处理逻辑
        logger.debug("处理音频块 #{} - 大小: {} bytes", chunkIndex, audioChunk.length);
        
        // 1. 可以保存到文件
        // saveAudioChunk(audioChunk, chunkIndex);
        
        // 2. 可以发送到音频播放器
        // audioPlayer.playChunk(audioChunk);
        
        // 3. 可以发送到WebSocket客户端
        // webSocketSession.sendBinary(audioChunk);
        
        // 4. 可以添加到音频缓冲区
        // audioBuffer.add(audioChunk);
    }
    
    /**
     * 带ID的音频块处理
     */
    private void processAudioChunkWithId(byte[] audioChunk, int textIndex) {
        logger.debug("处理文本 #{} 的音频块 - 大小: {} bytes", textIndex, audioChunk.length);
        
        // 可以根据textIndex进行不同的处理
        // 例如：保存到不同的文件、发送到不同的客户端等
    }
    
    /**
     * 异步音频处理器
     */
    private static class AsyncAudioProcessor implements StreamTtsCallback {
        private final AtomicInteger chunkCount = new AtomicInteger(0);
        
        @Override
        public void onStart() {
            logger.info("🎬 异步音频处理开始");
        }
        
        @Override
        public void onAudioChunk(byte[] audioChunk) throws Exception {
            int chunk = chunkCount.incrementAndGet();
            
            // 异步处理音频数据
            CompletableFuture.runAsync(() -> {
                try {
                    // 模拟音频处理
                    Thread.sleep(10);
                    logger.debug("📦 异步处理音频块 #{}", chunk);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        @Override
        public void onComplete() {
            logger.info("✅ 异步音频处理完成 - 总块数: {}", chunkCount.get());
        }
        
        @Override
        public void onError(Throwable error) {
            logger.error("❌ 异步音频处理失败", error);
        }
    }
    
    /**
     * WebSocket TTS回调处理器
     */
    private static class WebSocketTtsCallback implements StreamTtsCallback {
        private final String sessionId;
        private final AtomicInteger chunkCount = new AtomicInteger(0);
        
        public WebSocketTtsCallback(String sessionId) {
            this.sessionId = sessionId;
        }
        
        @Override
        public void onStart() {
            logger.info("🎬 WebSocket TTS开始 - 会话: {}", sessionId);
            // 发送开始消息到客户端
            // webSocketManager.sendMessage(sessionId, "tts_start", null);
        }
        
        @Override
        public void onAudioChunk(byte[] audioChunk) throws Exception {
            int chunk = chunkCount.incrementAndGet();
            logger.debug("📦 发送音频块到WebSocket - 会话: {}, 块: #{}", sessionId, chunk);
            
            // 发送音频数据到WebSocket客户端
            // webSocketManager.sendBinary(sessionId, audioChunk);
        }
        
        @Override
        public void onComplete() {
            logger.info("✅ WebSocket TTS完成 - 会话: {}, 总块数: {}", sessionId, chunkCount.get());
            // 发送完成消息到客户端
            // webSocketManager.sendMessage(sessionId, "tts_complete", Map.of("chunks", chunkCount.get()));
        }
        
        @Override
        public void onError(Throwable error) {
            logger.error("❌ WebSocket TTS失败 - 会话: {}", sessionId, error);
            // 发送错误消息到客户端
            // webSocketManager.sendMessage(sessionId, "tts_error", Map.of("error", error.getMessage()));
        }
    }
}
