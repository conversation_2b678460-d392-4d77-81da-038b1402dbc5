package cn.naii.iot.dao;

import cn.naii.iot.entity.SysRole;
import cn.naii.iot.resp.SysRoleResp;

import java.util.List;

/**
 * 角色管理 数据层
 * 
 * <AUTHOR>
 * 
 */
public interface RoleMapper {
  List<SysRole> query(SysRole role);

  List<SysRoleResp> queryList(SysRole role);

  int update(SysRole role);

  int resetDefault(SysRole role);

  int add(SysRole role);

  SysRole selectRoleById(Integer roleId);
}