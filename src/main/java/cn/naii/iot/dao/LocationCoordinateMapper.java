package cn.naii.iot.dao;

import cn.naii.iot.entity.LocationCoordinate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 地理位置坐标 Mapper 接口
 */
@Mapper
public interface LocationCoordinateMapper {
    
    /**
     * 查询所有记录
     */
    List<LocationCoordinate> selectAll();
    
    /**
     * 根据ID查询
     */
    LocationCoordinate selectById(Long id);
    
    /**
     * 根据条件查询
     */
    List<LocationCoordinate> selectByCondition(LocationCoordinate locationCoordinate);
    
    /**
     * 插入记录
     */
    int insert(LocationCoordinate locationCoordinate);
    
    /**
     * 更新记录
     */
    int update(LocationCoordinate locationCoordinate);
    
    /**
     * 删除记录
     */
    int deleteById(Long id);
}