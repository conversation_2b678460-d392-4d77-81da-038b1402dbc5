package cn.naii.iot.dao;

import cn.naii.iot.entity.VoicePrint;

import java.util.List;
import java.util.Map;

/**
 * 用户声纹信息 Mapper 接口
 */
public interface VoicePrintMapper {

    /**
     * 插入用户声纹信息
     *
     * @param voicePrint 用户声纹实体对象
     * @return 影响的行数
     */
    int insert(VoicePrint voicePrint);

    /**
     * 根据ID删除用户声纹信息
     *
     * @param id 用户声纹ID
     * @return 影响的行数
     */
    int deleteById(Long id);

    /**
     * 根据ID更新用户声纹信息
     *
     * @param voicePrint 用户声纹实体对象
     * @return 影响的行数
     */
    int updateById(VoicePrint voicePrint);

    /**
     * 根据ID查询用户声纹信息
     *
     * @param id 用户声纹ID
     * @return 用户声纹实体对象
     */
    VoicePrint selectById(Long id);

    /**
     * 查询所有用户声纹信息
     *
     * @return 用户声纹实体对象列表
     */
    List<VoicePrint> selectAll();

    /**
     * 条件查询用户声纹信息
     *
     * @param voicePrint 查询条件对象
     * @return 用户声纹实体对象列表
     */
    List<VoicePrint> selectByCondition(VoicePrint voicePrint);

    /**
     * 分页查询用户声纹信息
     *
     * @param params 查询参数Map，包含offset、limit和voicePrint条件对象
     * @return 用户声纹实体对象列表
     */
    List<VoicePrint> selectPage(Map<String, Object> params);

    /**
     * 统计符合条件的用户声纹信息数量
     *
     * @param voicePrint 查询条件对象
     * @return 记录总数
     */
    int countByCondition(VoicePrint voicePrint);
}