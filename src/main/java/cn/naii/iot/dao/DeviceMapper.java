package cn.naii.iot.dao;

import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.request.DelDeviceRequest;
import cn.naii.iot.request.UpTimbreRequest;
import cn.naii.iot.resp.DeviceResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息 数据层
 *
 * <AUTHOR>
 */
public interface DeviceMapper {
    List<SysDevice> query(SysDevice device);

    SysDevice selectDeviceById(String deviceId);

    int generateCode(SysDevice device);

    SysDevice queryVerifyCode(SysDevice device);

    int updateCode(SysDevice device);

    int update(SysDevice device);

    int add(SysDevice device);

    int delete(SysDevice device);

    int insertCode(String deviceId, String code);


    SysDevice selectDevice(@Param("deviceId") String deviceId,
                           @Param("openId") String openId);

    int delDevice(DelDeviceRequest request);

    List<DeviceResp> selectDeviceList(@Param("openId") String openId,
                                      @Param("searchName") String searchName);

    int bindDevice(@Param("openId") String openId,
                   @Param("deviceId") String deviceId,
                   @Param("userId") String userId);


    int updateTimbre(UpTimbreRequest device);
}