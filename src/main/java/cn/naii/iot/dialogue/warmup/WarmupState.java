package cn.naii.iot.dialogue.warmup;

import lombok.Data;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;

/**
 * 保温语音状态
 * 
 * <AUTHOR>
 * @since 2025-10-19
 */
@Data
public class WarmupState {
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 当前状态
     */
    private WarmupStatus status;
    
    /**
     * 保温语音类型(ok/hmm/listening/processing)
     */
    private String audioType;
    
    /**
     * 开始时间
     */
    private Instant startTime;
    
    /**
     * 结束时间
     */
    private Instant endTime;
    
    /**
     * 发送任务Future
     */
    private CompletableFuture<Void> sendFuture;
    
    /**
     * 是否应该中断(volatile保证可见性)
     */
    private volatile boolean shouldInterrupt;
    
    /**
     * 构造函数
     */
    public WarmupState(String sessionId) {
        this.sessionId = sessionId;
        this.status = WarmupStatus.IDLE;
        this.shouldInterrupt = false;
    }
    
    /**
     * 是否正在播放或发送中
     */
    public boolean isActive() {
        return status == WarmupStatus.PENDING || 
               status == WarmupStatus.SENDING || 
               status == WarmupStatus.PLAYING;
    }
    
    /**
     * 是否可以中断
     */
    public boolean canInterrupt() {
        return isActive();
    }
}

