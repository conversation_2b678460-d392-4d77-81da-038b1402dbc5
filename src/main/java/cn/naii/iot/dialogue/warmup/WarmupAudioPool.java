package cn.naii.iot.dialogue.warmup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 保温音频池 - 预加载和管理保温语音文件
 * 
 * <AUTHOR>
 * @since 2025-10-19
 */
@Component
public class WarmupAudioPool {
    private static final Logger logger = LoggerFactory.getLogger(WarmupAudioPool.class);
    
    private static final String WARMUP_AUDIO_DIR = "audio/warmup/";
    
    // 保温音频类型
    public static final String TYPE_OK = "ok";
    public static final String TYPE_HMM = "hmm";
    public static final String TYPE_LISTENING = "listening";
    public static final String TYPE_PROCESSING = "processing";
    
    // 音频缓存: audioType -> byte[] (完整的Opus文件数据)
    private final Map<String, byte[]> audioCache = new ConcurrentHashMap<>();
    
    // 音频类型列表
    private final List<String> audioTypes = Arrays.asList(
        TYPE_OK, TYPE_HMM, TYPE_LISTENING, TYPE_PROCESSING
    );
    
    // 随机选择器
    private final Random random = new Random();
    
    @PostConstruct
    public void initialize() {
        logger.info("🎙️ 初始化保温音频池...");
        
        try {
            // 确保目录存在
            Path warmupDir = Paths.get(WARMUP_AUDIO_DIR);
            if (!Files.exists(warmupDir)) {
                Files.createDirectories(warmupDir);
                logger.warn("⚠️ 保温音频目录不存在,已创建: {}", WARMUP_AUDIO_DIR);
                logger.warn("⚠️ 请将保温音频文件放入该目录: ok.opus, hmm.opus, listening.opus, processing.opus");
                return;
            }
            
            // 加载所有保温音频
            for (String type : audioTypes) {
                loadAudio(type);
            }
            
            if (audioCache.isEmpty()) {
                logger.warn("⚠️ 未加载任何保温音频文件,保温功能将不可用");
            } else {
                logger.info("✅ 保温音频池初始化完成 - 已加载 {} 个音频", audioCache.size());
            }
            
        } catch (Exception e) {
            logger.error("❌ 保温音频池初始化失败", e);
        }
    }
    
    /**
     * 加载单个保温音频
     */
    private void loadAudio(String type) {
        try {
            String fileName = type + ".opus";
            Path audioPath = Paths.get(WARMUP_AUDIO_DIR, fileName);
            
            if (!Files.exists(audioPath)) {
                logger.debug("保温音频文件不存在: {}", audioPath);
                return;
            }
            
            // 读取Opus文件
            byte[] opusData = Files.readAllBytes(audioPath);
            
            if (opusData.length == 0) {
                logger.warn("⚠️ 保温音频文件为空: {}", audioPath);
                return;
            }
            
            audioCache.put(type, opusData);
            
            logger.info("✅ 已加载保温音频: {} - {} bytes", type, opusData.length);
            
        } catch (IOException e) {
            logger.error("❌ 加载保温音频失败: {}", type, e);
        }
    }
    
    /**
     * 获取随机保温音频
     */
    public byte[] getRandomAudio() {
        if (audioCache.isEmpty()) {
            logger.warn("⚠️ 保温音频池为空");
            return null;
        }
        
        String type = getRandomType();
        return getAudio(type);
    }
    
    /**
     * 获取指定类型的保温音频
     */
    public byte[] getAudio(String type) {
        byte[] audio = audioCache.get(type);
        if (audio == null) {
            logger.warn("⚠️ 保温音频不存在: {}", type);
            return null;
        }
        return audio;
    }
    
    /**
     * 获取随机音频类型
     */
    public String getRandomType() {
        if (audioCache.isEmpty()) {
            return null;
        }
        
        List<String> availableTypes = new ArrayList<>(audioCache.keySet());
        return availableTypes.get(random.nextInt(availableTypes.size()));
    }
    
    /**
     * 检查是否有可用的保温音频
     */
    public boolean hasAudio() {
        return !audioCache.isEmpty();
    }
    
    /**
     * 获取所有可用的音频类型
     */
    public Set<String> getAvailableTypes() {
        return audioCache.keySet();
    }
}

