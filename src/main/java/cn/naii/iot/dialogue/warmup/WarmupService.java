package cn.naii.iot.dialogue.warmup;

import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.dialogue.service.AudioService;
import cn.naii.iot.dialogue.service.MessageService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 保温服务 - 管理保温语音的发送和中断
 * 
 * 核心功能:
 * 1. 在STT开始前发送简短的保温语音,给用户即时反馈
 * 2. 如果STT快速完成,自动取消或中断保温语音
 * 3. 如果用户再次说话,立即中断保温语音
 * 
 * <AUTHOR>
 * @since 2025-10-19
 */
@Service
public class WarmupService {
    private static final Logger logger = LoggerFactory.getLogger(WarmupService.class);
    
    @Resource
    private WarmupAudioPool audioPool;
    
    @Resource
    private AudioService audioService;
    
    @Resource
    private MessageService messageService;
    
    @Resource
    private SessionManager sessionManager;
    
    // 保温功能开关
    @Value("${warmup.enabled:false}")
    private boolean warmupEnabled;
    
    // 保温延迟(毫秒)
    @Value("${warmup.delay.ms:300}")
    private long warmupDelayMs;
    
    // 保温音频最大播放时长(毫秒)
    @Value("${warmup.max.duration.ms:2000}")
    private long maxDurationMs;
    
    // 是否允许中断
    @Value("${warmup.allow.interrupt:true}")
    private boolean allowInterrupt;
    
    // 保温音频类型(空表示随机)
    @Value("${warmup.audio.type:}")
    private String warmupAudioType;
    
    // 会话保温状态
    private final Map<String, WarmupState> sessionStates = new ConcurrentHashMap<>();
    
    // 调度器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2,
        Thread.ofVirtual().name("warmup-scheduler-", 0).factory());
    
    /**
     * 触发保温语音
     * 
     * @param session WebSocket会话
     * @return 保温任务Future
     */
    public CompletableFuture<Void> triggerWarmup(ChatSession session) {
        if (!warmupEnabled) {
            logger.debug("保温功能未启用");
            return CompletableFuture.completedFuture(null);
        }
        
        if (!audioPool.hasAudio()) {
            logger.warn("⚠️ 没有可用的保温音频");
            return CompletableFuture.completedFuture(null);
        }
        
        String sessionId = session.getSessionId();
        
        // 创建或获取保温状态
        WarmupState state = sessionStates.computeIfAbsent(sessionId, WarmupState::new);
        
        // 如果已经有保温任务在进行,跳过
        if (state.isActive()) {
            logger.debug("会话已有保温任务在进行 - SessionId: {}", sessionId);
            return CompletableFuture.completedFuture(null);
        }
        
        // 重置状态
        state.setStatus(WarmupStatus.PENDING);
        state.setStartTime(Instant.now());
        state.setShouldInterrupt(false);
        
        // 选择保温音频类型
        String audioType = warmupAudioType != null && !warmupAudioType.isEmpty() 
            ? warmupAudioType 
            : audioPool.getRandomType();
        state.setAudioType(audioType);
        
        logger.info("🎙️ 触发保温语音 - SessionId: {}, Type: {}", sessionId, audioType);

        // 立即发送保温语音（不延迟）
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                // 检查是否应该取消
                if (state.isShouldInterrupt()) {
                    logger.info("⏸️ 保温语音已取消 - SessionId: {}", sessionId);
                    state.setStatus(WarmupStatus.CANCELLED);
                    return;
                }

                // 立即发送保温语音
                sendWarmupAudio(session, state);

            } catch (Exception e) {
                logger.error("❌ 保温语音发送失败 - SessionId: {}", sessionId, e);
                state.setStatus(WarmupStatus.FAILED);
            }
        }, scheduler);
        
        state.setSendFuture(future);

        // 设置超时清理（保温语音最多播放maxDurationMs）
        scheduler.schedule(() -> {
            if (state.isActive()) {
                logger.warn("⏰ 保温语音超时,强制清理 - SessionId: {}", sessionId);
                interruptWarmup(sessionId);
            }
        }, maxDurationMs, TimeUnit.MILLISECONDS);

        return future;
    }
    
    /**
     * 发送保温音频
     * 注意: 这里直接发送完整的Opus文件,由AudioService处理帧分割和定时发送
     */
    private void sendWarmupAudio(ChatSession session, WarmupState state) throws Exception {
        String sessionId = state.getSessionId();
        String audioType = state.getAudioType();
        
        // 获取音频数据
        byte[] opusData = audioPool.getAudio(audioType);
        if (opusData == null || opusData.length == 0) {
            logger.warn("⚠️ 保温音频为空 - Type: {}", audioType);
            state.setStatus(WarmupStatus.FAILED);
            return;
        }
        
        state.setStatus(WarmupStatus.SENDING);
        
        logger.info("📤 开始发送保温语音 - SessionId: {}, Size: {} bytes", sessionId, opusData.length);
        
        // 检查是否应该中断
        if (state.isShouldInterrupt()) {
            logger.info("⏸️ 保温语音在发送前被中断 - SessionId: {}", sessionId);
            state.setStatus(WarmupStatus.INTERRUPTED);
            return;
        }
        
        state.setStatus(WarmupStatus.PLAYING);
        
        // 发送TTS开始消息
        messageService.sendTtsMessage(session, null, "start");
        
        // 直接发送完整的Opus数据
        // 注意: 这里简化处理,直接发送整个文件
        // 实际应该按帧发送,但为了简化先这样实现
        messageService.sendBinaryMessage(session, opusData);
        
        // 检查是否被中断
        if (state.isShouldInterrupt()) {
            logger.info("⏸️ 保温语音在播放中被中断 - SessionId: {}", sessionId);
            state.setStatus(WarmupStatus.INTERRUPTED);
            // 发送停止消息
            messageService.sendTtsMessage(session, null, "stop");
            return;
        }
        
        // 发送完成
        state.setStatus(WarmupStatus.COMPLETED);
        state.setEndTime(Instant.now());
        
        logger.info("✅ 保温语音发送完成 - SessionId: {}", sessionId);
        
        // 发送停止消息
        messageService.sendTtsMessage(session, null, "stop");
        
        // 延迟清理状态
        scheduler.schedule(() -> cleanupState(sessionId), 1, TimeUnit.SECONDS);
    }
    
    /**
     * 中断保温语音
     * 
     * @param sessionId 会话ID
     */
    public void interruptWarmup(String sessionId) {
        if (!allowInterrupt) {
            logger.debug("保温语音不允许中断");
            return;
        }
        
        WarmupState state = sessionStates.get(sessionId);
        if (state == null || !state.canInterrupt()) {
            return;
        }
        
        logger.info("🛑 中断保温语音 - SessionId: {}, Status: {}", sessionId, state.getStatus());
        
        // 标记应该中断
        state.setShouldInterrupt(true);
        
        // 取消发送任务
        CompletableFuture<Void> future = state.getSendFuture();
        if (future != null && !future.isDone()) {
            future.cancel(true);
        }
        
        // 更新状态
        if (state.getStatus() == WarmupStatus.PENDING) {
            state.setStatus(WarmupStatus.CANCELLED);
        } else {
            state.setStatus(WarmupStatus.INTERRUPTED);
        }
        
        // 清理状态
        scheduler.schedule(() -> cleanupState(sessionId), 500, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 检查是否正在播放保温语音
     */
    public boolean isWarmupPlaying(String sessionId) {
        WarmupState state = sessionStates.get(sessionId);
        return state != null && state.isActive();
    }

    /**
     * 获取保温状态
     */
    public WarmupStatus getWarmupStatus(String sessionId) {
        WarmupState state = sessionStates.get(sessionId);
        return state != null ? state.getStatus() : WarmupStatus.IDLE;
    }

    /**
     * 等待保温语音完成
     * 如果保温语音正在播放，等待其完成后再返回
     *
     * @param sessionId 会话ID
     * @param maxWaitMs 最大等待时间（毫秒）
     * @return true-保温已完成或不存在，false-等待超时
     */
    public boolean waitForWarmupComplete(String sessionId, long maxWaitMs) {
        WarmupState state = sessionStates.get(sessionId);
        if (state == null || !state.isActive()) {
            return true; // 没有保温任务或已完成
        }

        CompletableFuture<Void> future = state.getSendFuture();
        if (future == null) {
            return true;
        }

        try {
            logger.debug("⏳ 等待保温语音完成 - SessionId: {}, MaxWait: {}ms", sessionId, maxWaitMs);
            future.get(maxWaitMs, TimeUnit.MILLISECONDS);
            logger.debug("✅ 保温语音已完成 - SessionId: {}", sessionId);
            return true;
        } catch (TimeoutException e) {
            logger.warn("⏰ 等待保温语音超时 - SessionId: {}", sessionId);
            return false;
        } catch (Exception e) {
            logger.error("❌ 等待保温语音失败 - SessionId: {}", sessionId, e);
            return false;
        }
    }
    
    /**
     * 清理会话状态
     */
    public void cleanupState(String sessionId) {
        WarmupState state = sessionStates.remove(sessionId);
        if (state != null) {
            logger.debug("🧹 清理保温状态 - SessionId: {}, FinalStatus: {}", sessionId, state.getStatus());
        }
    }
    
    /**
     * 清理所有状态
     */
    public void cleanupAll() {
        sessionStates.clear();
        logger.info("🧹 清理所有保温状态");
    }
}

