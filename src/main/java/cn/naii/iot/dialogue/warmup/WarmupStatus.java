package cn.naii.iot.dialogue.warmup;

/**
 * 保温语音状态枚举
 * 
 * <AUTHOR>
 * @since 2025-10-19
 */
public enum WarmupStatus {
    /**
     * 空闲状态 - 无保温任务
     */
    IDLE,
    
    /**
     * 等待发送 - 延迟期间
     */
    PENDING,
    
    /**
     * 正在发送 - 发送音频帧中
     */
    SENDING,
    
    /**
     * 正在播放 - 设备正在播放
     */
    PLAYING,
    
    /**
     * 播放完成 - 正常完成
     */
    COMPLETED,
    
    /**
     * 被中断 - STT完成或用户再次说话
     */
    INTERRUPTED,
    
    /**
     * 已取消 - 延迟期间被取消
     */
    CANCELLED,
    
    /**
     * 发送失败 - 网络异常等
     */
    FAILED
}

