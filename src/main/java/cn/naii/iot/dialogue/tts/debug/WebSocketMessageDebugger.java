package cn.naii.iot.dialogue.tts.debug;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * WebSocket 消息调试工具
 * 用于详细分析 WebSocket 消息内容，帮助排查协议问题
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class WebSocketMessageDebugger {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketMessageDebugger.class);
    
    /**
     * 调试文本消息
     */
    public static void debugTextMessage(String message, String context) {
        logger.info("🔍 [{}] 文本消息调试", context);
        logger.info("📝 消息内容: [{}]", message);
        logger.info("📏 消息长度: {} 字符", message.length());
        
        // 字符编码分析
        byte[] utf8Bytes = message.getBytes(StandardCharsets.UTF_8);
        logger.info("📊 UTF-8 字节长度: {} bytes", utf8Bytes.length);
        
        // 十六进制表示
        StringBuilder hex = new StringBuilder();
        StringBuilder ascii = new StringBuilder();
        
        for (int i = 0; i < message.length() && i < 100; i++) { // 限制前100个字符
            char c = message.charAt(i);
            hex.append(String.format("%04x ", (int) c));
            
            if (c >= 32 && c <= 126) {
                ascii.append(c);
            } else {
                ascii.append('.');
            }
        }
        
        logger.info("🔢 十六进制 (前100字符): {}", hex.toString());
        logger.info("📄 ASCII 表示 (前100字符): [{}]", ascii.toString());
        
        // 检查特殊字符
        if (message.contains("\n")) {
            logger.info("⚠️ 包含换行符");
        }
        if (message.contains("\r")) {
            logger.info("⚠️ 包含回车符");
        }
        if (message.contains("\t")) {
            logger.info("⚠️ 包含制表符");
        }
        
        // 检查是否为已知控制信号
        checkKnownSignals(message);
    }
    
    /**
     * 调试二进制消息
     */
    public static void debugBinaryMessage(byte[] data, String context) {
        logger.info("🔍 [{}] 二进制消息调试", context);
        logger.info("📦 数据长度: {} bytes", data.length);
        
        if (data.length == 0) {
            logger.warn("⚠️ 空的二进制数据");
            return;
        }
        
        // 前几个字节的十六进制表示
        int previewBytes = Math.min(32, data.length);
        StringBuilder hex = new StringBuilder();
        StringBuilder ascii = new StringBuilder();
        
        for (int i = 0; i < previewBytes; i++) {
            int b = data[i] & 0xFF;
            hex.append(String.format("%02x ", b));
            
            if (b >= 32 && b <= 126) {
                ascii.append((char) b);
            } else {
                ascii.append('.');
            }
        }
        
        logger.info("🔢 十六进制 (前{}字节): {}", previewBytes, hex.toString());
        logger.info("📄 ASCII 表示 (前{}字节): [{}]", previewBytes, ascii.toString());
        
        // 检查是否可能是音频数据
        analyzeAudioData(data);
        
        // 检查是否可能是文本数据
        analyzeTextData(data);
    }
    
    /**
     * 检查已知的控制信号
     */
    private static void checkKnownSignals(String message) {
        String[] knownSignals = {
            "__STREAM_COMPLETE__",
            "__CONNECTION_CLOSING__", 
            "__END_STREAM__",
            "__ERROR__:"
        };
        
        for (String signal : knownSignals) {
            if (message.equals(signal)) {
                logger.info("✅ 识别为已知控制信号: {}", signal);
                return;
            } else if (message.startsWith(signal)) {
                logger.info("✅ 识别为已知控制信号前缀: {}", signal);
                String payload = message.substring(signal.length());
                logger.info("📄 信号载荷: [{}]", payload);
                return;
            }
        }
        
        logger.warn("⚠️ 未识别的控制信号");
    }
    
    /**
     * 分析音频数据特征
     */
    private static void analyzeAudioData(byte[] data) {
        // 检查 WAV 文件头
        if (data.length >= 12) {
            String header = new String(Arrays.copyOfRange(data, 0, 4), StandardCharsets.US_ASCII);
            if ("RIFF".equals(header)) {
                logger.info("🎵 检测到 WAV 文件头");
                
                String format = new String(Arrays.copyOfRange(data, 8, 12), StandardCharsets.US_ASCII);
                if ("WAVE".equals(format)) {
                    logger.info("🎵 确认为 WAV 格式");
                    analyzeWavHeader(data);
                }
                return;
            }
        }
        
        // 检查是否为 PCM 数据
        if (isPossiblePcmData(data)) {
            logger.info("🎵 可能是 PCM 音频数据");
        }
        
        // 统计数据分布
        analyzeDataDistribution(data);
    }
    
    /**
     * 分析 WAV 文件头
     */
    private static void analyzeWavHeader(byte[] data) {
        if (data.length < 44) {
            logger.warn("⚠️ WAV 数据太短，无法分析完整头部");
            return;
        }
        
        try {
            // 文件大小
            int fileSize = bytesToInt(data, 4) + 8;
            logger.info("📊 WAV 文件大小: {} bytes", fileSize);
            
            // 查找 fmt chunk
            int pos = 12;
            while (pos < data.length - 8) {
                String chunkId = new String(Arrays.copyOfRange(data, pos, pos + 4), StandardCharsets.US_ASCII);
                int chunkSize = bytesToInt(data, pos + 4);
                
                if ("fmt ".equals(chunkId)) {
                    if (pos + 8 + chunkSize <= data.length) {
                        analyzeFmtChunk(data, pos + 8, chunkSize);
                    }
                    break;
                }
                
                pos += 8 + chunkSize;
            }
            
        } catch (Exception e) {
            logger.warn("⚠️ WAV 头部分析失败: {}", e.getMessage());
        }
    }
    
    /**
     * 分析 fmt chunk
     */
    private static void analyzeFmtChunk(byte[] data, int offset, int size) {
        if (size < 16) {
            logger.warn("⚠️ fmt chunk 太小");
            return;
        }
        
        try {
            int audioFormat = bytesToShort(data, offset);
            int numChannels = bytesToShort(data, offset + 2);
            int sampleRate = bytesToInt(data, offset + 4);
            int byteRate = bytesToInt(data, offset + 8);
            int blockAlign = bytesToShort(data, offset + 12);
            int bitsPerSample = bytesToShort(data, offset + 14);
            
            logger.info("🎵 音频格式: {}", audioFormat == 1 ? "PCM" : "其他(" + audioFormat + ")");
            logger.info("🎵 声道数: {}", numChannels);
            logger.info("🎵 采样率: {} Hz", sampleRate);
            logger.info("🎵 字节率: {} bytes/sec", byteRate);
            logger.info("🎵 块对齐: {}", blockAlign);
            logger.info("🎵 位深度: {} bits", bitsPerSample);
            
        } catch (Exception e) {
            logger.warn("⚠️ fmt chunk 分析失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查是否可能是文本数据
     */
    private static void analyzeTextData(byte[] data) {
        try {
            String text = new String(data, StandardCharsets.UTF_8);
            
            // 检查是否包含可打印字符
            int printableCount = 0;
            for (char c : text.toCharArray()) {
                if (c >= 32 && c <= 126) {
                    printableCount++;
                }
            }
            
            double printableRatio = (double) printableCount / text.length();
            
            if (printableRatio > 0.8) {
                logger.info("📄 可能是文本数据 (可打印字符比例: {:.1f}%)", printableRatio * 100);
                logger.info("📄 文本内容: [{}]", text.length() > 200 ? text.substring(0, 200) + "..." : text);
            }
            
        } catch (Exception e) {
            logger.debug("📄 不是有效的 UTF-8 文本");
        }
    }
    
    /**
     * 检查是否可能是 PCM 数据
     */
    private static boolean isPossiblePcmData(byte[] data) {
        if (data.length < 100) {
            return false;
        }
        
        // 简单的启发式检查：PCM 数据通常有一定的变化但不会有太多极值
        int extremeCount = 0;
        for (int i = 0; i < Math.min(100, data.length); i++) {
            int value = data[i] & 0xFF;
            if (value == 0 || value == 255) {
                extremeCount++;
            }
        }
        
        return extremeCount < 20; // 如果极值太多，可能不是音频数据
    }
    
    /**
     * 分析数据分布
     */
    private static void analyzeDataDistribution(byte[] data) {
        int[] histogram = new int[256];
        for (byte b : data) {
            histogram[b & 0xFF]++;
        }
        
        // 找到最常见的字节值
        int maxCount = 0;
        int maxByte = 0;
        for (int i = 0; i < 256; i++) {
            if (histogram[i] > maxCount) {
                maxCount = i;
                maxByte = i;
            }
        }
        
        logger.info("📊 最常见字节值: 0x{} (出现 {} 次, {:.1f}%)", 
            String.format("%02x", maxByte), 
            maxCount, 
            (double) maxCount / data.length * 100);
    }
    
    /**
     * 字节数组转整数 (小端序)
     */
    private static int bytesToInt(byte[] data, int offset) {
        return (data[offset] & 0xFF) |
               ((data[offset + 1] & 0xFF) << 8) |
               ((data[offset + 2] & 0xFF) << 16) |
               ((data[offset + 3] & 0xFF) << 24);
    }
    
    /**
     * 字节数组转短整数 (小端序)
     */
    private static int bytesToShort(byte[] data, int offset) {
        return (data[offset] & 0xFF) |
               ((data[offset + 1] & 0xFF) << 8);
    }
}
