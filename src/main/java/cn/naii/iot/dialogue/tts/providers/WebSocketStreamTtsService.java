package cn.naii.iot.dialogue.tts.providers;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于 test_tts_client.py 和 tts_stream_server.py 的 WebSocket 流式 TTS 服务实现
 * 
 * 协议流程：
 * 1. 建立 WebSocket 连接到 /ws/tts_stream 端点
 * 2. 发送配置信息 JSON: {"prompt_text": "...", "prompt_wav_base64": "..."}
 * 3. 发送文本消息进行 TTS 转换
 * 4. 接收二进制音频数据流（PCM 格式，22050Hz）
 * 5. 接收控制信号：__STREAM_COMPLETE__, __ERROR__:message, __END_STREAM__, __CONNECTION_CLOSING__
 * 6. 发送结束信号：__END_STREAM__
 * 7. 接收关闭确认：__CONNECTION_CLOSING__
 * 8. 关闭连接
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class WebSocketStreamTtsService implements TtsService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketStreamTtsService.class);
    
    // 控制信号常量（匹配 Python 版本）
    private static final String SIGNAL_STREAM_COMPLETE = "__STREAM_COMPLETE__";
    private static final String SIGNAL_CONNECTION_CLOSING = "__CONNECTION_CLOSING__";
    private static final String SIGNAL_END_STREAM = "__END_STREAM__";
    private static final String SIGNAL_ERROR_PREFIX = "__ERROR__:";
    
    // 配置参数
    private static final long TTS_TIMEOUT_MS = 30000; // 30秒超时
    private static final String WS_ENDPOINT = "/ws/tts_stream"; // WebSocket 端点路径
    
    private final String apiUrl;
    private final String apiKey;
    private final String voiceName;
    private final String outputPath;
    private final OkHttpClient httpClient;
    
    // 提示配置
    private String promptText;
    private String promptWavBase64;
    
    /**
     * TTS 配置类（匹配 Python 版本的 JSON 格式）
     */
    public static class TtsConfig {
        @JsonProperty("prompt_text")
        public String promptText;
        
        @JsonProperty("prompt_wav_base64")
        public String promptWavBase64;
        
        public TtsConfig() {}
        
        public TtsConfig(String promptText, String promptWavBase64) {
            this.promptText = promptText;
            this.promptWavBase64 = promptWavBase64;
        }
    }
    
    public WebSocketStreamTtsService(SysConfig config, String voiceName, String outputPath) {
        this.apiUrl = config.getApiUrl();
        this.apiKey = config.getApiKey();
        this.voiceName = voiceName;
        this.outputPath = outputPath;
        
        // 创建 HTTP 客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
        
        logger.info("🚀 WebSocket 流式 TTS 服务初始化完成 - URL: {}, 语音: {}", apiUrl, voiceName);
    }
    
    /**
     * 设置提示文本和音频文件
     * 
     * @param promptText 提示文本
     * @param promptWavPath 提示音频文件路径（可为 null）
     */
    public void setPrompt(String promptText, String promptWavPath) throws Exception {
        this.promptText = promptText;
        
        if (promptWavPath != null && !promptWavPath.isEmpty()) {
            try {
                Path audioPath = Paths.get(promptWavPath);
                if (Files.exists(audioPath)) {
                    byte[] audioBytes = Files.readAllBytes(audioPath);
                    this.promptWavBase64 = Base64.getEncoder().encodeToString(audioBytes);
                    logger.info("✓ 提示音频文件加载成功: {} ({} bytes)", promptWavPath, audioBytes.length);
                } else {
                    logger.warn("提示音频文件不存在: {}", promptWavPath);
                    this.promptWavBase64 = "";
                }
            } catch (Exception e) {
                logger.error("加载提示音频文件失败: {}", promptWavPath, e);
                this.promptWavBase64 = "";
            }
        } else {
            this.promptWavBase64 = "";
        }
    }

    
    @Override
    public String textToSpeech(String text) throws Exception {
        if (text == null || text.isEmpty()) {
            logger.warn("文本内容为空！");
            return null;
        }
        
        // 生成音频文件路径
        String audioFilePath = Paths.get(outputPath, getAudioFileName()).toString();
        
        // 使用流式方法生成音频文件
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);
        
        try {
            // 创建输出目录
            Files.createDirectories(Paths.get(outputPath));
            
            streamTextToSpeech(text, new StreamTtsCallback() {
                private final ByteArrayOutputStream audioBuffer = new ByteArrayOutputStream();
                
                @Override
                public void onStart() {
                    logger.debug("开始 TTS 处理");
                }
                
                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    // 累积音频数据
                    audioBuffer.write(audioChunk);
                }
                
                @Override
                public void onComplete() {
                    try {
                        // 保存完整音频文件
                        Files.write(Paths.get(audioFilePath), audioBuffer.toByteArray());
                        logger.info("✅ 音频文件保存成功: {}", audioFilePath);
                    } catch (Exception e) {
                        errorRef.set(e);
                    } finally {
                        completionLatch.countDown();
                    }
                }
                
                @Override
                public void onError(Throwable error) {
                    errorRef.set(new Exception("TTS 处理失败", error));
                    completionLatch.countDown();
                }
            });
            
            // 等待处理完成
            boolean completed = completionLatch.await(TTS_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!completed) {
                throw new Exception("TTS 处理超时");
            }
            
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }
            
            return audioFilePath;
            
        } catch (Exception e) {
            logger.error("TTS 处理失败", e);
            throw e;
        }
    }
    
    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        if (text == null || text.isEmpty()) {
            logger.warn("文本内容为空！");
            return;
        }
        
        logger.info("🎬 开始 WebSocket 流式 TTS - 文本长度: {}, 语音: {}", text.length(), voiceName);
        
        // 构建 WebSocket URL
        String wsUrl = buildWebSocketUrl();
        
        // 创建 WebSocket 请求
        Request request = new Request.Builder()
                .url(wsUrl)
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("User-Agent", "IoT-Manager-TTS-Client/1.0")
                .build();
        
        // 状态管理
        AtomicBoolean isConnected = new AtomicBoolean(false);
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(1);
        
        // 创建 WebSocket 连接
        WebSocket webSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                logger.info("🔗 WebSocket 连接已建立");
                isConnected.set(true);
                connectionLatch.countDown();
                
                try {
                    callback.onStart();
                    
                    // 发送配置信息
                    sendConfiguration(webSocket);
                    
                    // 发送文本消息
                    sendTextMessage(webSocket, text);
                    
                } catch (Exception e) {
                    logger.error("发送消息失败", e);
                    errorRef.set(e);
                    completionLatch.countDown();
                }
            }
            
            @Override
            public void onMessage(WebSocket webSocket, String text) {
                logger.info("📨 收到文本消息: {}", text);
                handleTextMessage(text, callback, webSocket, completionLatch, errorRef);
            }

            @Override
            public void onMessage(WebSocket webSocket, okio.ByteString bytes) {
                logger.info("📨 收到二进制消息: {} bytes", bytes.size());
                handleBinaryMessage(bytes.toByteArray(), callback, errorRef);
            }
            
            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                logger.info("🔌 WebSocket 连接正在关闭: {} - {}", code, reason);
            }
            
            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                logger.info("❌ WebSocket 连接已关闭: {} - {}", code, reason);
                if (!isCompleted.get()) {
                    completionLatch.countDown();
                }
            }
            
            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                logger.error("❌ WebSocket 连接失败", t);
                errorRef.set(new Exception("WebSocket 连接失败", t));
                connectionLatch.countDown();
                completionLatch.countDown();
            }
        });
        
        try {
            // 等待连接建立
            boolean connected = connectionLatch.await(10, TimeUnit.SECONDS);
            if (!connected || !isConnected.get()) {
                throw new Exception("WebSocket 连接超时");
            }
            
            // 等待处理完成
            boolean completed = completionLatch.await(TTS_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!completed) {
                throw new Exception("TTS 处理超时");
            }
            
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }
            
        } finally {
            webSocket.close(1000, "正常关闭");
        }
    }
    
    /**
     * 构建 WebSocket URL
     */
    private String buildWebSocketUrl() {
        String baseUrl = apiUrl;
        
        // 转换 HTTP URL 为 WebSocket URL
        if (baseUrl.startsWith("http://")) {
            baseUrl = baseUrl.replace("http://", "ws://");
        } else if (baseUrl.startsWith("https://")) {
            baseUrl = baseUrl.replace("https://", "wss://");
        } else if (!baseUrl.startsWith("ws://") && !baseUrl.startsWith("wss://")) {
            baseUrl = "ws://" + baseUrl;
        }
        
        // 添加端点路径
        if (!baseUrl.endsWith(WS_ENDPOINT)) {
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            baseUrl += WS_ENDPOINT.substring(1); // 去掉开头的 /
        }
        
        return baseUrl;
    }
    
    /**
     * 发送配置信息（匹配 Python 版本的协议）
     */
    private void sendConfiguration(WebSocket webSocket) throws Exception {
        TtsConfig config = new TtsConfig(promptText, promptWavBase64);
        String configJson = JsonUtil.toJson(config);

        logger.info("📤 发送配置信息: {}", configJson);
        webSocket.send(configJson);
        logger.info("📤 已发送配置信息 - 提示文本长度: {}, 音频长度: {}",
            promptText != null ? promptText.length() : 0,
            promptWavBase64 != null ? promptWavBase64.length() : 0);
    }
    
    /**
     * 发送文本消息
     */
    private void sendTextMessage(WebSocket webSocket, String text) {
        logger.info("📤 发送文本消息: {}", text);
        webSocket.send(text);
        String textPreview = text.length() > 50 ? text.substring(0, 50) + "..." : text;
        logger.info("📤 已发送文本消息: {}", textPreview);
    }
    
    /**
     * 处理文本消息（控制信号）
     */
    private void handleTextMessage(String message, StreamTtsCallback callback,
                                 WebSocket webSocket, CountDownLatch completionLatch,
                                 AtomicReference<Exception> errorRef) {
        try {
            logger.info("🔍 处理文本消息: [{}] (长度: {})", message, message.length());

            if (SIGNAL_STREAM_COMPLETE.equals(message)) {
                logger.info("✅ 音频流接收完成");

                // 发送结束信号
                logger.info("📤 发送结束信号: {}", SIGNAL_END_STREAM);
                webSocket.send(SIGNAL_END_STREAM);
                logger.info("📤 已发送结束信号");

                callback.onComplete();
                completionLatch.countDown();

            } else if (message.startsWith(SIGNAL_ERROR_PREFIX)) {
                String errorMsg = message.substring(SIGNAL_ERROR_PREFIX.length());
                logger.error("❌ 服务器错误详情: [{}]", errorMsg);
                logger.error("❌ 完整错误消息: [{}]", message);
                errorRef.set(new Exception("服务器错误: " + errorMsg));
                completionLatch.countDown();

            } else if (SIGNAL_CONNECTION_CLOSING.equals(message)) {
                logger.info("🔌 服务器确认连接关闭");

            } else {
                logger.warn("⚠️ 收到未知控制消息: [{}] (长度: {})", message, message.length());
                // 打印消息的十六进制表示以便调试
                StringBuilder hex = new StringBuilder();
                for (char c : message.toCharArray()) {
                    hex.append(String.format("%04x ", (int) c));
                }
                logger.warn("⚠️ 消息十六进制: {}", hex.toString());
            }

        } catch (Exception e) {
            logger.error("处理文本消息失败", e);
            errorRef.set(e);
            completionLatch.countDown();
        }
    }
    
    /**
     * 处理二进制消息（音频数据）
     */
    private void handleBinaryMessage(byte[] audioData, StreamTtsCallback callback,
                                   AtomicReference<Exception> errorRef) {
        try {
            logger.info("📦 处理二进制音频数据: {} bytes", audioData.length);

            // 打印前几个字节用于调试
            if (audioData.length > 0) {
                StringBuilder hex = new StringBuilder();
                int printBytes = Math.min(16, audioData.length);
                for (int i = 0; i < printBytes; i++) {
                    hex.append(String.format("%02x ", audioData[i] & 0xFF));
                }
                logger.debug("📦 音频数据前 {} 字节: {}", printBytes, hex.toString());
            }

            callback.onAudioChunk(audioData);
            logger.debug("📦 音频块处理完成: {} bytes", audioData.length);

        } catch (Exception e) {
            logger.error("处理音频数据失败", e);
            errorRef.set(e);
        }
    }

    @Override
    public String getProviderName() {
        return "";
    }

    /**
     * 生成音频文件名
     */
    public String getAudioFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("tts_%s_%s.wav", voiceName.replaceAll("[^a-zA-Z0-9]", "_"), timestamp);
    }
}
