package cn.naii.iot.dialogue.tts.providers;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import io.github.whitemagic2014.tts.BuiltInVoice;
import io.github.whitemagic2014.tts.StreamTTSConfig;
import io.github.whitemagic2014.tts.TTS;
import io.github.whitemagic2014.tts.TTSVoice;
import io.github.whitemagic2014.tts.bean.Voice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Paths;

public class NaiiTtsService implements TtsService {
    private static final Logger logger = LoggerFactory.getLogger(NaiiTtsService.class);

    private static final String PROVIDER_NAME = "naiitts";

    // 音频名称
    private final String voiceName;

    // 音频输出路径
    private final String outputPath;

    public NaiiTtsService(String voiceName, String outputPath) {
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return TtsService.super.audioFormat();
    }

    @Override
    public boolean isSupportStreamTts() {
        // NaiiTTS库暂不支持流式,保持非流式实现
        return false;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        String wavFilePath = null;
        try {
            // 获取中文语音
            Voice voiceObj = getVoiceObject();

            // 生成唯一的文件名（不带扩展名）
            String baseFileName = getAudioFileName().split("\\.")[0];

            TTS tts = new TTS(voiceObj, text)
                    .streamConfig(new StreamTTSConfig(getBuiltInVoiceByVoiceName()))
                    .storage(outputPath)
                    .fileName(baseFileName);

            // 注意：NaiiTTS库的trans()方法返回的是文件名（不含路径）
            String returnedFileName = tts.transStream();

            // 构建完整的文件路径
            wavFilePath = buildFullFilePath(returnedFileName);

            // 等待文件完全写入并验证
            waitForFileAndValidate(wavFilePath, "WAV");
//            wavFilePath = AudioUtils.resampleWavTo16kHz(wavFilePath);
            logger.debug("NaiiTTS完成:16000 Hz: {}", wavFilePath);
            // 返回文件路径
            return wavFilePath;

        } catch (InterruptedException e) {
            handleInterruptedException(wavFilePath, e);
            return null;
        } finally {

        }
    }
    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        // NaiiTTS库暂不支持流式,抛出异常
        throw new UnsupportedOperationException("NaiiTTS暂不支持流式TTS,请使用非流式方法或切换到其他TTS提供商");
    }

    /**
     * 获取Voice对象
     */
    private Voice getVoiceObject() {
        return TTSVoice.provides().stream()
                .filter(v -> "zh-CN-XiaoyiNeural".equals(v.getShortName()))
                .toList().getFirst();
    }

    /**
     * 构建完整的文件路径
     * @param returnedFileName NaiiTTS库返回的文件名
     * @return 完整的文件路径
     */
    private String buildFullFilePath(String returnedFileName) {
        // NaiiTTS库返回的文件名可能包含路径，需要处理
        if (returnedFileName.contains("/") || returnedFileName.contains("\\")) {
            // 如果返回的是完整路径，直接使用
            return returnedFileName;
        } else {
            // 如果只是文件名，拼接路径
            return outputPath + returnedFileName;
        }
    }

    /**
     * 等待文件完全写入并验证
     * @param filePath 文件路径
     * @param fileType 文件类型描述（用于日志和异常消息）
     * @throws Exception 如果文件不存在或为空
     * @throws InterruptedException 如果等待被中断
     */
    private void waitForFileAndValidate(String filePath, String fileType) throws Exception, InterruptedException {
        // 等待文件完全写入（NaiiTTS库可能是异步写入）
        int maxRetries = 10;
        int retryCount = 0;
        long fileSize = 0;

        while (retryCount < maxRetries) {
            if (Files.exists(Paths.get(filePath))) {
                long currentSize = Files.size(Paths.get(filePath));

                // 如果文件大小稳定（连续两次检查大小相同），认为写入完成
                if (currentSize > 0 && currentSize == fileSize) {
                    logger.debug("NaiiTTS文件写入完成: {}, 大小: {} bytes", filePath, currentSize);
                    break;
                }

                fileSize = currentSize;
            }

            // 等待100ms后重试
            Thread.sleep(100);
            retryCount++;
        }

        // 验证文件是否存在且有内容
        if (!Files.exists(Paths.get(filePath))) {
            throw new Exception("NaiiTTS生成的" + fileType + "文件不存在: " + filePath);
        }

        long finalSize = Files.size(Paths.get(filePath));
        if (finalSize == 0) {
            throw new Exception("NaiiTTS生成的" + fileType + "文件为空: " + filePath);
        }

        logger.debug("NaiiTTS生成{}文件: {}, 大小: {} bytes", fileType, filePath, finalSize);
    }

    /**
     * 处理中断异常并清理临时文件
     * @param filePath 需要删除的文件路径
     * @param e 中断异常
     * @throws Exception 包装后的异常
     */
    private void handleInterruptedException(String filePath, InterruptedException e) throws Exception {
        Thread.currentThread().interrupt();
        // 确保删除临时文件
        if (filePath != null) {
            try {
                Files.deleteIfExists(Paths.get(filePath));
                logger.debug("NaiiTTS删除临时文件: {}", filePath);
            } catch (Exception ex) {
                logger.warn("删除临时文件失败: {}", filePath, ex);
            }
        }
        throw new Exception("NaiiTTS处理被中断", e);
    }

    /**
     * 根据voiceName返回对应的BuiltInVoice枚举
     * @return BuiltInVoice枚举值
     */
    private BuiltInVoice getBuiltInVoiceByVoiceName() {
        switch (voiceName.toLowerCase()) {
            case "pusa":
                return BuiltInVoice.PUSA;
            case "laodaoshi":
                return BuiltInVoice.LAODAOSHI;
            case "guanyu":
                return BuiltInVoice.GUANYU;
            default:
                // 默认返回PUSA
                return BuiltInVoice.GUANYU;
        }
    }

}