package cn.naii.iot.dialogue.tts;

import java.util.UUID;

/**
 * TTS服务接口
 */
public interface TtsService {

  /**
   * 获取服务提供商名称
   */
  String getProviderName();

  /**
   * 音频格式
   */
  default String audioFormat() {
    return "wav";
  }

  /**
   * 生成文件名称
   *
   * @return 文件名称
   */
  default String getAudioFileName() {
    return UUID.randomUUID().toString().replace("-", "") + "." + audioFormat();
  }

  /**
   * 是否支持流式TTS
   *
   * @return true表示支持流式,false表示仅支持非流式
   */
  default boolean isSupportStreamTts() {
    return false;
  }

  /**
   * 将文本转换为语音（非流式）
   *
   * @param text 要转换为语音的文本
   * @return 生成的音频文件路径
   */
  String textToSpeech(String text) throws Exception;

  /**
   * 流式将文本转换为语音
   *
   * @param text     要转换为语音的文本
   * @param callback 流式回调接口,接收PCM格式的音频数据块
   * @throws Exception 转换过程中可能发生的异常
   */
  default void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
    throw new UnsupportedOperationException("该TTS提供商不支持流式TTS");
  }

}
