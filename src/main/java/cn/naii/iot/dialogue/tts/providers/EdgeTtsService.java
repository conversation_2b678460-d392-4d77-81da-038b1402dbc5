package cn.naii.iot.dialogue.tts.providers;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.utils.AudioUtils;
import io.github.whitemagic2014.tts.TTS;
import io.github.whitemagic2014.tts.TTSVoice;
import io.github.whitemagic2014.tts.bean.Voice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
public class EdgeTtsService implements TtsService {
    private static final Logger logger = LoggerFactory.getLogger(EdgeTtsService.class);

    private static final String PROVIDER_NAME = "edge";

    // 音频名称
    private final String voiceName;

    // 音频输出路径
    private final String outputPath;

    public EdgeTtsService(String voiceName, String outputPath) {
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return TtsService.super.audioFormat();
    }

    @Override
    public boolean isSupportStreamTts() {
        // EdgeTTS库暂不支持流式,保持非流式实现
        return false;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        String mp3FilePath = null;
        try {
            // 获取中文语音
            Voice voiceObj = getVoiceObject();

            // 生成唯一的文件名（不带扩展名）
            String baseFileName = getAudioFileName().split("\\.")[0];

            TTS ttsEngine = new TTS(voiceObj, text);
            // 执行TTS转换获取音频文件
            // 注意：EdgeTTS库的trans()方法返回的是文件名（不含路径）
            String returnedFileName = ttsEngine.findHeadHook()
                    .storage(outputPath)
                    .fileName(baseFileName)
                    .isRateLimited(true)
                    // 改为true，允许覆盖，避免文件名冲突
                    .overwrite(true)
                    .formatMp3()
                    .trans();

            // 构建完整的MP3文件路径
            mp3FilePath = buildFullFilePath(returnedFileName);

            // 等待文件完全写入并验证
            waitForFileAndValidate(mp3FilePath, "MP3");

            // 1. 将MP3转换为PCM (已经设置为16kHz采样率和单声道)
            byte[] pcmData = AudioUtils.mp3ToPcm(mp3FilePath);

            // 2. 将PCM转换回WAV (使用AudioUtils中的设置：16kHz, 单声道, 160kbps)
            String resampledFileName = AudioUtils.saveAsWav(pcmData);

            logger.debug("EdgeTTS转换完成: MP3 -> WAV: {}", resampledFileName);

            // 3. 返回重采样后的文件路径
            return AudioUtils.AUDIO_PATH + resampledFileName;

        } catch (InterruptedException e) {
            handleInterruptedException(mp3FilePath, e);
            // 永远不会执行到这里
            return null;
        }
    }
    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        // EdgeTTS库暂不支持流式,抛出异常
        throw new UnsupportedOperationException("EdgeTTS暂不支持流式TTS,请使用非流式方法或切换到其他TTS提供商");
    }

    /**
     * 获取Voice对象
     */
    private Voice getVoiceObject() {
        var matchedVoices = TTSVoice.provides().stream()
                .filter(v -> v.getShortName().equals(voiceName))
                .toList();

        if (matchedVoices.isEmpty()) {
            // 获取所有可用的语音名称用于日志
            var availableVoices = TTSVoice.provides().stream()
                    .map(Voice::getShortName)
                    .toList();

            logger.error("❌ EdgeTTS未找到匹配的语音: \"{}\", 可用语音列表: {}",
                voiceName, availableVoices);

            throw new IllegalArgumentException(
                String.format("EdgeTTS未找到匹配的语音: \"%s\", 可用语音: %s",
                    voiceName, availableVoices));
        }

        return matchedVoices.getFirst();
    }

    /**
     * 构建完整的文件路径
     * @param returnedFileName EdgeTTS库返回的文件名
     * @return 完整的文件路径
     */
    private String buildFullFilePath(String returnedFileName) {
        // EdgeTTS库返回的文件名可能包含路径，需要处理
        if (returnedFileName.contains("/") || returnedFileName.contains("\\")) {
            // 如果返回的是完整路径，直接使用
            return returnedFileName;
        } else {
            // 如果只是文件名，拼接路径
            return outputPath + returnedFileName;
        }
    }

    /**
     * 等待文件完全写入并验证
     * @param filePath 文件路径
     * @param fileType 文件类型描述（用于日志和异常消息）
     * @throws Exception 如果文件不存在或为空
     * @throws InterruptedException 如果等待被中断
     */
    private void waitForFileAndValidate(String filePath, String fileType) throws Exception, InterruptedException {
        // 等待文件完全写入（EdgeTTS库可能是异步写入）
        int maxRetries = 10;
        int retryCount = 0;
        long fileSize = 0;

        while (retryCount < maxRetries) {
            if (Files.exists(Paths.get(filePath))) {
                long currentSize = Files.size(Paths.get(filePath));

                // 如果文件大小稳定（连续两次检查大小相同），认为写入完成
                if (currentSize > 0 && currentSize == fileSize) {
                    logger.debug("EdgeTTS文件写入完成: {}, 大小: {} bytes", filePath, currentSize);
                    break;
                }

                fileSize = currentSize;
            }

            // 等待100ms后重试
            Thread.sleep(100);
            retryCount++;
        }

        // 验证文件是否存在且有内容
        if (!Files.exists(Paths.get(filePath))) {
            throw new Exception("EdgeTTS生成的" + fileType + "文件不存在: " + filePath);
        }

        long finalSize = Files.size(Paths.get(filePath));
        if (finalSize == 0) {
            throw new Exception("EdgeTTS生成的" + fileType + "文件为空: " + filePath);
        }

        logger.debug("EdgeTTS生成{}文件: {}, 大小: {} bytes", fileType, filePath, finalSize);
    }

    /**
     * 处理中断异常并清理临时文件
     * @param filePath 需要删除的文件路径
     * @param e 中断异常
     * @throws Exception 包装后的异常
     */
    private void handleInterruptedException(String filePath, InterruptedException e) throws Exception {
        Thread.currentThread().interrupt();
        // 确保删除临时文件
        if (filePath != null) {
            try {
                Files.deleteIfExists(Paths.get(filePath));
                logger.debug("EdgeTTS删除临时文件: {}", filePath);
            } catch (Exception ex) {
                logger.warn("删除临时文件失败: {}", filePath, ex);
            }
        }
        throw new Exception("EdgeTTS处理被中断", e);
    }

}