package cn.naii.iot.dialogue.tts.providers;

import io.github.whitemagic2014.tts.TTS;
import io.github.whitemagic2014.tts.TTSVoice;
import io.github.whitemagic2014.tts.bean.Voice;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.utils.AudioUtils;

public class EdgeTtsService implements TtsService {
    private static final Logger logger = LoggerFactory.getLogger(EdgeTtsService.class);

    private static final String PROVIDER_NAME = "edge";

    // 音频名称
    private String voiceName;

    // 音频输出路径
    private String outputPath;

    public EdgeTtsService(String voiceName, String outputPath) {
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "mp3";
    }

    @Override
    public boolean isSupportStreamTts() {
        // EdgeTTS库暂不支持流式,保持非流式实现
        return false;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        String mp3FilePath = null;
        try {
            // 获取中文语音
            Voice voiceObj = TTSVoice.provides().stream()
                    .filter(v -> v.getShortName().equals(voiceName))
                    .collect(Collectors.toList()).get(0);

            // 生成唯一的文件名（不带扩展名）
            String baseFileName = getAudioFileName().split("\\.")[0];

            TTS ttsEngine = new TTS(voiceObj, text);
            // 执行TTS转换获取音频文件
            // 注意：EdgeTTS库的trans()方法返回的是文件名（不含路径）
            String returnedFileName = ttsEngine.findHeadHook()
                    .storage(outputPath)
                    .fileName(baseFileName)
                    .isRateLimited(true)
                    .overwrite(true)  // 改为true，允许覆盖，避免文件名冲突
                    .formatMp3()
                    .trans();

            // 构建完整的MP3文件路径
            // EdgeTTS库返回的文件名可能包含路径，需要处理
            if (returnedFileName.contains("/") || returnedFileName.contains("\\")) {
                // 如果返回的是完整路径，直接使用
                mp3FilePath = returnedFileName;
            } else {
                // 如果只是文件名，拼接路径
                mp3FilePath = outputPath + returnedFileName;
            }

            // 🆕 等待文件完全写入（EdgeTTS库可能是异步写入）
            int maxRetries = 10;
            int retryCount = 0;
            long fileSize = 0;

            while (retryCount < maxRetries) {
                if (Files.exists(Paths.get(mp3FilePath))) {
                    long currentSize = Files.size(Paths.get(mp3FilePath));

                    // 如果文件大小稳定（连续两次检查大小相同），认为写入完成
                    if (currentSize > 0 && currentSize == fileSize) {
                        logger.debug("EdgeTTS文件写入完成: {}, 大小: {} bytes", mp3FilePath, currentSize);
                        break;
                    }

                    fileSize = currentSize;
                }

                // 等待100ms后重试
                Thread.sleep(100);
                retryCount++;
            }

            // 验证MP3文件是否存在且有内容
            if (!Files.exists(Paths.get(mp3FilePath))) {
                throw new Exception("EdgeTTS生成的MP3文件不存在: " + mp3FilePath);
            }

            long finalSize = Files.size(Paths.get(mp3FilePath));
            if (finalSize == 0) {
                throw new Exception("EdgeTTS生成的MP3文件为空: " + mp3FilePath);
            }

            logger.debug("EdgeTTS生成MP3文件: {}, 大小: {} bytes", mp3FilePath, finalSize);

            // 1. 将MP3转换为PCM (已经设置为16kHz采样率和单声道)
            byte[] pcmData = AudioUtils.mp3ToPcm(mp3FilePath);

            // 2. 将PCM转换回WAV (使用AudioUtils中的设置：16kHz, 单声道, 160kbps)
            String resampledFileName = AudioUtils.saveAsWav(pcmData);

            logger.debug("EdgeTTS转换完成: MP3 -> WAV: {}", resampledFileName);

            // 3. 返回重采样后的文件路径
            return AudioUtils.AUDIO_PATH + resampledFileName;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new Exception("EdgeTTS处理被中断", e);
        } finally {
            // 4. 确保删除临时MP3文件（在finally块中确保一定会执行）
            if (mp3FilePath != null) {
                try {
                    Files.deleteIfExists(Paths.get(mp3FilePath));
                    logger.debug("EdgeTTS删除临时MP3文件: {}", mp3FilePath);
                } catch (Exception e) {
                    logger.warn("删除临时MP3文件失败: {}", mp3FilePath, e);
                }
            }
        }
    }

    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        // EdgeTTS库暂不支持流式,抛出异常
        throw new UnsupportedOperationException("EdgeTTS暂不支持流式TTS,请使用非流式方法或切换到其他TTS提供商");
    }

}