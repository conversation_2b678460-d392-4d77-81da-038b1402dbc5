package cn.naii.iot.dialogue.tts;

/**
 * 流式TTS音频数据回调接口
 * 用于接收TTS服务实时生成的音频数据块
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@FunctionalInterface
public interface StreamTtsCallback {
    
    /**
     * 接收音频数据块
     * 
     * @param audioChunk PCM音频数据块 (16kHz, 单声道, 16位)
     * @throws Exception 处理异常
     */
    void onAudioChunk(byte[] audioChunk) throws Exception;
    
    /**
     * 流式传输开始回调 (可选)
     * 默认实现为空
     */
    default void onStart() {
        // 默认不做任何操作
    }
    
    /**
     * 流式传输完成回调 (可选)
     * 默认实现为空
     */
    default void onComplete() {
        // 默认不做任何操作
    }
    
    /**
     * 流式传输错误回调 (可选)
     * 
     * @param error 错误信息
     */
    default void onError(Throwable error) {
        // 默认不做任何操作
    }
}

