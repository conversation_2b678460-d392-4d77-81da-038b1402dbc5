package cn.naii.iot.dialogue.tts;

import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.utils.OpusProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 流式TTS音频处理器
 * 负责将TTS生成的PCM音频流转换为Opus帧并发送
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Component
public class StreamTtsProcessor {
    private static final Logger logger = LoggerFactory.getLogger(StreamTtsProcessor.class);
    
    private final OpusProcessor opusProcessor;
    
    // 流式会话状态管理
    private final ConcurrentHashMap<String, StreamState> streamStates = new ConcurrentHashMap<>();
    
    public StreamTtsProcessor(OpusProcessor opusProcessor) {
        this.opusProcessor = opusProcessor;
    }
    
    /**
     * 流式会话状态
     */
    public static class StreamState {
        private final String sessionId;
        private final AtomicBoolean isActive = new AtomicBoolean(true);
        private final AtomicLong totalBytesProcessed = new AtomicLong(0);
        private final AtomicLong totalFramesSent = new AtomicLong(0);
        private final long startTime = System.currentTimeMillis();
        
        public StreamState(String sessionId) {
            this.sessionId = sessionId;
        }
        
        public boolean isActive() {
            return isActive.get();
        }
        
        public void markInactive() {
            isActive.set(false);
        }
        
        public void addBytes(long bytes) {
            totalBytesProcessed.addAndGet(bytes);
        }
        
        public void addFrames(long frames) {
            totalFramesSent.addAndGet(frames);
        }
        
        public long getTotalBytesProcessed() {
            return totalBytesProcessed.get();
        }
        
        public long getTotalFramesSent() {
            return totalFramesSent.get();
        }
        
        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }
    }
    
    /**
     * 初始化流式会话
     * 
     * @param sessionId 会话ID
     * @return 流式状态对象
     */
    public StreamState initStream(String sessionId) {
        StreamState state = new StreamState(sessionId);
        streamStates.put(sessionId, state);
        logger.debug("🎬 初始化流式TTS会话 - SessionId: {}", sessionId);
        return state;
    }
    
    /**
     * 处理PCM音频块并转换为Opus帧
     * 
     * @param sessionId 会话ID
     * @param pcmChunk PCM音频数据块
     * @param frameConsumer Opus帧消费者
     * @return 生成的Opus帧数量
     */
    public int processPcmChunk(String sessionId, byte[] pcmChunk, FrameConsumer frameConsumer) {
        StreamState state = streamStates.get(sessionId);
        if (state == null || !state.isActive()) {
            logger.warn("⚠️ 流式会话不存在或已关闭 - SessionId: {}", sessionId);
            return 0;
        }
        
        try {
            // 使用OpusProcessor的流式模式转换PCM为Opus
            List<byte[]> opusFrames = opusProcessor.pcmToOpus(sessionId, pcmChunk, true);
            
            // 发送每一帧
            int sentCount = 0;
            for (byte[] frame : opusFrames) {
                try {
                    frameConsumer.accept(frame);
                    sentCount++;
                } catch (Exception e) {
                    logger.error("❌ Opus帧发送失败 - SessionId: {}", sessionId, e);
                    throw e;
                }
            }
            
            // 更新统计
            state.addBytes(pcmChunk.length);
            state.addFrames(sentCount);
            
            if (sentCount > 0) {
                logger.trace("📤 发送Opus帧 - SessionId: {}, 帧数: {}, PCM大小: {} bytes", 
                    sessionId, sentCount, pcmChunk.length);
            }
            
            return sentCount;
            
        } catch (Exception e) {
            logger.error("❌ PCM转Opus失败 - SessionId: {}", sessionId, e);
            return 0;
        }
    }
    
    /**
     * 刷新残留数据并完成流式传输
     * 
     * @param sessionId 会话ID
     * @param frameConsumer Opus帧消费者
     * @return 刷新的Opus帧数量
     */
    public int flushStream(String sessionId, FrameConsumer frameConsumer) {
        StreamState state = streamStates.get(sessionId);
        if (state == null) {
            logger.warn("⚠️ 流式会话不存在 - SessionId: {}", sessionId);
            return 0;
        }
        
        try {
            // 刷新OpusProcessor中的残留数据
            List<byte[]> remainingFrames = opusProcessor.flushLeftover(sessionId);
            
            int sentCount = 0;
            for (byte[] frame : remainingFrames) {
                try {
                    frameConsumer.accept(frame);
                    sentCount++;
                } catch (Exception e) {
                    logger.error("❌ 残留帧发送失败 - SessionId: {}", sessionId, e);
                }
            }
            
            if (sentCount > 0) {
                state.addFrames(sentCount);
                logger.debug("🔄 刷新残留帧 - SessionId: {}, 帧数: {}", sessionId, sentCount);
            }
            
            // 标记流为非活跃
            state.markInactive();
            
            // 记录统计信息
            logger.info("✅ 流式TTS完成 - SessionId: {}, 总字节: {}, 总帧数: {}, 耗时: {}ms",
                sessionId, 
                state.getTotalBytesProcessed(),
                state.getTotalFramesSent(),
                state.getDuration());
            
            return sentCount;
            
        } catch (Exception e) {
            logger.error("❌ 刷新流式数据失败 - SessionId: {}", sessionId, e);
            return 0;
        }
    }
    
    /**
     * 清理流式会话
     * 
     * @param sessionId 会话ID
     */
    public void cleanupStream(String sessionId) {
        StreamState state = streamStates.remove(sessionId);
        if (state != null) {
            state.markInactive();
            logger.debug("🗑️ 清理流式TTS会话 - SessionId: {}", sessionId);
        }
        
        // 清理OpusProcessor中的状态
        opusProcessor.removeLeftoverState(sessionId);
    }
    
    /**
     * 获取流式会话状态
     * 
     * @param sessionId 会话ID
     * @return 流式状态,如果不存在返回null
     */
    public StreamState getStreamState(String sessionId) {
        return streamStates.get(sessionId);
    }
    
    /**
     * Opus帧消费者接口
     */
    @FunctionalInterface
    public interface FrameConsumer {
        void accept(byte[] opusFrame) throws Exception;
    }
}

