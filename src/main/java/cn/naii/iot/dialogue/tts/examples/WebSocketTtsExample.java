package cn.naii.iot.dialogue.tts.examples;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.dialogue.tts.factory.TtsServiceFactory;
import cn.naii.iot.dialogue.tts.providers.WebSocketStreamTtsService;
import cn.naii.iot.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket 流式 TTS 服务使用示例
 * 
 * 演示如何在实际应用中使用 WebSocketStreamTtsService
 * 包括配置、流式处理、错误处理等最佳实践
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Component
public class WebSocketTtsExample {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketTtsExample.class);
    
    @Autowired
    private TtsServiceFactory ttsServiceFactory;
    
    /**
     * 示例1：基本的流式 TTS 使用
     */
    public void basicStreamingExample() {
        logger.info("🎬 示例1：基本流式 TTS 使用");
        
        try {
            // 1. 创建配置
            SysConfig config = createWebSocketConfig();
            
            // 2. 获取服务实例
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");
            
            // 3. 设置提示音频（如果是 WebSocketStreamTtsService）
            if (ttsService instanceof WebSocketStreamTtsService) {
                WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
                streamService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);
            }
            
            // 4. 执行流式 TTS
            String text = "欢迎使用 WebSocket 流式 TTS 服务！";
            
            CountDownLatch latch = new CountDownLatch(1);
            AtomicInteger chunkCount = new AtomicInteger(0);
            
            ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
                @Override
                public void onStart() {
                    logger.info("✅ TTS 处理开始");
                }
                
                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    int count = chunkCount.incrementAndGet();
                    logger.info("📦 接收音频块 #{}: {} bytes", count, audioChunk.length);
                    
                    // 这里可以进行实时音频处理
                    // 例如：播放、转发、保存等
                }
                
                @Override
                public void onComplete() {
                    logger.info("🎉 TTS 处理完成，共接收 {} 个音频块", chunkCount.get());
                    latch.countDown();
                }
                
                @Override
                public void onError(Throwable error) {
                    logger.error("❌ TTS 处理失败", error);
                    latch.countDown();
                }
            });
            
            // 等待处理完成
            latch.await(30, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            logger.error("基本流式 TTS 示例失败", e);
        }
    }
    
    /**
     * 示例2：实时音频播放
     */
    public void realTimePlaybackExample() {
        logger.info("🎬 示例2：实时音频播放");
        
        try {
            SysConfig config = createWebSocketConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");
            
            if (ttsService instanceof WebSocketStreamTtsService) {
                WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
                streamService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);
            }
            
            String text = "这是一个实时音频播放的示例，音频数据会被实时处理和播放。";
            
            // 音频播放器（示例实现）
            AudioPlayer audioPlayer = new AudioPlayer();
            
            ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
                @Override
                public void onStart() {
                    logger.info("🎵 开始实时音频播放");
                    audioPlayer.start();
                }
                
                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    // 实时播放音频块
                    audioPlayer.playChunk(audioChunk);
                }
                
                @Override
                public void onComplete() {
                    logger.info("🎵 音频播放完成");
                    audioPlayer.stop();
                }
                
                @Override
                public void onError(Throwable error) {
                    logger.error("🎵 音频播放失败", error);
                    audioPlayer.stop();
                }
            });
            
        } catch (Exception e) {
            logger.error("实时音频播放示例失败", e);
        }
    }
    
    /**
     * 示例3：音频数据缓存和批处理
     */
    public void audioCachingExample() {
        logger.info("🎬 示例3：音频数据缓存和批处理");
        
        try {
            SysConfig config = createWebSocketConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");
            
            if (ttsService instanceof WebSocketStreamTtsService) {
                WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
                streamService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);
            }
            
            String text = "这个示例演示如何缓存音频数据并进行批处理。";
            
            // 音频缓存
            ByteArrayOutputStream audioCache = new ByteArrayOutputStream();
            final int BATCH_SIZE = 4096; // 4KB 批处理大小
            
            ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
                @Override
                public void onStart() {
                    logger.info("📦 开始音频数据缓存");
                }
                
                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    // 添加到缓存
                    audioCache.write(audioChunk);
                    
                    // 当缓存达到批处理大小时进行处理
                    if (audioCache.size() >= BATCH_SIZE) {
                        byte[] batchData = audioCache.toByteArray();
                        processBatchAudio(batchData);
                        audioCache.reset();
                    }
                }
                
                @Override
                public void onComplete() {
                    // 处理剩余的音频数据
                    if (audioCache.size() > 0) {
                        byte[] remainingData = audioCache.toByteArray();
                        processBatchAudio(remainingData);
                    }
                    logger.info("📦 音频数据缓存完成");
                }
                
                @Override
                public void onError(Throwable error) {
                    logger.error("📦 音频数据缓存失败", error);
                }
            });
            
        } catch (Exception e) {
            logger.error("音频缓存示例失败", e);
        }
    }
    
    /**
     * 示例4：错误处理和重试机制
     */
    public void errorHandlingExample() {
        logger.info("🎬 示例4：错误处理和重试机制");
        
        try {
            SysConfig config = createWebSocketConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");
            
            if (ttsService instanceof WebSocketStreamTtsService) {
                WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
                streamService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);
            }
            
            String text = "这个示例演示错误处理和重试机制。";
            
            // 重试配置
            final int MAX_RETRIES = 3;
            final long RETRY_DELAY_MS = 1000;
            
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
                logger.info("🔄 尝试第 {}/{} 次", attempt, MAX_RETRIES);
                
                try {
                    CountDownLatch latch = new CountDownLatch(1);
                    AtomicInteger errorCount = new AtomicInteger(0);
                    
                    ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
                        @Override
                        public void onStart() {
                            logger.info("✅ TTS 处理开始（尝试 {}）", attempt);
                        }
                        
                        @Override
                        public void onAudioChunk(byte[] audioChunk) throws Exception {
                            // 处理音频数据
                            logger.debug("📦 接收音频块: {} bytes", audioChunk.length);
                        }
                        
                        @Override
                        public void onComplete() {
                            logger.info("🎉 TTS 处理成功（尝试 {}）", attempt);
                            latch.countDown();
                        }
                        
                        @Override
                        public void onError(Throwable error) {
                            errorCount.incrementAndGet();
                            logger.warn("⚠️ TTS 处理失败（尝试 {}）: {}", attempt, error.getMessage());
                            latch.countDown();
                        }
                    });
                    
                    // 等待处理完成
                    boolean completed = latch.await(30, TimeUnit.SECONDS);
                    
                    if (completed && errorCount.get() == 0) {
                        logger.info("✅ TTS 处理成功");
                        return; // 成功，退出重试循环
                    }
                    
                } catch (Exception e) {
                    logger.warn("⚠️ 尝试 {} 失败: {}", attempt, e.getMessage());
                }
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < MAX_RETRIES) {
                    logger.info("⏳ 等待 {} ms 后重试...", RETRY_DELAY_MS);
                    Thread.sleep(RETRY_DELAY_MS);
                }
            }
            
            logger.error("❌ 所有重试尝试都失败了");
            
        } catch (Exception e) {
            logger.error("错误处理示例失败", e);
        }
    }
    
    /**
     * 示例5：批量文本处理
     */
    public void batchTextProcessingExample() {
        logger.info("🎬 示例5：批量文本处理");
        
        try {
            SysConfig config = createWebSocketConfig();
            TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");
            
            if (ttsService instanceof WebSocketStreamTtsService) {
                WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
                streamService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);
            }
            
            // 批量文本
            String[] texts = {
                "第一段文本：欢迎使用 WebSocket TTS 服务。",
                "第二段文本：这是一个批量处理的示例。",
                "第三段文本：每段文本都会独立处理。"
            };
            
            for (int i = 0; i < texts.length; i++) {
                final int index = i + 1;
                final String text = texts[i];
                
                logger.info("📝 处理第 {}/{} 段文本", index, texts.length);
                
                CountDownLatch latch = new CountDownLatch(1);
                
                ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
                    @Override
                    public void onStart() {
                        logger.info("🎬 开始处理第 {} 段", index);
                    }
                    
                    @Override
                    public void onAudioChunk(byte[] audioChunk) throws Exception {
                        // 处理音频数据
                        logger.debug("📦 第 {} 段音频块: {} bytes", index, audioChunk.length);
                    }
                    
                    @Override
                    public void onComplete() {
                        logger.info("✅ 第 {} 段处理完成", index);
                        latch.countDown();
                    }
                    
                    @Override
                    public void onError(Throwable error) {
                        logger.error("❌ 第 {} 段处理失败", index, error);
                        latch.countDown();
                    }
                });
                
                // 等待当前文本处理完成
                latch.await(30, TimeUnit.SECONDS);
                
                // 避免服务器过载
                if (i < texts.length - 1) {
                    Thread.sleep(500);
                }
            }
            
            logger.info("🎉 批量文本处理完成");
            
        } catch (Exception e) {
            logger.error("批量文本处理示例失败", e);
        }
    }
    
    /**
     * 创建 WebSocket TTS 配置
     */
    private SysConfig createWebSocketConfig() {
        SysConfig config = new SysConfig();
        config.setProvider("websocket_stream");
        config.setApiUrl("ws://localhost:60009"); // Python 服务器默认端口
        config.setApiKey("demo-api-key");
        return config;
    }
    
    /**
     * 处理批量音频数据
     */
    private void processBatchAudio(byte[] audioData) {
        logger.info("🔄 处理批量音频数据: {} bytes", audioData.length);
        
        // 这里可以添加具体的音频处理逻辑
        // 例如：格式转换、压缩、发送到客户端等
    }
    
    /**
     * 简单的音频播放器示例
     */
    private static class AudioPlayer {
        private boolean isPlaying = false;
        
        public void start() {
            isPlaying = true;
            logger.info("🎵 音频播放器启动");
        }
        
        public void playChunk(byte[] audioChunk) {
            if (isPlaying) {
                // 这里应该实现实际的音频播放逻辑
                // 例如使用 Java Sound API 或其他音频库
                logger.debug("🎵 播放音频块: {} bytes", audioChunk.length);
            }
        }
        
        public void stop() {
            isPlaying = false;
            logger.info("🎵 音频播放器停止");
        }
    }
}
