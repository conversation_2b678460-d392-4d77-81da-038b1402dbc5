package cn.naii.iot.dialogue.tts.providers;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 自定义TTS服务 - 演示如何使用WebSocket发送文本流并接收音频流
 *
 * 本实现展示了：
 * 1. WebSocket连接建立和管理
 * 2. 文本流式发送
 * 3. 音频流式接收和处理
 * 4. 错误处理和连接管理
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class CustomizeTtsService implements TtsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomizeTtsService.class);
    private static final String PROVIDER_NAME = "customize";

    // WebSocket连接超时时间（毫秒）
    private static final long WS_CONNECT_TIMEOUT_MS = 10000;
    // TTS处理超时时间（毫秒）
    private static final long TTS_TIMEOUT_MS = 30000;

    // 配置参数
    private final String apiUrl;
    private final String apiKey;
    private final String voiceName;
    private final String outputPath;

    // HTTP客户端
    private final OkHttpClient httpClient;

    public CustomizeTtsService(SysConfig config, String voiceName, String outputPath) {
        this.apiUrl = config.getApiUrl(); // WebSocket服务地址
        this.apiKey = config.getApiKey(); // API密钥
        this.voiceName = voiceName;
        this.outputPath = outputPath;

        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(WS_CONNECT_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                .readTimeout(TTS_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                .writeTimeout(10000, TimeUnit.MILLISECONDS)
                .build();
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "wav";
    }

    @Override
    public boolean isSupportStreamTts() {
        // 支持流式TTS
        return true;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        if (text == null || text.isEmpty()) {
            logger.warn("文本内容为空！");
            return null;
        }

        // 生成音频文件路径
        String audioFilePath = Paths.get(outputPath, getAudioFileName()).toString();

        // 使用流式方法生成音频文件
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);

        try {
            streamTextToSpeech(text, new StreamTtsCallback() {
                private byte[] audioBuffer = new byte[0];

                @Override
                public void onAudioChunk(byte[] audioChunk) throws Exception {
                    // 累积音频数据
                    byte[] newBuffer = new byte[audioBuffer.length + audioChunk.length];
                    System.arraycopy(audioBuffer, 0, newBuffer, 0, audioBuffer.length);
                    System.arraycopy(audioChunk, 0, newBuffer, audioBuffer.length, audioChunk.length);
                    audioBuffer = newBuffer;
                }

                @Override
                public void onComplete() {
                    try {
                        // 保存完整音频文件
                        Files.write(Paths.get(audioFilePath), audioBuffer);
                        logger.info("✅ 自定义TTS音频文件保存成功: {}", audioFilePath);
                    } catch (Exception e) {
                        errorRef.set(e);
                    } finally {
                        completionLatch.countDown();
                    }
                }

                @Override
                public void onError(Throwable error) {
                    errorRef.set(new Exception("TTS处理失败", error));
                    completionLatch.countDown();
                }
            });

            // 等待处理完成
            boolean completed = completionLatch.await(TTS_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!completed) {
                throw new Exception("TTS处理超时");
            }

            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }

            return audioFilePath;

        } catch (Exception e) {
            logger.error("自定义TTS处理失败", e);
            throw e;
        }
    }

    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        if (text == null || text.isEmpty()) {
            logger.warn("文本内容为空！");
            return;
        }

        logger.info("🎬 开始自定义WebSocket流式TTS - 文本长度: {}, 语音: {}", text.length(), voiceName);

        // 构建WebSocket URL
        String wsUrl = buildWebSocketUrl();

        // 创建WebSocket请求
        Request request = new Request.Builder()
                .url(wsUrl)
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("User-Agent", "IoT-Manager-TTS-Client/1.0")
                .build();

        // 状态管理
        AtomicBoolean isConnected = new AtomicBoolean(false);
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(1);

        // 创建WebSocket连接
        WebSocket webSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                logger.info("🔗 WebSocket连接已建立");
                isConnected.set(true);
                connectionLatch.countDown();

                try {
                    callback.onStart();

                    // 发送TTS请求
                    sendTtsRequest(webSocket, text);

                } catch (Exception e) {
                    logger.error("发送TTS请求失败", e);
                    errorRef.set(e);
                    webSocket.close(1000, "发送请求失败");
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                try {
                    handleTextMessage(text, callback);
                } catch (Exception e) {
                    logger.error("处理文本消息失败", e);
                    errorRef.set(e);
                    webSocket.close(1000, "处理消息失败");
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, okio.ByteString bytes) {
                try {
                    handleBinaryMessage(bytes.toByteArray(), callback);
                } catch (Exception e) {
                    logger.error("处理二进制消息失败", e);
                    errorRef.set(e);
                    webSocket.close(1000, "处理消息失败");
                }
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                logger.info("🔚 WebSocket连接正在关闭 - 代码: {}, 原因: {}", code, reason);
                webSocket.close(code, reason);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                logger.info("❌ WebSocket连接已关闭 - 代码: {}, 原因: {}", code, reason);

                if (!isCompleted.get()) {
                    try {
                        callback.onComplete();
                    } catch (Exception e) {
                        logger.warn("调用完成回调失败", e);
                    }
                    isCompleted.set(true);
                }

                completionLatch.countDown();
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                logger.error("❌ WebSocket连接失败", t);
                errorRef.set(new Exception("WebSocket连接失败", t));

                if (!isConnected.get()) {
                    connectionLatch.countDown();
                }

                if (!isCompleted.get()) {
                    try {
                        callback.onError(t);
                    } catch (Exception e) {
                        logger.warn("调用错误回调失败", e);
                    }
                    isCompleted.set(true);
                }

                completionLatch.countDown();
            }
        });

        try {
            // 等待连接建立
            boolean connected = connectionLatch.await(WS_CONNECT_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!connected) {
                webSocket.close(1000, "连接超时");
                throw new Exception("WebSocket连接超时");
            }

            Exception connectionError = errorRef.get();
            if (connectionError != null) {
                throw connectionError;
            }

            // 等待处理完成
            boolean completed = completionLatch.await(TTS_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (!completed) {
                webSocket.close(1000, "处理超时");
                throw new Exception("TTS处理超时");
            }

            Exception processingError = errorRef.get();
            if (processingError != null) {
                throw processingError;
            }

            logger.info("✅ 自定义WebSocket流式TTS完成");

        } finally {
            // 确保WebSocket连接被关闭
            if (webSocket != null) {
                webSocket.close(1000, "处理完成");
            }
        }
    }

    /**
     * 构建WebSocket URL
     */
    private String buildWebSocketUrl() {
        // 将HTTP URL转换为WebSocket URL
        String wsUrl = apiUrl;
        if (wsUrl.startsWith("http://")) {
            wsUrl = wsUrl.replace("http://", "ws://");
        } else if (wsUrl.startsWith("https://")) {
            wsUrl = wsUrl.replace("https://", "wss://");
        }

        // 添加TTS端点路径（根据实际API调整）
        if (!wsUrl.endsWith("/")) {
            wsUrl += "/";
        }
        wsUrl += "tts/stream";

        logger.debug("构建WebSocket URL: {}", wsUrl);
        return wsUrl;
    }

    /**
     * 发送TTS请求到WebSocket服务器
     */
    private void sendTtsRequest(WebSocket webSocket, String text) throws Exception {
        // 构建TTS请求消息
        TtsRequest request = new TtsRequest();
        request.text = text;
        request.voice = voiceName;
        request.format = "pcm"; // 请求PCM格式音频
        request.sampleRate = 16000; // 16kHz采样率
        request.channels = 1; // 单声道

        String requestJson = JsonUtil.toJson(request);
        logger.debug("发送TTS请求: {}", requestJson);

        // 发送文本消息
        boolean sent = webSocket.send(requestJson);
        if (!sent) {
            throw new Exception("发送TTS请求失败");
        }

        logger.info("📤 TTS请求已发送");
    }

    /**
     * 处理文本消息（通常是状态或错误信息）
     */
    private void handleTextMessage(String message, StreamTtsCallback callback) throws Exception {
        logger.debug("收到文本消息: {}", message);

        try {
            // 解析响应消息
            TtsResponse response = JsonUtil.fromJson(message, TtsResponse.class);

            if (response.status != null) {
                switch (response.status) {
                    case "started":
                        logger.info("🎬 TTS处理已开始");
                        break;
                    case "processing":
                        logger.debug("⚙️ TTS正在处理...");
                        break;
                    case "completed":
                        logger.info("✅ TTS处理完成");
                        callback.onComplete();
                        break;
                    case "error":
                        String errorMsg = response.message != null ? response.message : "未知错误";
                        logger.error("❌ TTS处理错误: {}", errorMsg);
                        callback.onError(new Exception("TTS处理错误: " + errorMsg));
                        break;
                    default:
                        logger.warn("⚠️ 未知状态: {}", response.status);
                        break;
                }
            }

        } catch (Exception e) {
            logger.warn("解析文本消息失败: {}", message, e);
            // 不抛出异常，继续处理其他消息
        }
    }

    /**
     * 处理二进制消息（音频数据）
     */
    private void handleBinaryMessage(byte[] audioData, StreamTtsCallback callback) throws Exception {
        if (audioData == null || audioData.length == 0) {
            logger.debug("收到空音频数据");
            return;
        }

        logger.trace("📦 收到音频数据块，大小: {} bytes", audioData.length);

        // 假设服务器返回的是PCM格式音频数据
        // 如果是其他格式（如MP3、WAV），需要进行格式转换
        byte[] pcmData = audioData;

        // 如果需要格式转换，可以在这里添加
        // 例如：pcmData = AudioUtils.mp3ToPcm(audioData);

        // 回调音频数据
        callback.onAudioChunk(pcmData);
    }

    /**
     * TTS请求数据类
     */
    private static class TtsRequest {
        @JsonProperty("text")
        public String text;

        @JsonProperty("voice")
        public String voice;

        @JsonProperty("format")
        public String format;

        @JsonProperty("sample_rate")
        public int sampleRate;

        @JsonProperty("channels")
        public int channels;
    }

    /**
     * TTS响应数据类
     */
    private static class TtsResponse {
        @JsonProperty("status")
        public String status;

        @JsonProperty("message")
        public String message;

        @JsonProperty("audio_length")
        public Integer audioLength;

        @JsonProperty("chunk_index")
        public Integer chunkIndex;
    }
}
