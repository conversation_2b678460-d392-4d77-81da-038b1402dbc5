package cn.naii.iot.dialogue.tts.providers;

import cn.hutool.core.util.StrUtil;
import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.utils.AudioUtils;
import cn.naii.iot.utils.RetryUtils;
import com.alibaba.dashscope.aigc.multimodalconversation.AudioParameters;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import io.reactivex.Flowable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
public class AliyunTtsService implements TtsService {
    private static final Logger logger = LoggerFactory.getLogger(AliyunTtsService.class);

    private static final String PROVIDER_NAME = "aliyun";

    // 重试配置
    private static final RetryUtils.RetryConfig RETRY_CONFIG = RetryUtils.RetryConfig.builder()
            .maxAttempts(3)
            .retryDelayMs(1000)
            .build();

    // 平滑请求策略：避免瞬时高峰触发阿里云系统保护
    // 根据阿里云官方建议：采用平滑请求策略（匀速调度），将请求均匀分散在时间窗口内
    private static final long REQUEST_INTERVAL_MS = 200; // 每个请求间隔200ms
    private static final AtomicLong lastRequestTime = new AtomicLong(0);

    // 阿里云配置
    private final String apiKey;
    private final String voiceName;
    private final String outputPath;

    public AliyunTtsService(SysConfig config,
            String voiceName, String outputPath) {
        this.apiKey = config.getApiKey();
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean isSupportStreamTts() {
        // 阿里云Qwen-TTS支持流式
        // Sambert和Cosyvoice暂不支持流式
        return getVoiceByName(voiceName) != null;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        // 非流式TTS支持：cosyvoice-v2 和 qwen-tts
        // Sambert不支持非流式
        if (voiceName.contains("sambert")) {
            throw new UnsupportedOperationException(
                "阿里云TTS - " + voiceName + " 不支持非流式,请使用cosyvoice-v2或qwen-tts");
        }

        try {
            // 判断使用哪个模型
            if (getVoiceByName(voiceName) != null) {
                // Qwen-TTS音色，使用qwen-tts（RPM=100,000，几乎不会触发限流）
                return ttsQwenTts(text);
            } else {
                // Cosyvoice音色，使用cosyvoice-v2
                return ttsCosyvoice(text);
            }
        } catch (Exception e) {
            logger.error("语音合成aliyun -使用{}模型语音合成失败：", voiceName, e);
            throw new Exception("语音合成失败");
        }
    }

    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        // 检查是否支持流式
        if (!isSupportStreamTts()) {
            throw new UnsupportedOperationException(
                "阿里云TTS - " + voiceName + " 不支持流式,仅Qwen-TTS系列支持流式");
        }

        // 🆕 平滑请求策略：避免瞬时高峰触发阿里云系统保护
        smoothRequestRate();

        logger.info("🎬 开始阿里云Qwen-TTS流式合成 - 文本长度: {}, 语音: {}", text.length(), voiceName);

        try {
            callback.onStart();

            AudioParameters.Voice voice = getVoiceByName(voiceName);
            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .model("qwen-tts")
                    .apiKey(apiKey)
                    .text(text)
                    .voice(voice)
                    .build();

            MultiModalConversation conv = new MultiModalConversation();

            // 🆕 使用streamCall进行流式调用
            Flowable<MultiModalConversationResult> result = conv.streamCall(param);

            AtomicInteger chunkCount = new AtomicInteger(0);

            // 🆕 处理流式响应
            result.blockingForEach(chunk -> {
                try {
                    // 检查是否有音频数据
                    if (chunk.getOutput() != null &&
                        chunk.getOutput().getAudio() != null &&
                        chunk.getOutput().getAudio().getData() != null &&
                        !chunk.getOutput().getAudio().getData().isEmpty()) {

                        // 解码Base64音频数据
                        // 注意：阿里云流式返回的是Base64编码的PCM数据(24kHz采样率, 16位, 单声道)，不是WAV格式
                        // 参考文档：https://help.aliyun.com/zh/model-studio/qwen-tts
                        String base64Audio = chunk.getOutput().getAudio().getData();
                        byte[] pcmData24k = Base64.getDecoder().decode(base64Audio);

                        // 将24kHz PCM重采样为16kHz以匹配系统配置
                        byte[] pcmData16k = AudioUtils.resamplePcm(pcmData24k, 24000, 16000);

                        // 回调重采样后的PCM数据
                        callback.onAudioChunk(pcmData16k);

                        int count = chunkCount.incrementAndGet();
                        if (count == 1) {
                            logger.info("📦 接收首个阿里云音频块 - 原始: {} bytes (24kHz) -> 重采样: {} bytes (16kHz)",
                                pcmData24k.length, pcmData16k.length);
                        }
                        logger.trace("📦 接收阿里云音频块 #{}, 原始: {} bytes -> 重采样: {} bytes",
                            count, pcmData24k.length, pcmData16k.length);
                    }

                    // 检查是否完成
                    if (chunk.getOutput() != null &&
                        "stop".equals(chunk.getOutput().getFinishReason())) {
                        logger.info("✅ 阿里云Qwen-TTS流式合成完成 - 总块数: {}", chunkCount.get());
                    }

                } catch (Exception e) {
                    logger.warn("⚠️ 处理阿里云音频块失败: {}", e.getMessage());
                    throw e;
                }
            });

            callback.onComplete();

        } catch (Exception e) {
            logger.error("❌ 阿里云Qwen-TTS流式合成失败: {}", e.getMessage(), e);
            callback.onError(e);
            throw e;
        }
    }

    private AudioParameters.Voice getVoiceByName(String voiceName) {
        switch (voiceName) {
            case "Chelsie":
                return AudioParameters.Voice.CHELSIE;
            case "Cherry":
                return AudioParameters.Voice.CHERRY;
            case "Ethan":
                return AudioParameters.Voice.ETHAN;
            case "Serena":
                return AudioParameters.Voice.SERENA;
            default:
                return null;
        }
    }

    // qwen-tts非流式合成（支持Qwen-TTS音色，RPM=100,000）
    private String ttsQwenTts(String text) {
        AudioParameters.Voice voice = getVoiceByName(voiceName);
        if (voice == null) {
            logger.error("语音合成aliyun - 不支持的Qwen-TTS音色: {}", voiceName);
            return StrUtil.EMPTY;
        }

        // 🆕 平滑请求策略：避免瞬时高峰触发阿里云系统保护
        smoothRequestRate();

        RetryUtils.RetryConfig config = RetryUtils.RetryConfig.builder()
                .maxAttempts(3)
                .retryDelayMs(1000)
                .operationName("qwen-tts语音合成")
                .build();

        return RetryUtils.executeWithRetryOrNull(() -> {
            // 构建非流式请求参数
            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .model("qwen-tts")
                    .apiKey(apiKey)
                    .text(text)
                    .voice(voice)
                    .build();

            MultiModalConversation conv = new MultiModalConversation();
            MultiModalConversationResult result = conv.call(param);

            // 检查返回结果
            if (result == null || result.getOutput() == null || result.getOutput().getAudio() == null) {
                throw new RuntimeException("qwen-tts返回结果为null");
            }

            // 获取音频URL（非流式返回的是URL，不是Base64数据）
            String audioUrl = result.getOutput().getAudio().getUrl();
            if (StrUtil.isEmpty(audioUrl)) {
                throw new RuntimeException("qwen-tts返回的音频URL为空");
            }

            logger.info("✅ qwen-tts非流式合成成功 - 音频URL: {}", audioUrl);

            // 下载音频文件并转换为16kHz
            String outPath = outputPath + getAudioFileName();
            downloadAndResampleAudio(audioUrl, outPath);

            return outPath;
        }, config);
    }

    /**
     * 下载音频文件并重采样为16kHz
     */
    private void downloadAndResampleAudio(String audioUrl, String outPath) throws IOException {
        File file = new File(outPath);

        // 下载WAV文件（qwen-tts返回的是24kHz WAV）
        java.net.URL url = new java.net.URL(audioUrl);
        try (java.io.InputStream in = url.openStream();
             FileOutputStream fos = new FileOutputStream(file)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
        logger.info("✅ qwen-tts音频文件下载成功 (24kHz): {}", outPath);

        // 重采样为16kHz（覆盖原文件）
        AudioUtils.resampleWavTo16kHz(outPath);
        logger.info("✅ qwen-tts音频文件重采样完成 (24kHz -> 16kHz): {}", outPath);
    }

    // cosyvoice默认并发只有3个，所以需要增加一个重试机制
    private String ttsCosyvoice(String text) {
        // 🆕 平滑请求策略：避免瞬时高峰触发阿里云系统保护
        smoothRequestRate();

        RetryUtils.RetryConfig config = RetryUtils.RetryConfig.builder()
                .maxAttempts(3)
                .retryDelayMs(1000)
                .operationName("cosyvoice-v2语音合成")
                .build();

        return RetryUtils.executeWithRetryOrNull(() -> {
            com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam param =
                    com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam.builder()
                            .apiKey(apiKey)
                            .model("cosyvoice-v2")
                            .voice(voiceName)
                            .format(com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat.WAV_16000HZ_MONO_16BIT)
                            .build();

            com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer synthesizer =
                    new com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer(param, null);
            ByteBuffer audio = synthesizer.call(text);

            // 检查返回的ByteBuffer是否为null
            if (audio == null) {
                throw new RuntimeException("cosyvoice-v2返回结果为null");
            }

            String outPath = outputPath + getAudioFileName();
            File file = new File(outPath);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(audio.array());
            }

            logger.info("✅ cosyvoice-v2语音合成成功: {}", outPath);
            return outPath;
        }, config);
    }

    /**
     * 平滑请求速率 - 避免瞬时高峰触发阿里云系统保护
     * 根据阿里云官方建议：采用平滑请求策略（匀速调度），将请求均匀分散在时间窗口内
     *
     * 阿里云官方文档说明：
     * "Request rate increased too quickly：表示在未达到RPM或TPM限流条件时，
     * 因调用频率在短时间内激增，触发了系统稳定性保护机制。
     * 建议优化客户端调用逻辑，采用平滑请求策略（如匀速调度、指数退避或请求队列缓冲），
     * 将请求均匀分散在时间窗口内，避免瞬时高峰。"
     */
    private static void smoothRequestRate() {
        long now = System.currentTimeMillis();
        long lastTime = lastRequestTime.get();
        long timeSinceLastRequest = now - lastTime;

        // 如果距离上次请求时间小于间隔，则等待
        if (timeSinceLastRequest < REQUEST_INTERVAL_MS) {
            long waitTime = REQUEST_INTERVAL_MS - timeSinceLastRequest;
            try {
                logger.debug("⏱️ 阿里云TTS平滑请求速率 - 等待 {}ms", waitTime);
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("⚠️ 阿里云TTS平滑请求等待被中断");
            }
        }

        // 更新最后请求时间
        lastRequestTime.set(System.currentTimeMillis());
    }


}