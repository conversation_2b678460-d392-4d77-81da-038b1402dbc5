package cn.naii.iot.dialogue.tts.providers;

import com.alibaba.dashscope.aigc.multimodalconversation.AudioParameters;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.audio.tts.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.tts.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.tts.SpeechSynthesizer;
import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.utils.AudioUtils;

import cn.hutool.core.util.StrUtil;

import io.reactivex.Flowable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

public class AliyunTtsService implements TtsService {
    private static final Logger logger = LoggerFactory.getLogger(AliyunTtsService.class);

    private static final String PROVIDER_NAME = "aliyun";
    // 添加重试次数常量
    private static final int MAX_RETRY_ATTEMPTS = 3;
    // 添加重试间隔常量（毫秒）
    private static final long RETRY_DELAY_MS = 1000;
    // 添加TTS操作超时时间（秒）
    private static final long TTS_TIMEOUT_SECONDS = 5;

    // 🆕 全局请求间隔（毫秒）- 用于避免阿里云限流
    // 阿里云TTS有账号级别的QPS限制，需要在所有请求之间添加延迟
    // 默认500ms，可通过配置文件修改
    private static long requestIntervalMs = 500;

    // 🆕 全局锁，用于控制请求频率
    private static final Object REQUEST_LOCK = new Object();
    private static volatile long lastRequestTime = 0;

    // 使用共享的线程池，避免频繁创建和销毁
    private static final ExecutorService sharedExecutor = Executors.newCachedThreadPool();

    // 阿里云配置
    private final String apiKey;
    private final String voiceName;
    private final String outputPath;

    public AliyunTtsService(SysConfig config,
            String voiceName, String outputPath) {
        this.apiKey = config.getApiKey();
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    /**
     * 🆕 设置全局请求间隔（由外部配置注入）
     */
    public static void setRequestIntervalMs(long intervalMs) {
        requestIntervalMs = intervalMs;
        logger.info("⚙️ 阿里云TTS全局请求间隔设置为: {}ms", intervalMs);
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean isSupportStreamTts() {
        // 🆕 阿里云Qwen-TTS支持流式
        // Sambert和Cosyvoice暂不支持流式
        return getVoiceByName(voiceName) != null;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        try {
            if (voiceName.contains("sambert")) {
                return ttsSambert(text);
            } else if (getVoiceByName(voiceName) != null) {
                return ttsQwen(text);
            } else {
                return ttsCosyvoice(text);
            }
        } catch (Exception e) {
            logger.error("语音合成aliyun -使用{}模型语音合成失败：", voiceName, e);
            throw new Exception("语音合成失败");
        }
    }

    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        // 检查是否支持流式
        if (!isSupportStreamTts()) {
            throw new UnsupportedOperationException(
                "阿里云TTS - " + voiceName + " 不支持流式,仅Qwen-TTS系列支持流式");
        }

        logger.info("🎬 开始阿里云Qwen-TTS流式合成 - 文本长度: {}, 语音: {}", text.length(), voiceName);

        try {
            // 🆕 等待请求间隔，避免限流
            waitForRequestInterval();

            callback.onStart();

            AudioParameters.Voice voice = getVoiceByName(voiceName);
            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .model("qwen-tts")
                    .apiKey(apiKey)
                    .text(text)
                    .voice(voice)
                    .build();

            MultiModalConversation conv = new MultiModalConversation();

            // 🆕 使用streamCall进行流式调用
            Flowable<MultiModalConversationResult> result = conv.streamCall(param);

            AtomicInteger chunkCount = new AtomicInteger(0);

            // 🆕 处理流式响应
            result.blockingForEach(chunk -> {
                try {
                    // 检查是否有音频数据
                    if (chunk.getOutput() != null &&
                        chunk.getOutput().getAudio() != null &&
                        chunk.getOutput().getAudio().getData() != null &&
                        !chunk.getOutput().getAudio().getData().isEmpty()) {

                        // 解码Base64音频数据
                        // 注意：阿里云流式返回的是Base64编码的PCM数据(24kHz采样率, 16位, 单声道)，不是WAV格式
                        // 参考文档：https://help.aliyun.com/zh/model-studio/qwen-tts
                        String base64Audio = chunk.getOutput().getAudio().getData();
                        byte[] pcmData24k = Base64.getDecoder().decode(base64Audio);

                        // 将24kHz PCM重采样为16kHz以匹配系统配置
                        byte[] pcmData16k = AudioUtils.resamplePcm(pcmData24k, 24000, 16000);

                        // 回调重采样后的PCM数据
                        callback.onAudioChunk(pcmData16k);

                        int count = chunkCount.incrementAndGet();
                        if (count == 1) {
                            logger.info("📦 接收首个阿里云音频块 - 原始: {} bytes (24kHz) -> 重采样: {} bytes (16kHz)",
                                pcmData24k.length, pcmData16k.length);
                        }
                        logger.trace("📦 接收阿里云音频块 #{}, 原始: {} bytes -> 重采样: {} bytes",
                            count, pcmData24k.length, pcmData16k.length);
                    }

                    // 检查是否完成
                    if (chunk.getOutput() != null &&
                        "stop".equals(chunk.getOutput().getFinishReason())) {
                        logger.info("✅ 阿里云Qwen-TTS流式合成完成 - 总块数: {}", chunkCount.get());
                    }

                } catch (Exception e) {
                    logger.warn("⚠️ 处理阿里云音频块失败: {}", e.getMessage());
                    throw e;
                }
            });

            callback.onComplete();

        } catch (Exception e) {
            logger.error("❌ 阿里云Qwen-TTS流式合成失败: {}", e.getMessage(), e);
            callback.onError(e);
            throw e;
        }
    }

    private String ttsQwen(String text) {
        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                // 🆕 等待请求间隔，避免限流
                waitForRequestInterval();

                AudioParameters.Voice voice = getVoiceByName(voiceName);
                MultiModalConversationParam param = MultiModalConversationParam.builder()
                        .model("qwen-tts")
                        .apiKey(apiKey)
                        .text(text)
                        .voice(voice)
                        .build();

                // 使用共享线程池而不是每次创建新的
                Future<MultiModalConversationResult> future = sharedExecutor.submit(() -> {
                    MultiModalConversation conv = new MultiModalConversation();
                    return conv.call(param);
                });
                
                // 等待结果，设置超时
                MultiModalConversationResult result;
                try {
                    result = future.get(TTS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    logger.warn("语音合成aliyun - 使用{}模型超时，正在重试 ({}/{})", voiceName, attempts + 1, MAX_RETRY_ATTEMPTS);
                    attempts++;
                    if (attempts >= MAX_RETRY_ATTEMPTS) {
                        logger.error("语音合成aliyun - 使用{}模型多次超时，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    continue;
                }
                
                // 检查结果是否有效
                if (result == null || result.getOutput() == null || 
                    result.getOutput().getAudio() == null || 
                    result.getOutput().getAudio().getUrl() == null) {
                    
                    logger.warn("语音合成aliyun - 使用{}模型返回无效结果，正在重试 ({}/{})", voiceName, attempts + 1, MAX_RETRY_ATTEMPTS);
                    attempts++;
                    if (attempts >= MAX_RETRY_ATTEMPTS) {
                        logger.error("语音合成aliyun - 使用{}模型多次返回无效结果，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    continue;
                }
                
                String audioUrl = result.getOutput().getAudio().getUrl();
                String tempWavPath = outputPath + getAudioFileName();
                File tempWavFile = new File(tempWavPath);

                // 下载音频文件到本地（24kHz WAV格式），也使用共享线程池
                Future<Boolean> downloadFuture = sharedExecutor.submit(() -> {
                    try (InputStream in = new URL(audioUrl).openStream();
                            FileOutputStream out = new FileOutputStream(tempWavFile)) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = in.read(buffer)) != -1) {
                            out.write(buffer, 0, bytesRead);
                        }
                        return true;
                    } catch (Exception e) {
                        return false;
                    }
                });

                try {
                    Boolean downloadSuccess = downloadFuture.get(TTS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                    if (!downloadSuccess) {
                        throw new IOException("下载音频文件失败");
                    }
                } catch (TimeoutException e) {
                    downloadFuture.cancel(true);
                    logger.warn("语音合成aliyun - 使用{}模型下载音频超时，正在重试 ({}/{})", voiceName, attempts + 1, MAX_RETRY_ATTEMPTS);
                    attempts++;
                    if (attempts >= MAX_RETRY_ATTEMPTS) {
                        logger.error("语音合成aliyun - 使用{}模型多次下载超时，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    continue;
                }

                // 🆕 将24kHz WAV转换为16kHz WAV
                // 阿里云Qwen-TTS非流式返回的是24kHz WAV格式
                try {
                    String outPath = outputPath + getAudioFileName();

                    // 使用ffmpeg将24kHz WAV转换为16kHz WAV
                    String[] command = {
                        "ffmpeg",
                        "-i", tempWavPath,
                        "-ar", "16000",  // 重采样为16kHz
                        "-ac", "1",      // 单声道
                        "-y",            // 覆盖输出文件
                        outPath
                    };

                    Process process = Runtime.getRuntime().exec(command);
                    int exitCode = process.waitFor();

                    if (exitCode != 0) {
                        throw new IOException("ffmpeg转换失败，退出代码: " + exitCode);
                    }

                    // 删除临时24kHz WAV文件
                    tempWavFile.delete();

                    logger.debug("✅ 阿里云非流式TTS音频转换完成 - 24kHz WAV -> 16kHz WAV: {}", outPath);

                    return outPath;
                } catch (Exception e) {
                    logger.error("❌ 阿里云非流式TTS音频转换失败", e);
                    // 转换失败，删除临时文件
                    if (tempWavFile.exists()) {
                        tempWavFile.delete();
                    }
                    throw new IOException("音频转换失败: " + e.getMessage(), e);
                }
            } catch (Exception e) {
                attempts++;
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    logger.warn("语音合成aliyun - 使用{}模型失败，正在重试 ({}/{}): {}", voiceName, attempts, MAX_RETRY_ATTEMPTS, e.getMessage());
                    try {
                        // 等待一段时间后重试
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("重试等待被中断", ie);
                        return StrUtil.EMPTY;
                    }
                } else {
                    logger.error("语音合成aliyun - 使用{}模型语音合成失败，已达到最大重试次数：", voiceName, e);
                    return StrUtil.EMPTY;
                }
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 🆕 等待请求间隔，避免阿里云限流
     * 使用全局锁确保所有请求之间有足够的间隔
     */
    private static void waitForRequestInterval() {
        synchronized (REQUEST_LOCK) {
            long now = System.currentTimeMillis();
            long timeSinceLastRequest = now - lastRequestTime;

            if (timeSinceLastRequest < requestIntervalMs) {
                long waitTime = requestIntervalMs - timeSinceLastRequest;
                try {
                    logger.debug("⏱️ 等待 {}ms 以避免阿里云限流", waitTime);
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            lastRequestTime = System.currentTimeMillis();
        }
    }

    private AudioParameters.Voice getVoiceByName(String voiceName) {
        switch (voiceName) {
            case "Chelsie":
                return AudioParameters.Voice.CHELSIE;
            case "Cherry":
                return AudioParameters.Voice.CHERRY;
            case "Ethan":
                return AudioParameters.Voice.ETHAN;
            case "Serena":
                return AudioParameters.Voice.SERENA;
            default:
                return null;
        }
    }

    // cosyvoice默认并发只有3个，所以需要增加一个重试机制
    private String ttsCosyvoice(String text) {
        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                // 🆕 等待请求间隔，避免限流
                waitForRequestInterval();

                com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam param =
                com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam.builder()
                                .apiKey(apiKey)
                                .model("cosyvoice-v2")
                                .voice(voiceName)
                                .format(com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat.WAV_16000HZ_MONO_16BIT)
                                .build();

                // 使用共享线程池
                Future<ByteBuffer> future = sharedExecutor.submit(() -> {
                    com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer synthesizer =
                        new com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer(param, null);
                    return synthesizer.call(text);
                });
                
                // 等待结果，设置超时
                ByteBuffer audio;
                try {
                    audio = future.get(TTS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    logger.warn("语音合成aliyun - 使用{}模型超时，正在重试 ({}/{})", voiceName, attempts + 1, MAX_RETRY_ATTEMPTS);
                    attempts++;
                    if (attempts >= MAX_RETRY_ATTEMPTS) {
                        logger.error("语音合成aliyun - 使用{}模型多次超时，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    continue;
                }
                
                // 检查返回的ByteBuffer是否为null
                if (audio == null) {
                    attempts++;
                    if (attempts < MAX_RETRY_ATTEMPTS) {
                        logger.warn("语音合成aliyun - 使用{}模型返回null，正在重试 ({}/{})", voiceName, attempts, MAX_RETRY_ATTEMPTS);
                        // 等待一段时间后重试
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                        continue;
                    } else {
                        logger.error("语音合成aliyun - 使用{}模型多次返回null，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                }
                
                String outPath = outputPath + getAudioFileName();
                File file = new File(outPath);
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    fos.write(audio.array());
                } catch (IOException e) {
                    logger.error("语音合成aliyun -使用{}模型语音合成失败：", voiceName, e);
                    return StrUtil.EMPTY;
                }
                return outPath;
            } catch (Exception e) {
                attempts++;
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    logger.warn("语音合成aliyun - 使用{}模型失败，正在重试 ({}/{}): {}", voiceName, attempts, MAX_RETRY_ATTEMPTS, e.getMessage());
                    try {
                        // 等待一段时间后重试
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("重试等待被中断", ie);
                        return StrUtil.EMPTY;
                    }
                } else {
                    logger.error("语音合成aliyun -使用{}模型语音合成失败，已达到最大重试次数：", voiceName, e);
                    return StrUtil.EMPTY;
                }
            }
        }
        return StrUtil.EMPTY;
    }

    public String ttsSambert(String text) {
        int attempts = 0;
        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                // 🆕 等待请求间隔，避免限流
                waitForRequestInterval();

                SpeechSynthesisParam param = SpeechSynthesisParam.builder()
                        .apiKey(apiKey)
                        .model(voiceName)
                        .text(text)
                        .sampleRate(AudioUtils.SAMPLE_RATE)
                        .format(SpeechSynthesisAudioFormat.WAV)
                        .build();

                // 使用共享线程池
                Future<ByteBuffer> future = sharedExecutor.submit(() -> {
                    SpeechSynthesizer synthesizer = new SpeechSynthesizer();
                    return synthesizer.call(param);
                });
                
                // 等待结果，设置超时
                ByteBuffer audio;
                try {
                    audio = future.get(TTS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    logger.warn("语音合成aliyun - 使用{}模型超时，正在重试 ({}/{})，文本：{}", voiceName, attempts + 1, MAX_RETRY_ATTEMPTS, text);
                    attempts++;
                    if (attempts >= MAX_RETRY_ATTEMPTS) {
                        logger.error("语音合成aliyun - 使用{}模型多次超时，放弃重试，文本：{}", voiceName, text);
                        return StrUtil.EMPTY;
                    }
                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    continue;
                }
                
                // 检查返回的ByteBuffer是否为null
                if (audio == null) {
                    attempts++;
                    if (attempts < MAX_RETRY_ATTEMPTS) {
                        logger.warn("语音合成aliyun - 使用{}模型返回null，正在重试 ({}/{})", voiceName, attempts, MAX_RETRY_ATTEMPTS);
                        // 等待一段时间后重试
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                        continue;
                    } else {
                        logger.error("语音合成aliyun - 使用{}模型多次返回null，放弃重试", voiceName);
                        return StrUtil.EMPTY;
                    }
                }
                
                String outPath = outputPath + getAudioFileName();
                File file = new File(outPath);
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    fos.write(audio.array());
                } catch (IOException e) {
                    logger.error("语音合成aliyun - 使用{}模型失败：", voiceName, e);
                    return StrUtil.EMPTY;
                }
                return outPath;
            } catch (Exception e) {
                attempts++;
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    logger.warn("语音合成aliyun - 使用{}模型失败，正在重试 ({}/{}): {}", voiceName, attempts, MAX_RETRY_ATTEMPTS, e.getMessage());
                    try {
                        // 等待一段时间后重试
                        TimeUnit.MILLISECONDS.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("重试等待被中断", ie);
                        return StrUtil.EMPTY;
                    }
                } else {
                    logger.error("语音合成aliyun - 使用{}模型失败，已达到最大重试次数：", voiceName, e);
                    return StrUtil.EMPTY;
                }
            }
        }
        return StrUtil.EMPTY;
    }

}