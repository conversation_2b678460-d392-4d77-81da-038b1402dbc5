package cn.naii.iot.dialogue.tts.providers;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.TtsService;
import cn.naii.iot.entity.SysConfig;
import cn.naii.iot.utils.AudioUtils;
import cn.naii.iot.utils.HttpUtil;
import cn.naii.iot.utils.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HexFormat;

@Slf4j
public class MiniMaxTtsService implements TtsService {

    private static final String PROVIDER_NAME = "minimax";

    private final String groupId;
    private final String apiKey;

    private final String outputPath;
    private final String voiceName;

    private final OkHttpClient client = HttpUtil.client;
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public MiniMaxTtsService(SysConfig config, String voiceName, String outputPath) {
        this.groupId = config.getAppId();
        this.apiKey = config.getApiKey();
        this.voiceName = voiceName;
        this.outputPath = outputPath;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String audioFormat() {
        return "mp3";
    }

    @Override
    public boolean isSupportStreamTts() {
        // MiniMax支持流式TTS
        return true;
    }

    @Override
    public String textToSpeech(String text) throws Exception {
        var output = Paths.get(outputPath, getAudioFileName()).toString();
        sendRequest(text, output, false);
        return output;
    }

    @Override
    public void streamTextToSpeech(String text, StreamTtsCallback callback) throws Exception {
        log.info("🎬 开始MiniMax流式TTS - 文本长度: {}", text.length());

        var params = new Text2AudioParams(voiceName, text);
        params.setStream(true);  // 启用流式模式

        var request = new Request.Builder()
                .url("https://api.minimaxi.com/v1/t2a_v2?Groupid=%s".formatted(groupId))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer %s".formatted(apiKey))
                .post(RequestBody.create(JsonUtil.toJson(params), JSON))
                .build();

        try {
            callback.onStart();

            try (var response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "无响应体";
                    throw new IOException("MiniMax TTS请求失败: " + response.code() + ", " + errorBody);
                }

                // 处理SSE流
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(response.body().byteStream()))) {

                    String line;
                    int chunkCount = 0;

                    while ((line = reader.readLine()) != null) {
                        // SSE格式: "data: {json}"
                        if (line.startsWith("data: ")) {
                            String jsonData = line.substring(6);

                            try {
                                StreamChunk chunk = JsonUtil.fromJson(jsonData, StreamChunk.class);

                                if (chunk.data != null && chunk.data.audio != null && !chunk.data.audio.isEmpty()) {
                                    // 解码音频数据 (hex格式)
                                    byte[] audioBytes = HexFormat.of().parseHex(chunk.data.audio);

                                    // MiniMax返回的是MP3格式,需要转换为PCM
                                    byte[] pcmData = AudioUtils.mp3ToPcm(saveTemp(audioBytes));

                                    // 回调PCM数据
                                    callback.onAudioChunk(pcmData);
                                    chunkCount++;

                                    log.trace("📦 接收MiniMax音频块 #{}, 大小: {} bytes (PCM: {} bytes)",
                                        chunkCount, audioBytes.length, pcmData.length);
                                }

                                // 检查是否完成
                                if (chunk.data != null && chunk.data.status == 2) {
                                    log.info("✅ MiniMax流式TTS完成 - 总块数: {}", chunkCount);
                                    break;
                                }

                            } catch (Exception e) {
                                log.warn("⚠️ 解析MiniMax音频块失败: {}", e.getMessage());
                            }
                        }
                    }
                }

                callback.onComplete();

            } catch (IOException e) {
                callback.onError(e);
                throw e;
            }

        } catch (Exception e) {
            log.error("❌ MiniMax流式TTS失败", e);
            callback.onError(e);
            throw e;
        }
    }

    /**
     * 保存临时MP3文件用于转换
     */
    private String saveTemp(byte[] mp3Data) throws IOException {
        String tempPath = outputPath + "temp_" + System.currentTimeMillis() + ".mp3";
        Files.write(Paths.get(tempPath), mp3Data);
        return tempPath;
    }

    private void sendRequest(String text, String filepath, boolean stream) {
        var params = new Text2AudioParams(voiceName, text);
        params.setStream(stream);

        var request = new Request.Builder()
                .url("https://api.minimaxi.com/v1/t2a_v2?Groupid=%s".formatted(groupId))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer %s".formatted(apiKey)) // 添加Authorization头
                .post(RequestBody.create(JsonUtil.toJson(params), JSON))
                .build();

        try (var resp = client.newCall(request).execute()) {
            if (resp.isSuccessful()) {
                var respBody = JsonUtil.fromJson(resp.body().string(), Text2AudioResp.class);
                if (respBody.baseResp.statusCode == 0) {
                    var bytes = HexFormat.of().parseHex(respBody.data.audio);
                    Files.write(Paths.get(filepath), bytes);
                } else {
                    log.error("TTS失败 {}:{}", respBody.baseResp.statusCode, respBody.baseResp.statusMsg);
                }
            } else {
                log.error("TTS请求失败 {}", resp.body().string());
            }
        } catch (IOException e) {
            log.error("发送TTS请求时发生错误", e);
            throw new RuntimeException("发送TTS请求失败", e);
        }
    }

    /**
     * 流式响应数据块
     */
    @Data
    static class StreamChunk {
        private StreamData data;
        @JsonProperty("base_resp")
        private BaseResp baseResp;

        @Data
        static class StreamData {
            private String audio;  // hex格式的音频数据
            private int status;    // 0:进行中, 2:完成
        }

        record BaseResp(@JsonProperty("status_code") int statusCode, @JsonProperty("status_msg") String statusMsg) {
        }
    }

    @Data
    @Accessors(chain = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Text2AudioParams {

        public Text2AudioParams(String voiceId, String text) {
            this("speech-02-hd", voiceId, text);
        }

        public Text2AudioParams(String model, String voiceId, String text) {
            this.model = model;
            this.text = text;
            this.audioSetting = new AudioSetting();
            this.voiceSetting = new VoiceSetting().setVoiceId(voiceId);
        }

        private String model;
        private String text;
        private boolean stream = false;
        private String languageBoost = "auto";
        private String outputFormat = "hex";
        private VoiceSetting voiceSetting;
        private AudioSetting audioSetting;

        @Data
        @Accessors(chain = true)
        public static class VoiceSetting {
            @JsonProperty("voice_id")
            private String voiceId;
            private double speed = 1;
            private double vol = 1;
            private int pitch = 0;
            private String emotion = "happy";
        }

        @Data
        public static class AudioSetting {
            @JsonProperty("sample_rate")
            private int sampleRate = 32000;
            private int bitrate = 128000;
            private String format = "mp3";
        }
    }

    @Data
    public static class Text2AudioResp {
        private Data data;
        @JsonProperty("base_resp")
        private BaseResp baseResp;

        record Data(int status, String audio) {
        }

        record BaseResp(@JsonProperty("status_code") int statusCode, @JsonProperty("status_msg") String statusMsg) {
        }
    }

}
