package cn.naii.iot.dialogue.service;

import cn.naii.iot.communication.common.ChatSession;
import cn.naii.iot.communication.common.SessionManager;
import cn.naii.iot.dialogue.tts.StreamTtsProcessor;
import cn.naii.iot.utils.AudioUtils;
import cn.naii.iot.utils.OpusProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 音频服务，负责处理音频的流式和非流式发送
 */
@Service
public class AudioService {
    private static final Logger logger = LoggerFactory.getLogger(AudioService.class);

    // 帧发送时间间隔略小于OPUS_FRAME_DURATION_MS，避免因某些调度原因，导致没能在规定时间内发送，设备出现杂音
    private static final long OPUS_FRAME_SEND_INTERVAL_MS = AudioUtils.OPUS_FRAME_DURATION_MS;

    // 仅播放文本的 Sleep 时长
    private static final long ONLY_TEXT_SLEEP_TIME_MS = 1000;

    @Autowired
    private OpusProcessor opusProcessor;

    @Autowired
    private StreamTtsProcessor streamTtsProcessor;

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private MessageService messageService;

    // 使用虚拟线程池处理定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            Thread.ofVirtual().name("audio-scheduler-", 0).factory());

    // ==================== 非流式TTS状态管理 ====================
    // 存储每个会话最后一次发送帧的时间戳
    private final Map<String, AtomicLong> lastFrameSentTime = new ConcurrentHashMap<>();

    // 存储每个会话当前是否正在播放音频
    private final Map<String, AtomicBoolean> isPlaying = new ConcurrentHashMap<>();


    // 存储每个会话的调度任务
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    // 存储每个会话的音频发送任务
    private final Map<String, CompletableFuture<?>> sendAudioTasks = new ConcurrentHashMap<>();

    // 存储播放开始时间（纳秒）
    private final Map<String, Long> playStartTimes = new ConcurrentHashMap<>();

    // 存储播放位置（毫秒）
    private final Map<String, Long> playPositions = new ConcurrentHashMap<>();

    // ==================== 流式TTS状态管理 ====================
    // 存储流式TTS的播放状态
    private final Map<String, AtomicBoolean> streamingIsPlaying = new ConcurrentHashMap<>();

    // 存储流式TTS的播放开始时间
    private final Map<String, Long> streamingPlayStartTimes = new ConcurrentHashMap<>();

    // 存储流式TTS的播放位置
    private final Map<String, Long> streamingPlayPositions = new ConcurrentHashMap<>();

    /**
     * 发送TTS开始消息
     */
    public void sendStart(ChatSession session) {
        messageService.sendTtsMessage(session, null, "start");
    }

    /**
     * 发送TTS句子开始消息
     */
    public void sendSentenceStart(ChatSession session, String text) {
        messageService.sendTtsMessage(session, text, "sentence_start");
    }

    /**
     * 发送停止消息
     */
    public CompletableFuture<Void> sendStop(ChatSession session) {
        return sendStop(session, false);
    }

    /**
     * 发送停止消息
     */
    public CompletableFuture<Void> sendStop(ChatSession session, boolean stopByAudioTaskInner) {
        String sessionId = session.getSessionId();

        try {
            // 如果在播放音乐，则不停止
            if (sessionManager.isMusicPlaying(sessionId)) {
                return CompletableFuture.completedFuture(null);
            }

            // 标记播放结束
            AtomicBoolean playingState = isPlaying.computeIfAbsent(sessionId, k -> new AtomicBoolean());
            playingState.set(false);

            // 取消调度任务
            cancelScheduledTask(sessionId);

            // 清理播放时间信息
            cleanTimers(sessionId);

            // 延迟500ms后发送stop消息，确保设备完成音频播放
            CompletableFuture<Void> sendTtsMessageFuture = CompletableFuture.runAsync(() -> {
                messageService.sendTtsMessage(session, null, "stop");
            }, CompletableFuture.delayedExecutor(500, TimeUnit.MILLISECONDS));

            // 检查是否需要关闭会话
            if (sessionManager.isCloseAfterChat(sessionId)) {
                sendTtsMessageFuture.thenRun(() -> {
                    try {
                        // 🔧 修复：在关闭会话前，先发送goodbye消息通知设备进入待命状态
                        logger.info("📤 准备关闭会话，先发送goodbye消息 - SessionId: {}", sessionId);
                        messageService.sendGoodbyeMessage(session);

                        // 延迟200ms后关闭会话，确保设备收到goodbye消息
                        Thread.sleep(200);

                        logger.info("🔒 关闭会话 - SessionId: {}", sessionId);
                        sessionManager.closeSession(sessionId);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        logger.error("❌ 关闭会话时被中断 - SessionId: {}", sessionId, e);
                        sessionManager.closeSession(sessionId);
                    } catch (Exception e) {
                        logger.error("❌ 发送goodbye消息或关闭会话失败 - SessionId: {}", sessionId, e);
                        sessionManager.closeSession(sessionId);
                    }
                });
            }
            sessionManager.setPlaying(sessionId, false);

            if (!stopByAudioTaskInner) {
                CompletableFuture<?> sendAudioTask = sendAudioTasks.remove(sessionId);
                if (sendAudioTask != null && !sendAudioTask.isDone()) {
                    sendAudioTask.cancel(true);
                }
            }
            return sendTtsMessageFuture;
        } catch (Exception e) {
            logger.error("发送停止消息失败", e);
            AtomicBoolean playingState = isPlaying.computeIfAbsent(sessionId, k -> new AtomicBoolean());
            playingState.set(false);
            return CompletableFuture.completedFuture(null);
        }
    }

    /**
     * 检查会话是否正在播放音频
     */
    public boolean isPlaying(String sessionId) {
        return isPlaying.containsKey(sessionId) &&
                isPlaying.get(sessionId).get();
    }

    /**
     * 发送音频消息
     *
     * @param session  WebSocketSession会话
     * @param sentence 句子对象
     * @param isFirst  是否是开始消息
     * @param isLast   是否是结束消息
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioMessage(
            ChatSession session,
            DialogueService.Sentence sentence,
            boolean isFirst,
            boolean isLast) {

        String sessionId = session.getSessionId();
        String audioPath = sentence.getAudioPath();
        String text = sentence.getText();

        // 标记开始播放
        AtomicBoolean playingState = isPlaying.computeIfAbsent(sessionId, k -> new AtomicBoolean(true));
        playingState.set(true);

        // 创建一个 CompletableFuture 链来处理整个流程
        CompletableFuture<Void> startFuture = isFirst ? CompletableFuture.runAsync(() -> sendStart(session))
                : CompletableFuture.completedFuture(null);
        
        logger.info("向设备发送音频消息（sendAudioMessage） - SessionId: {}, 文本: {}, 音频路径: {}", sessionId, text, audioPath);

        if (sentence.getStream() == null) {
            if (text != null && !text.isEmpty()) {
                // 检查是否是纯表情符号（通过检查句子是否有moods但没有实际文本内容）
                boolean isOnlyEmoji = sentence.getMoods() != null && !sentence.getMoods().isEmpty() &&
                        (text.trim().length() <= 4); // 表情符号通常不超过4个字符

                if (isOnlyEmoji) {
                    // 纯表情符号，只发送表情，不发送文本
                    CompletableFuture<Void> emotionFuture = startFuture.thenRun(() -> sendSentenceEmotion(session, sentence, null));

                    final AtomicBoolean finalPlayingState = playingState;

                    return emotionFuture.thenCompose(v -> {
                        finalPlayingState.set(false);
                        try {
                            TimeUnit.MILLISECONDS.sleep(ONLY_TEXT_SLEEP_TIME_MS);
                        } catch (InterruptedException e) {
                            logger.error("等待表情播放失败", e);
                        }

                        if (isLast) {
                            return sendStop(session);
                        }
                        return CompletableFuture.completedFuture(null);
                    });
                } else {
                    // 有实际文本内容，发送异常提示
                    CompletableFuture<Void> sentenceStartFuture = startFuture.thenRun(() -> sendSentenceStart(session, text));

                    // 发送句子表情
                    CompletableFuture<Void> emotionFuture = sentenceStartFuture.thenRun(() -> sendSentenceEmotion(session, sentence, null));

                    // 使用单独的变量存储播放状态引用
                    final AtomicBoolean finalPlayingState = playingState;

                    return emotionFuture.thenCompose(v -> {
                        finalPlayingState.set(false);
                        try {
                            TimeUnit.MILLISECONDS.sleep(ONLY_TEXT_SLEEP_TIME_MS);
                        } catch (InterruptedException e) {
                            logger.error("等待异常提示播放失败", e);
                        }

                        if (isLast) {
                            return sendStop(session);
                        }
                        return CompletableFuture.completedFuture(null);
                    });
                }
            }
            // 如果没有音频路径但是结束消息，发送结束标记
            if (isLast) {
                return startFuture.thenCompose(v -> sendStop(session));
            }
            playingState.set(false);
            return startFuture;
        }

        // 使用单独的变量存储播放状态引用
        final AtomicBoolean finalPlayingState = playingState;

        // 发送句子开始标记
        CompletableFuture<Void> sentenceStartFuture = startFuture.thenRun(() -> sendSentenceStart(session, text));

        // 发送句子表情
        CompletableFuture<Void> emotionFuture = sentenceStartFuture.thenRun(() -> sendSentenceEmotion(session, sentence, null));

        return sentence.getStream() ? emotionFuture.thenCompose(v -> {
            // 确保播放状态为true
            finalPlayingState.set(true);
            // 创建发送帧的CompletableFuture
            CompletableFuture<Void> sendFramesFuture = new CompletableFuture<>();
            try {
                // 初始化播放时间和位置
                playStartTimes.put(sessionId, System.nanoTime());
                playPositions.put(sessionId, 0L);

                // 创建帧发送任务，从第一帧开始通过调度器发送
                final int[] frameIndex = {0};

                Runnable frameTask = new Runnable() {
                    @Override
                    public void run() {
//                        logger.info("开始处理帧流，当前第{}帧", frameIndex[0]);
                        Boolean answerStreamFinished = sentence.isAnswerStreamFinished();
//                        logger.info("流处理状态：{}", answerStreamFinished);
                        try {
                            List<byte[]> answerFrames = sentence.getAnswerFrames();
//                            logger.info("sentence获取到的帧数：{}", answerFrames.size());
                            // 帧发送完毕
                            if ((answerStreamFinished && frameIndex[0] >= answerFrames.size()) || !session.isOpen()) {
//                                logger.info("帧处理完毕，进入收尾工作");
                                endTask(sessionId, sendFramesFuture);
                                return;
                            }
                            // 新的帧未生成
                            if (frameIndex[0] >= answerFrames.size()) {
                                // 继续下一次处理
//                                logger.info("新的帧未生成，进入下一轮循环");
                                scheduleNextFrame(sessionId, this);
                                return;
                            }
                            // 更新活跃时间
                            sessionManager.updateLastActivity(sessionId);

                            // 发送当前帧
                            byte[] frame = answerFrames.get(frameIndex[0]++);
//                            logger.info("发送帧");
                            sendOpusFrame(session, frame);

                            // 更新播放位置
                            Long position = playPositions.get(sessionId);
                            if (position != null) {
                                playPositions.put(sessionId, position + OPUS_FRAME_SEND_INTERVAL_MS);
                            }

                            // 继续下一轮处理
                            scheduleNextFrame(sessionId, this);
                        } catch (Exception e) {
                            // 发生错误，取消调度任务
                            logger.error("流式帧处理失败", e);
                            endTask(sessionId, sendFramesFuture, e);
                        }
                    }
                };
                scheduleNextFrame(sessionId, frameTask);
            } catch (Exception e) {
                logger.error("音频帧发送初始化失败", e);
                endTask(sessionId, sendFramesFuture, e);
            }
            return sendFramesFuture;
        }).whenComplete((result, error) -> {
            // 无论成功还是失败，都标记播放结束
            finalPlayingState.set(false);

            // 取消调度任务
            cancelScheduledTask(sessionId);

            // 清理播放时间信息
            cleanTimers(sessionId);
        }).thenCompose(v -> {
            // 发送停止消息（只有在isLast为true时才发送）
            if (isLast) {
                return sendStop(session, true);
            }
            return CompletableFuture.completedFuture(null);
        }).exceptionally(error -> {
            logger.error("发送音频消息失败", error);

            // 如果发生错误但仍然是结束消息，确保发送stop
            if (isLast) {
                try {
                    sendStop(session, true);
                } catch (Exception e) {
                    logger.error("发送停止消息失败", e);
                }
            }
            return null;
        })
                :
                // 非流式处理音频文件
                emotionFuture.thenCompose(v -> CompletableFuture.supplyAsync(() -> {

                    String fullPath = audioPath;
                    // 添加null检查，防止TTS失败时audioPath为null导致NPE
                    if (fullPath == null) {
                        logger.warn("⚠️ 音频路径为null，跳过音频发送 - sessionId: {}, sequenceNumber: {}",
                            sessionId, sentence);
                        return null;
                    }

                    File audioFile = new File(fullPath);
                    if (!audioFile.exists()) {
                        logger.warn("⚠️ 音频文件不存在 - SessionId: {}, 路径: {}", sessionId, fullPath);
                        return null;
                    }

                    logger.debug("📁 开始读取音频文件 - SessionId: {}, 路径: {}, 大小: {} bytes",
                        sessionId, fullPath, audioFile.length());

                    List<byte[]> opusFrames;

                    try {
                        if (audioPath.contains(".opus")) {
                            // 如果是opus文件，直接读取opus帧数据
                            opusFrames = opusProcessor.readOpus(audioFile);
                            logger.debug("📁 读取Opus文件完成 - SessionId: {}, 帧数: {}", sessionId, opusFrames.size());
                        } else {
                            // 如果不是opus文件，按照原来的逻辑处理
                            byte[] audioData = AudioUtils.readAsPcm(fullPath);
                            logger.debug("📁 读取PCM数据完成 - SessionId: {}, 大小: {} bytes", sessionId, audioData.length);
                            // 将PCM转换为Opus帧
                            opusFrames = opusProcessor.pcmToOpus(sessionId, audioData, false);
                            logger.debug("📁 PCM转Opus完成 - SessionId: {}, 帧数: {}", sessionId, opusFrames.size());
                        }
                        return opusFrames;
                    } catch (Exception e) {
                        logger.error("❌ 处理音频文件失败 - SessionId: {}, 路径: {}", sessionId, fullPath, e);
                        return null;
                    }
                })).thenCompose(opusFrames -> {
                    if (opusFrames == null || opusFrames.isEmpty()) {
                        finalPlayingState.set(false);
                        if (isLast) {
                            return sendStop(session);
                        }
                        return CompletableFuture.completedFuture(null);
                    }

                    // 确保播放状态为true
                    finalPlayingState.set(true);

                    // 创建发送帧的CompletableFuture
                    CompletableFuture<Void> sendFramesFuture = new CompletableFuture<>();

                    try {
                        // 初始化播放时间和位置
                        playStartTimes.put(sessionId, System.nanoTime());
                        playPositions.put(sessionId, 0L);

                        // 创建帧发送任务，从第一帧开始通过调度器发送
                        final int[] frameIndex = {0};

                        Runnable frameTask = new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    if (!finalPlayingState.get() || frameIndex[0] >= opusFrames.size() || !session.isOpen()) {
                                        // 完成非流式音频处理
                                        logger.debug("📦 非流式音频发送完成 - SessionId: {}, 总帧数: {}, 当前索引: {}, 播放状态: {}, 会话开启: {}",
                                            sessionId, opusFrames.size(), frameIndex[0], finalPlayingState.get(), session.isOpen());
                                        endTask(sessionId, sendFramesFuture);
                                        return;
                                    }

                                    // 更新活跃时间
                                    sessionManager.updateLastActivity(sessionId);

                                    // 发送当前帧
                                    byte[] frame = opusFrames.get(frameIndex[0]++);
                                    sendOpusFrame(session, frame);

                                    // 更新播放位置
                                    Long position = playPositions.get(sessionId);
                                    if (position != null) {
                                        playPositions.put(sessionId, position + OPUS_FRAME_SEND_INTERVAL_MS);
                                    }

                                    // 每50帧打印一次进度
                                    if (frameIndex[0] % 50 == 0) {
                                        logger.debug("📦 非流式音频发送进度 - SessionId: {}, 已发送: {}/{}",
                                            sessionId, frameIndex[0], opusFrames.size());
                                    }

                                    // 计算下一帧的发送时间
                                    if (frameIndex[0] < opusFrames.size()) {
                                        scheduleNextFrame(sessionId, this);
                                    } else {
                                        // 所有帧已发送完成
                                        logger.info("📦 非流式音频所有帧发送完成 - SessionId: {}, 总帧数: {}", sessionId, opusFrames.size());
                                        endTask(sessionId, sendFramesFuture);
                                    }

                                } catch (Exception e) {
                                    // 发生错误，取消调度任务
                                    logger.error("非流式帧处理失败 - SessionId: {}, 当前帧索引: {}/{}",
                                        sessionId, frameIndex[0], opusFrames.size(), e);
                                    endTask(sessionId, sendFramesFuture, e);
                                }
                            }
                        };

                        // 启动帧发送调度
                        if (opusFrames.size() > 0) {
                            scheduleNextFrame(sessionId, frameTask);
                        } else {
                            // 没有帧需要发送
                            endTask(sessionId, sendFramesFuture);
                        }

                    } catch (Exception e) {
                        logger.error("音频帧发送初始化失败", e);
                        endTask(sessionId, sendFramesFuture, e);
                    }

                    // 返回帧发送Future
                    return sendFramesFuture;
                }).whenComplete((result, error) -> {
                    // 无论成功还是失败，都标记播放结束
                    finalPlayingState.set(false);

                    // 取消调度任务
                    cancelScheduledTask(sessionId);

                    // 清理播放时间信息
                    cleanTimers(sessionId);
                }).thenCompose(v -> {
                    // 发送停止消息（只有在isLast为true时才发送）
                    if (isLast) {
                        return sendStop(session, true);
                    }
                    return CompletableFuture.completedFuture(null);
                }).exceptionally(error -> {
                    logger.error("发送音频消息失败", error);

                    // 如果发生错误但仍然是结束消息，确保发送stop
                    if (isLast) {
                        try {
                            sendStop(session, true);
                        } catch (Exception e) {
                            logger.error("发送停止消息失败", e);
                        }
                    }
                    return null;
                });
    }

    /**
     * 发送Opus帧数据
     */
    public void sendOpusFrame(ChatSession session, byte[] opusFrame) throws IOException {
        messageService.sendBinaryMessage(session, opusFrame);
    }

    /**
     * 流式发送音频 - 接收PCM音频块并实时转换发送
     *
     * @param session          WebSocket会话
     * @param sentence         句子对象
     * @param isFirst          是否是第一个句子
     * @param isLast           是否是最后一个句子
     * @param pcmChunkConsumer 用于接收PCM音频块的消费者(由TTS流式回调提供)
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendStreamingAudio(
            ChatSession session,
            DialogueService.Sentence sentence,
            boolean isFirst,
            boolean isLast,
            java.util.function.Consumer<java.util.function.Consumer<byte[]>> pcmChunkConsumer) {

        String sessionId = session.getSessionId();
        String text = sentence.getText();

        logger.info("🎬 开始流式音频发送 - SessionId: {}, 文本: {}, isFirst: {}, isLast: {}",
                sessionId, text, isFirst, isLast);

        // 标记开始播放（使用流式专用状态）
        AtomicBoolean playingState = streamingIsPlaying.computeIfAbsent(sessionId, k -> new AtomicBoolean(true));
        playingState.set(true);

        // 发送开始消息
        CompletableFuture<Void> startFuture = isFirst ?
                CompletableFuture.runAsync(() -> sendStart(session)) :
                CompletableFuture.completedFuture(null);

        // 发送句子开始标记
        CompletableFuture<Void> sentenceStartFuture = startFuture
                .thenRun(() -> sendSentenceStart(session, text));

        // 发送句子表情
        CompletableFuture<Void> emotionFuture = sentenceStartFuture
                .thenRun(() -> sendSentenceEmotion(session, sentence, null));

        // 创建流式处理Future
        CompletableFuture<Void> streamingFuture = new CompletableFuture<>();

        emotionFuture.thenRun(() -> {
            try {
                // 初始化流式会话
                streamTtsProcessor.initStream(sessionId);

                // 初始化播放时间和位置
                playStartTimes.put(sessionId, System.nanoTime());
                playPositions.put(sessionId, 0L);

                // 帧计数器
                AtomicInteger frameCount = new AtomicInteger(0);

                // 定义PCM块处理器
                java.util.function.Consumer<byte[]> pcmChunkHandler = pcmChunk -> {
                    try {
                        if (!playingState.get() || !session.isOpen()) {
                            logger.warn("⚠️ 会话已关闭或停止播放,跳过PCM块");
                            return;
                        }

                        // 更新活跃时间
                        sessionManager.updateLastActivity(sessionId);

                        // 处理PCM块并转换为Opus帧
                        int sentFrames = streamTtsProcessor.processPcmChunk(
                                sessionId,
                                pcmChunk,
                                opusFrame -> {
                                    try {
                                        // 发送Opus帧
                                        sendOpusFrame(session, opusFrame);

                                        int count = frameCount.incrementAndGet();

                                        // 更新播放位置
                                        Long position = playPositions.get(sessionId);
                                        if (position != null) {
                                            playPositions.put(sessionId, position + OPUS_FRAME_SEND_INTERVAL_MS);
                                        }

                                        if (count % 50 == 0) {
                                            logger.debug("📦 已发送 {} 个流式Opus帧", count);
                                        }

                                    } catch (IOException e) {
                                        logger.error("❌ 发送流式Opus帧失败", e);
                                        throw new RuntimeException(e);
                                    }
                                }
                        );

                    } catch (Exception e) {
                        logger.error("❌ 处理流式PCM块失败", e);
                        streamingFuture.completeExceptionally(e);
                    }
                };

                // 调用TTS流式处理(通过消费者传入PCM块处理器)
                pcmChunkConsumer.accept(pcmChunkHandler);

                // 刷新剩余数据
                streamTtsProcessor.flushStream(sessionId, opusFrame -> {
                    try {
                        sendOpusFrame(session, opusFrame);
                        frameCount.incrementAndGet();
                    } catch (IOException e) {
                        logger.error("❌ 发送最后的Opus帧失败", e);
                        throw new RuntimeException(e);
                    }
                });

                logger.info("✅ 流式音频发送完成 - SessionId: {}, 总帧数: {}", sessionId, frameCount.get());

                // 清理流式会话
                streamTtsProcessor.cleanupStream(sessionId);

                streamingFuture.complete(null);

            } catch (Exception e) {
                logger.error("❌ 流式音频发送失败", e);
                streamingFuture.completeExceptionally(e);
            }
        });

        // 处理完成后的清理
        return streamingFuture.whenComplete((result, error) -> {
            // 标记播放结束
            playingState.set(false);

            // 清理播放时间信息
            cleanTimers(sessionId);

        }).thenCompose(v -> {
            // 发送停止消息(只有在isLast为true时才发送)
            if (isLast) {
                return sendStop(session, true);
            }
            return CompletableFuture.completedFuture(null);

        }).exceptionally(error -> {
            logger.error("❌ 流式音频发送异常", error);

            // 如果发生错误但仍然是结束消息,确保发送stop
            if (isLast) {
                try {
                    sendStop(session, true);
                } catch (Exception e) {
                    logger.error("发送停止消息失败", e);
                }
            }
            return null;
        });
    }

    /**
     * 发送表情信息。如果句子里没有分析出表情，则默认返回 happy
     */
    private void sendSentenceEmotion(ChatSession session, DialogueService.Sentence sentence, String defaultEmotion) {
        List<String> moods = sentence.getMoods();
        if (moods != null && !moods.isEmpty()) {
            messageService.sendEmotion(session, moods.get(0));
        } else if (defaultEmotion != null) {
            messageService.sendEmotion(session, defaultEmotion);
        }
    }

    /**
     * 清理会话资源
     */
    public void cleanupSession(String sessionId) {
        lastFrameSentTime.remove(sessionId);
        isPlaying.remove(sessionId);
        cleanTimers(sessionId);
        cancelScheduledTask(sessionId);
        opusProcessor.cleanup(sessionId);

        // 清理音频发送任务
        CompletableFuture<?> sendAudioTask = sendAudioTasks.remove(sessionId);
        if (sendAudioTask != null && !sendAudioTask.isDone()) {
            sendAudioTask.cancel(true);
            logger.info("已取消音频发送任务 - SessionId: {}", sessionId);
        }

        // 清理流式TTS状态
        streamingIsPlaying.remove(sessionId);
        streamingPlayStartTimes.remove(sessionId);
        streamingPlayPositions.remove(sessionId);
    }

    /**
     * 结束非流式音频发送任务
     *
     * @param sessionId 会话ID
     * @param future 任务的CompletableFuture
     */
    private void endTask(String sessionId, CompletableFuture<Void> future) {
        endTask(sessionId, future, null);
    }

    /**
     * 结束非流式音频发送任务（带异常）
     * <p>
     * 该方法负责清理任务相关的资源，包括：
     * - 取消调度任务
     * - 清理播放时间信息
     * - 完成或异常完成CompletableFuture
     * </p>
     *
     * @param sessionId 会话ID
     * @param future 任务的CompletableFuture
     * @param error 异常信息，如果为null则表示正常完成
     */
    private void endTask(String sessionId, CompletableFuture<Void> future, Throwable error) {
        // 取消调度任务
        cancelScheduledTask(sessionId);

        // 清理播放时间信息
        cleanTimers(sessionId);

        // 完成Future
        if (error != null) {
            future.completeExceptionally(error);
        } else {
            future.complete(null);
        }
    }

    /**
     * 清理播放计时器资源
     * <p>
     * 清理会话的播放开始时间和播放位置信息
     * </p>
     *
     * @param sessionId 会话ID
     */
    private void cleanTimers(String sessionId) {
        playStartTimes.remove(sessionId);
        playPositions.remove(sessionId);
    }

    /**
     * 计算并调度下一帧的发送时间
     * <p>
     * 该方法使用纳秒级精度计算下一帧的发送时间，确保音频帧按照精确的时间间隔发送。
     * 计算公式：expectedTime = startTime + position * 1_000_000
     * 其中position是以毫秒为单位的播放位置。
     * </p>
     *
     * @param sessionId 会话ID
     * @param frameTask 帧发送任务
     */
    private void scheduleNextFrame(String sessionId, Runnable frameTask) {
        Long startTime = playStartTimes.get(sessionId);
        Long position = playPositions.get(sessionId);

        if (startTime == null || position == null) {
            // 如果没有时间信息，使用固定间隔
            ScheduledFuture<?> future = scheduler.schedule(frameTask, OPUS_FRAME_SEND_INTERVAL_MS, TimeUnit.MILLISECONDS);
            scheduledTasks.put(sessionId, future);
            return;
        }

        // 计算预期发送时间（纳秒级精度）
        long expectedTime = startTime + position * 1_000_000;
        long currentTime = System.nanoTime();
        long delayNanos = expectedTime - currentTime;

        ScheduledFuture<?> future;
        if (delayNanos <= 0) {
            // 如果当前时间已经超过预期时间，立即发送
            future = scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
        } else {
            // 延迟到精确时间点再发送
            future = scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
        }

        scheduledTasks.put(sessionId, future);
    }

    /**
     * 取消会话的调度任务
     * <p>
     * 取消当前会话正在进行的帧发送调度任务。
     * 如果任务已经完成或不存在，则不执行任何操作。
     * </p>
     *
     * @param sessionId 会话ID
     */
    public void cancelScheduledTask(String sessionId) {
        ScheduledFuture<?> task = scheduledTasks.remove(sessionId);
        if (task != null && !task.isDone()) {
            task.cancel(false);
        }
    }

}