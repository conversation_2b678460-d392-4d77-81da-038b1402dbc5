package cn.naii.iot.dialogue.service;

import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DecimalFormat;

/**
 * 音频处理管道计时器
 * 用于跟踪从音频接收到响应发送的整个处理流程的各个阶段耗时
 */
@Getter
@Setter
public class PipelineTiming {
    private static final Logger logger = LoggerFactory.getLogger(PipelineTiming.class);
    private static final DecimalFormat df = new DecimalFormat("0.000");
    
    private final String sessionId;
    
    // 1. 音频接收阶段
    private Long audioReceivedTime;           // 设备音频接收时间
    
    // 2. VAD处理阶段
    private Long vadStartTime;                // VAD开始处理时间
    private Long vadSpeechDetectedTime;       // VAD检测到语音开始时间
    private Long vadSpeechEndTime;            // VAD检测到语音结束时间
    
    // 3. STT处理阶段
    private Long sttStartTime;                // STT开始时间
    private Long sttCompleteTime;             // STT完成时间
    
    // 4. LLM处理阶段
    private Long llmStartTime;                // LLM开始时间
    private Long llmFirstTokenTime;           // LLM第一个token生成时间
    private Long llmCompleteTime;             // LLM完成时间
    
    // 5. TTS处理阶段
    private Long ttsStartTime;                // TTS开始时间（首句）
    private Long ttsFirstAudioTime;           // TTS首句音频生成完成时间
    private Long ttsWarmupCompleteTime;       // 保温词TTS完成时间
    private Long ttsActualSpeechTime;         // 实际语音（非保温词）TTS完成时间
    
    // 6. 音频发送阶段
    private Long firstFrameSentTime;          // 第一帧音频发送时间
    private Long warmupFrameSentTime;         // 保温词音频发送时间
    private Long actualSpeechFrameSentTime;   // 实际语音第一帧发送时间
    private Long lastFrameSentTime;           // 最后一帧音频发送时间
    
    // 标记
    private boolean isWarmupWord = false;     // 是否包含保温词
    private String warmupWord;                // 保温词内容
    
    public PipelineTiming(String sessionId) {
        this.sessionId = sessionId;
    }
    
    /**
     * 记录音频接收时间
     */
    public void markAudioReceived() {
        this.audioReceivedTime = System.currentTimeMillis();
        logger.debug("⏱️ [{}] 音频接收 - 时间: {}", sessionId, audioReceivedTime);
    }
    
    /**
     * 记录VAD开始处理
     */
    public void markVadStart() {
        this.vadStartTime = System.currentTimeMillis();
    }
    
    /**
     * 记录VAD检测到语音开始
     */
    public void markVadSpeechDetected() {
        this.vadSpeechDetectedTime = System.currentTimeMillis();
        if (vadStartTime != null) {
            long duration = vadSpeechDetectedTime - vadStartTime;
            logger.info("⏱️ [{}] VAD检测到语音 - 耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录VAD检测到语音结束
     */
    public void markVadSpeechEnd() {
        this.vadSpeechEndTime = System.currentTimeMillis();
        if (vadSpeechDetectedTime != null) {
            long duration = vadSpeechEndTime - vadSpeechDetectedTime;
            logger.info("⏱️ [{}] VAD语音持续时间: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录STT开始
     */
    public void markSttStart() {
        this.sttStartTime = System.currentTimeMillis();
        logger.info("⏱️ [{}] STT开始处理", sessionId);
    }
    
    /**
     * 记录STT完成
     */
    public void markSttComplete() {
        this.sttCompleteTime = System.currentTimeMillis();
        if (sttStartTime != null) {
            long duration = sttCompleteTime - sttStartTime;
            logger.info("⏱️ [{}] STT处理完成 - 耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录LLM开始
     */
    public void markLlmStart() {
        this.llmStartTime = System.currentTimeMillis();
        logger.info("⏱️ [{}] LLM开始处理", sessionId);
    }
    
    /**
     * 记录LLM第一个token
     */
    public void markLlmFirstToken() {
        this.llmFirstTokenTime = System.currentTimeMillis();
        if (llmStartTime != null) {
            long duration = llmFirstTokenTime - llmStartTime;
            logger.info("⏱️ [{}] LLM首个token生成 - 耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录LLM完成
     */
    public void markLlmComplete() {
        this.llmCompleteTime = System.currentTimeMillis();
        if (llmStartTime != null) {
            long duration = llmCompleteTime - llmStartTime;
            logger.info("⏱️ [{}] LLM处理完成 - 总耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录TTS开始（首句）
     */
    public void markTtsStart() {
        this.ttsStartTime = System.currentTimeMillis();
        logger.info("⏱️ [{}] TTS开始处理（首句）", sessionId);
    }
    
    /**
     * 记录TTS首句音频生成完成
     */
    public void markTtsFirstAudioComplete() {
        this.ttsFirstAudioTime = System.currentTimeMillis();
        if (ttsStartTime != null) {
            long duration = ttsFirstAudioTime - ttsStartTime;
            logger.info("⏱️ [{}] TTS首句音频生成完成 - 耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录保温词TTS完成
     */
    public void markTtsWarmupComplete(String warmupWord) {
        this.ttsWarmupCompleteTime = System.currentTimeMillis();
        this.isWarmupWord = true;
        this.warmupWord = warmupWord;
        if (ttsStartTime != null) {
            long duration = ttsWarmupCompleteTime - ttsStartTime;
            logger.info("⏱️ [{}] 保温词TTS完成 - 内容: \"{}\" - 耗时: {}ms", sessionId, warmupWord, duration);
        }
    }
    
    /**
     * 记录实际语音TTS完成
     */
    public void markTtsActualSpeechComplete() {
        this.ttsActualSpeechTime = System.currentTimeMillis();
        Long baseTime = ttsWarmupCompleteTime != null ? ttsWarmupCompleteTime : ttsStartTime;
        if (baseTime != null) {
            long duration = ttsActualSpeechTime - baseTime;
            logger.info("⏱️ [{}] 实际语音TTS完成 - 耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录第一帧音频发送
     */
    public void markFirstFrameSent() {
        this.firstFrameSentTime = System.currentTimeMillis();
        if (audioReceivedTime != null) {
            long duration = firstFrameSentTime - audioReceivedTime;
            logger.info("⏱️ [{}] 第一帧音频发送 - 从接收到发送耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录保温词音频发送
     */
    public void markWarmupFrameSent() {
        this.warmupFrameSentTime = System.currentTimeMillis();
        if (audioReceivedTime != null) {
            long duration = warmupFrameSentTime - audioReceivedTime;
            logger.info("⏱️ [{}] 保温词音频播放 - 从接收到播放耗时: {}ms", sessionId, duration);
        }
    }
    
    /**
     * 记录实际语音第一帧发送
     */
    public void markActualSpeechFrameSent() {
        this.actualSpeechFrameSentTime = System.currentTimeMillis();
        if (audioReceivedTime != null) {
            long duration = actualSpeechFrameSentTime - audioReceivedTime;
            logger.info("⏱️ [{}] 实际语音播放 - 从接收到播放耗时: {}ms（不包含保温词）", sessionId, duration);
        }
    }
    
    /**
     * 记录最后一帧音频发送
     */
    public void markLastFrameSent() {
        this.lastFrameSentTime = System.currentTimeMillis();
    }
    
    /**
     * 打印完整的管道计时摘要
     */
    public void printSummary() {
        if (audioReceivedTime == null) {
            logger.warn("⏱️ [{}] 管道计时未初始化", sessionId);
            return;
        }

        StringBuilder summary = new StringBuilder();
        summary.append("\n");
        summary.append("╔════════════════════════════════════════════════════════════════════════════╗\n");
        summary.append(String.format("║ 音频处理管道计时摘要 - SessionId: %-40s ║\n", sessionId));
        summary.append("╠════════════════════════════════════════════════════════════════════════════╣\n");

        // 各阶段耗时
        summary.append("║ 阶段耗时:                                                                  ║\n");

        // VAD处理
        if (vadSpeechDetectedTime != null && vadStartTime != null) {
            long vadDuration = vadSpeechDetectedTime - vadStartTime;
            summary.append(String.format("║   1. VAD处理:     %6.2fs                                                   ║\n", vadDuration / 1000.0));
        }

        // STT处理
        if (sttCompleteTime != null && sttStartTime != null) {
            long sttDuration = sttCompleteTime - sttStartTime;
            summary.append(String.format("║   2. STT处理:     %6.2fs                                                   ║\n", sttDuration / 1000.0));
        }

        // LLM处理
        if (llmCompleteTime != null && llmStartTime != null) {
            long llmDuration = llmCompleteTime - llmStartTime;
            if (llmFirstTokenTime != null && llmFirstTokenTime > llmStartTime) {
                long llmFirstTokenDuration = llmFirstTokenTime - llmStartTime;
                summary.append(String.format("║   3. LLM处理:     %6.2fs (首token: %.2fs)                                ║\n",
                    llmDuration / 1000.0, llmFirstTokenDuration / 1000.0));
            } else {
                summary.append(String.format("║   3. LLM处理:     %6.2fs                                                   ║\n",
                    llmDuration / 1000.0));
            }
        }

        // TTS处理
        if (ttsFirstAudioTime != null && ttsStartTime != null) {
            long ttsDuration = ttsFirstAudioTime - ttsStartTime;
            summary.append(String.format("║   4. TTS处理:     %6.2fs                                                   ║\n", ttsDuration / 1000.0));
        }

        summary.append("╠════════════════════════════════════════════════════════════════════════════╣\n");
        summary.append("║ 关键时间点:                                                                ║\n");

        // 音频接收到保温词播放
        if (warmupFrameSentTime != null && audioReceivedTime != null) {
            long warmupDelay = warmupFrameSentTime - audioReceivedTime;
            summary.append(String.format("║   • 音频接收 → 保温词播放:     %6.2fs                                     ║\n", warmupDelay / 1000.0));
        }

        // 音频接收到实际语音播放
        if (actualSpeechFrameSentTime != null && audioReceivedTime != null) {
            long actualSpeechDelay = actualSpeechFrameSentTime - audioReceivedTime;
            summary.append(String.format("║   • 音频接收 → 实际语音播放:   %6.2fs (不含保温词)                       ║\n", actualSpeechDelay / 1000.0));
        }

        // 端到端总耗时
        if (lastFrameSentTime != null && audioReceivedTime != null) {
            long totalDuration = lastFrameSentTime - audioReceivedTime;
            summary.append(String.format("║   • 端到端总耗时:               %6.2fs                                     ║\n", totalDuration / 1000.0));
        } else if (actualSpeechFrameSentTime != null && audioReceivedTime != null) {
            long totalDuration = actualSpeechFrameSentTime - audioReceivedTime;
            summary.append(String.format("║   • 端到端总耗时:               %6.2fs (到首帧发送)                       ║\n", totalDuration / 1000.0));
        }

        summary.append("╚════════════════════════════════════════════════════════════════════════════╝");

        logger.info(summary.toString());
    }
    
    /**
     * 计算VAD处理耗时
     */
    public Long getVadDuration() {
        if (vadSpeechDetectedTime != null && vadStartTime != null) {
            return vadSpeechDetectedTime - vadStartTime;
        }
        return null;
    }
    
    /**
     * 计算STT处理耗时
     */
    public Long getSttDuration() {
        if (sttCompleteTime != null && sttStartTime != null) {
            return sttCompleteTime - sttStartTime;
        }
        return null;
    }
    
    /**
     * 计算LLM处理耗时
     */
    public Long getLlmDuration() {
        if (llmCompleteTime != null && llmStartTime != null) {
            return llmCompleteTime - llmStartTime;
        }
        return null;
    }
    
    /**
     * 计算TTS处理耗时
     */
    public Long getTtsDuration() {
        if (ttsFirstAudioTime != null && ttsStartTime != null) {
            return ttsFirstAudioTime - ttsStartTime;
        }
        return null;
    }
    
    /**
     * 计算端到端总耗时
     */
    public Long getTotalDuration() {
        if (lastFrameSentTime != null && audioReceivedTime != null) {
            return lastFrameSentTime - audioReceivedTime;
        } else if (actualSpeechFrameSentTime != null && audioReceivedTime != null) {
            return actualSpeechFrameSentTime - audioReceivedTime;
        }
        return null;
    }
}

