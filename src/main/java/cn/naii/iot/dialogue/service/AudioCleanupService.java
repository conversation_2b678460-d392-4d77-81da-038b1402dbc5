package cn.naii.iot.dialogue.service;

import cn.naii.iot.utils.AudioUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 音频文件清理服务
 * 负责管理TTS生成的音频文件的生命周期
 */
@Slf4j
@Service
public class AudioCleanupService {

    // 配置参数
    @Value("${audio.cleanup.enabled:true}")
    private boolean cleanupEnabled;
    
    @Value("${audio.cleanup.session.delay.minutes:5}")
    private int sessionCleanupDelayMinutes;
    
    @Value("${audio.cleanup.scheduled.hours:24}")
    private int scheduledCleanupHours;
    
    @Value("${audio.cleanup.cache.enabled:true}")
    private boolean cacheEnabled;
    
    @Value("${audio.cleanup.cache.max.size:1000}")
    private int cacheMaxSize;
    
    @Value("${audio.cleanup.cache.ttl.hours:24}")
    private int cacheTtlHours;

    // 会话级文件跟踪: sessionId -> Set<文件路径>
    private final Map<String, Set<String>> sessionFiles = new ConcurrentHashMap<>();
    
    // 文件缓存: 文本hash -> 文件路径
    private final Map<String, CachedAudioFile> audioCache = new ConcurrentHashMap<>();
    
    // 待清理的会话: sessionId -> 清理时间戳
    private final Map<String, Long> pendingCleanup = new ConcurrentHashMap<>();

    /**
     * 缓存的音频文件信息
     */
    private static class CachedAudioFile {
        String filePath;
        long createTime;
        long lastAccessTime;
        int accessCount;
        
        CachedAudioFile(String filePath) {
            this.filePath = filePath;
            this.createTime = System.currentTimeMillis();
            this.lastAccessTime = this.createTime;
            this.accessCount = 1;
        }
        
        void access() {
            this.lastAccessTime = System.currentTimeMillis();
            this.accessCount++;
        }
        
        boolean isExpired(long ttlMillis) {
            return System.currentTimeMillis() - lastAccessTime > ttlMillis;
        }
    }

    @PostConstruct
    public void init() {
        if (cleanupEnabled) {
            log.info("🧹 音频清理服务已启动 - 会话延迟: {}分钟, 定时清理: {}小时, 缓存: {}", 
                sessionCleanupDelayMinutes, scheduledCleanupHours, cacheEnabled ? "启用" : "禁用");
        } else {
            log.warn("⚠️ 音频清理服务已禁用");
        }
    }

    /**
     * 注册会话生成的音频文件
     */
    public void registerSessionFile(String sessionId, String filePath) {
        if (!cleanupEnabled || filePath == null) {
            return;
        }
        
        sessionFiles.computeIfAbsent(sessionId, k -> ConcurrentHashMap.newKeySet())
            .add(filePath);
        
        log.debug("📝 注册会话文件 - SessionId: {}, 文件: {}", sessionId, filePath);
    }

    /**
     * 注册缓存的音频文件
     */
    public String getCachedAudio(String textHash) {
        if (!cacheEnabled) {
            return null;
        }
        
        CachedAudioFile cached = audioCache.get(textHash);
        if (cached != null) {
            // 检查文件是否仍然存在
            if (new File(cached.filePath).exists()) {
                cached.access();
                log.debug("✅ 缓存命中 - Hash: {}, 文件: {}, 访问次数: {}", 
                    textHash, cached.filePath, cached.accessCount);
                return cached.filePath;
            } else {
                // 文件已被删除，移除缓存
                audioCache.remove(textHash);
                log.debug("❌ 缓存失效(文件不存在) - Hash: {}", textHash);
            }
        }
        return null;
    }

    /**
     * 添加到缓存
     */
    public void cacheAudio(String textHash, String filePath) {
        if (!cacheEnabled || textHash == null || filePath == null) {
            return;
        }
        
        // 检查缓存大小限制
        if (audioCache.size() >= cacheMaxSize) {
            evictOldestCache();
        }
        
        audioCache.put(textHash, new CachedAudioFile(filePath));
        log.debug("💾 添加到缓存 - Hash: {}, 文件: {}", textHash, filePath);
    }

    /**
     * 驱逐最旧的缓存项
     */
    private void evictOldestCache() {
        if (audioCache.isEmpty()) {
            return;
        }
        
        // 找到最久未访问的项
        String oldestKey = audioCache.entrySet().stream()
            .min(Comparator.comparingLong(e -> e.getValue().lastAccessTime))
            .map(Map.Entry::getKey)
            .orElse(null);
        
        if (oldestKey != null) {
            CachedAudioFile removed = audioCache.remove(oldestKey);
            log.debug("🗑️ 驱逐缓存 - Hash: {}, 文件: {}", oldestKey, removed.filePath);
        }
    }

    /**
     * 标记会话结束，延迟清理
     */
    public void scheduleSessionCleanup(String sessionId) {
        if (!cleanupEnabled || sessionId == null) {
            return;
        }
        
        long cleanupTime = System.currentTimeMillis() + 
            TimeUnit.MINUTES.toMillis(sessionCleanupDelayMinutes);
        pendingCleanup.put(sessionId, cleanupTime);
        
        log.info("⏰ 计划清理会话文件 - SessionId: {}, 延迟: {}分钟", 
            sessionId, sessionCleanupDelayMinutes);
    }

    /**
     * 立即清理会话文件
     */
    public void cleanupSessionNow(String sessionId) {
        if (!cleanupEnabled || sessionId == null) {
            return;
        }
        
        Set<String> files = sessionFiles.remove(sessionId);
        pendingCleanup.remove(sessionId);
        
        if (files == null || files.isEmpty()) {
            return;
        }
        
        int deletedCount = 0;
        int cachedCount = 0;
        
        for (String filePath : files) {
            // 检查是否在缓存中
            boolean inCache = audioCache.values().stream()
                .anyMatch(cached -> cached.filePath.equals(filePath));
            
            if (inCache) {
                cachedCount++;
                log.debug("💾 保留缓存文件 - 文件: {}", filePath);
                continue;
            }
            
            // 删除文件
            try {
                File file = new File(filePath);
                if (file.exists() && file.delete()) {
                    deletedCount++;
                    log.debug("🗑️ 删除会话文件 - 文件: {}", filePath);
                }
            } catch (Exception e) {
                log.warn("⚠️ 删除文件失败 - 文件: {}, 错误: {}", filePath, e.getMessage());
            }
        }
        
        log.info("🧹 会话文件清理完成 - SessionId: {}, 删除: {}, 保留(缓存): {}", 
            sessionId, deletedCount, cachedCount);
    }

    /**
     * 定时检查待清理的会话
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void checkPendingCleanup() {
        if (!cleanupEnabled) {
            return;
        }
        
        long now = System.currentTimeMillis();
        List<String> toClean = new ArrayList<>();
        
        pendingCleanup.forEach((sessionId, cleanupTime) -> {
            if (now >= cleanupTime) {
                toClean.add(sessionId);
            }
        });
        
        for (String sessionId : toClean) {
            cleanupSessionNow(sessionId);
        }
    }

    /**
     * 定时清理过期文件
     * 根据配置的小时数执行
     */
    @Scheduled(cron = "${audio.cleanup.scheduled.cron:0 0 */6 * * ?}")
    public void scheduledCleanup() {
        if (!cleanupEnabled) {
            return;
        }
        
        log.info("🧹 开始定时音频文件清理...");
        
        try {
            long cutoffTime = System.currentTimeMillis() - 
                TimeUnit.HOURS.toMillis(scheduledCleanupHours);
            
            cleanupOldFiles(cutoffTime);
            cleanupExpiredCache();
            
        } catch (Exception e) {
            log.error("❌ 定时清理失败", e);
        }
    }

    /**
     * 清理旧文件
     */
    private void cleanupOldFiles(long cutoffTime) throws IOException {
        Path audioPath = Paths.get(AudioUtils.AUDIO_PATH);
        if (!Files.exists(audioPath)) {
            return;
        }
        
        Set<String> cachedFiles = audioCache.values().stream()
            .map(cached -> cached.filePath)
            .collect(Collectors.toSet());
        
        Set<String> sessionActiveFiles = sessionFiles.values().stream()
            .flatMap(Set::stream)
            .collect(Collectors.toSet());
        
        int[] stats = {0, 0, 0}; // [删除, 保留(缓存), 保留(会话)]
        
        Files.walkFileTree(audioPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                try {
                    String filePath = file.toString();
                    
                    // 跳过缓存文件
                    if (cachedFiles.contains(filePath)) {
                        stats[1]++;
                        return FileVisitResult.CONTINUE;
                    }
                    
                    // 跳过活跃会话文件
                    if (sessionActiveFiles.contains(filePath)) {
                        stats[2]++;
                        return FileVisitResult.CONTINUE;
                    }
                    
                    // 检查文件年龄
                    long fileTime = attrs.lastModifiedTime().toMillis();
                    if (fileTime < cutoffTime) {
                        Files.delete(file);
                        stats[0]++;
                        log.debug("🗑️ 删除过期文件 - 文件: {}, 年龄: {}小时", 
                            filePath, TimeUnit.MILLISECONDS.toHours(System.currentTimeMillis() - fileTime));
                    }
                    
                } catch (Exception e) {
                    log.warn("⚠️ 处理文件失败 - 文件: {}, 错误: {}", file, e.getMessage());
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        log.info("📊 文件清理统计 - 删除: {}, 保留(缓存): {}, 保留(会话): {}", 
            stats[0], stats[1], stats[2]);
    }

    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache() {
        if (!cacheEnabled) {
            return;
        }
        
        long ttlMillis = TimeUnit.HOURS.toMillis(cacheTtlHours);
        List<String> expiredKeys = new ArrayList<>();
        
        audioCache.forEach((key, cached) -> {
            if (cached.isExpired(ttlMillis)) {
                expiredKeys.add(key);
            }
        });
        
        for (String key : expiredKeys) {
            audioCache.remove(key);
        }
        
        if (!expiredKeys.isEmpty()) {
            log.info("🗑️ 清理过期缓存 - 数量: {}", expiredKeys.size());
        }
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cleanupEnabled", cleanupEnabled);
        stats.put("cacheEnabled", cacheEnabled);
        stats.put("activeSessions", sessionFiles.size());
        stats.put("pendingCleanup", pendingCleanup.size());
        stats.put("cacheSize", audioCache.size());
        stats.put("cacheMaxSize", cacheMaxSize);
        
        // 计算缓存命中率
        long totalAccess = audioCache.values().stream()
            .mapToLong(cached -> cached.accessCount)
            .sum();
        stats.put("totalCacheAccess", totalAccess);
        
        return stats;
    }
}

