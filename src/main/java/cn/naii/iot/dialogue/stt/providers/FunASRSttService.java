package cn.naii.iot.dialogue.stt.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import cn.naii.iot.dialogue.stt.SttService;
import cn.naii.iot.entity.SysConfig;

import org.apache.commons.lang3.StringUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Sinks;

import java.net.URI;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * FunASR STT服务实现
 * <br/>
 * <a href="https://github.com/modelscope/FunASR/blob/main/runtime/docs/SDK_tutorial_online_zh.md">FunASR实时语音听写便捷部署教程</a>
 *  <br/>
 * <a href="https://github.com/modelscope/FunASR/blob/main/runtime/docs/SDK_advanced_guide_online_zh.md">FunASR实时语音听写服务开发指南</a>
 *  <br/>
 * <a href="https://www.funasr.com/static/offline/index.html">体验地址</a>
 */
public class FunASRSttService implements SttService {

    private static final Logger logger = LoggerFactory.getLogger(FunASRSttService.class);
    private static final String PROVIDER_NAME = "funasr";

    private static final String SPEAKING_START = "{\"mode\":\"online\",\"wav_name\":\"voice.wav\",\"is_speaking\":true,\"wav_format\":\"pcm\",\"chunk_size\":[5,10,5],\"itn\":true}";
    private static final String SPEAKING_END = "{\"is_speaking\": false}";
    private static final int QUEUE_TIMEOUT_MS = 100; // 队列等待超时时间
    private static final long RECOGNITION_TIMEOUT_MS = 30000; // 识别超时时间（30秒）

    private final String apiUrl;

    public FunASRSttService(SysConfig config) {
        this.apiUrl = config.getApiUrl();
        logger.info("初始化FunASR STT服务，API地址: {}", apiUrl);
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public String recognition(byte[] audioData) {
        logger.warn("不支持，请使用流式识别");
        return StringUtils.EMPTY;
    }

    @Override
    public boolean supportsStreaming() {
        return true;
    }

    @Override
    public String streamRecognition(Sinks.Many<byte[]> audioSink) {
        logger.info("开始流式语音识别，API地址: {}", apiUrl);
        logger.debug("请求参数[SPEAKING_START]: {}", SPEAKING_START);
        
        // 使用阻塞队列存储音频数据
        BlockingQueue<byte[]> audioQueue = new LinkedBlockingQueue<>();
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        AtomicReference<String> finalResult = new AtomicReference<>("");
        CountDownLatch recognitionLatch = new CountDownLatch(1);
        
        // 订阅Sink并将数据放入队列
        audioSink.asFlux().subscribe(
            data -> {
                audioQueue.offer(data);
                if (logger.isDebugEnabled()) {
                    logger.debug("接收到音频数据块，大小: {} bytes", data.length);
                }
            },
            error -> {
                logger.error("音频流处理错误", error);
                isCompleted.set(true);
            },
            () -> {
                logger.debug("音频流订阅完成");
                isCompleted.set(true);
            }
        );
        
        // 创建WebSocket客户端
        WebSocketClient webSocketClient = new WebSocketClient(URI.create(apiUrl)) {
            @Override
            public void onOpen(ServerHandshake handshake) {
                logger.info("FunASR WebSocket连接已打开，服务器响应码: {}，消息: {}", handshake.getHttpStatus(), handshake.getHttpStatusMessage());
                logger.debug("发送初始请求参数: {}", SPEAKING_START);
                send(SPEAKING_START);
                
                // 启动虚拟线程发送音频数据
                Thread.startVirtualThread(() -> {
                    logger.debug("启动音频数据发送线程");
                    try {
                        int chunkCount = 0;
                        while (!isCompleted.get() || !audioQueue.isEmpty()) {
                            byte[] audioChunk = null;
                            try {
                                audioChunk = audioQueue.poll(QUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                            } catch (InterruptedException e) {
                                logger.warn("音频数据队列等待被中断", e);
                                Thread.currentThread().interrupt(); // 重新设置中断标志
                                break;
                            }
                            
                            if (audioChunk != null && isOpen()) {
                                send(audioChunk);
                                chunkCount++;
                                if (logger.isDebugEnabled()) {
                                    logger.debug("发送音频数据块 #{}，大小: {} bytes", chunkCount, audioChunk.length);
                                }
                            }
                        }
                        logger.debug("总共发送了 {} 个音频数据块", chunkCount);
                        
                        // 发送结束信号
                        if (isOpen()) {
                            logger.debug("发送结束信号: {}", SPEAKING_END);
                            send(SPEAKING_END);
                        }
                    } catch (Exception e) {
                        logger.error("发送音频数据时发生错误", e);
                    }
                });
            }

            @Override
            public void onMessage(String message) {
                logger.debug("收到WebSocket消息: {}", message);
                try {
                    JSONObject jsonObject = JSON.parseObject(message);
                    boolean isFinal = jsonObject.getBoolean("code");
                    if (isFinal) {
                        String text = jsonObject.getString("text");
                        logger.info("收到最终识别结果: {}", text);
                        finalResult.set(text);
                        recognitionLatch.countDown(); // 识别完成，释放锁
                    } else {
                        String text = jsonObject.getString("text");
                        logger.debug("收到中间识别结果: {}", text);
                    }
                } catch (Exception e) {
                    logger.error("解析FunASR响应失败: {}", message, e);
                }
            }

            @Override
            public void onClose(int code, String reason, boolean remote) {
                logger.info("FunASR WebSocket连接关闭，状态码: {}, 远程关闭: {}, 原因: {}", code, remote, reason);
                // 确保锁被释放
                recognitionLatch.countDown();
            }

            @Override
            public void onError(Exception ex) {
                logger.error("FunASR WebSocket发生错误", ex);
                // 确保锁被释放
                recognitionLatch.countDown();
            }
        };

        try {
            // 连接WebSocket
            logger.debug("开始连接WebSocket");
            webSocketClient.connect();
            
            // 等待识别完成或超时
            logger.debug("等待识别完成，超时时间: {} ms", RECOGNITION_TIMEOUT_MS);
            boolean recognized = recognitionLatch.await(RECOGNITION_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            
            if (!recognized) {
                logger.warn("FunASR识别超时，超过 {} ms", RECOGNITION_TIMEOUT_MS);
            } else {
                logger.info("FunASR识别完成，结果: {}", finalResult.get());
            }
        } catch (Exception e) {
            logger.error("FunASR识别过程中发生错误", e);
        } finally {
            // 关闭WebSocket连接
            if (webSocketClient.isOpen()) {
                logger.debug("关闭WebSocket连接");
                webSocketClient.close();
            }
        }
        
        return finalResult.get();
    }
}