package cn.naii.iot.dialogue.llm.tool.mcp.device;

import cn.naii.iot.communication.domain.DeviceMcpMessage;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 设备端mcp相关属性
 */
@Data
public class DeviceMcpHolder {
    /**
     * mcp请求ID
     */
    private Long mcpRequestId = 10000L;
    /**
     * mcp初始化完成
     */
    private boolean mcpInitialized = false;
    /**
     * mcp指令阻塞请求表
     */
    private Map<Long, CompletableFuture<DeviceMcpMessage>> mcpPendingRequests = new HashMap<>();
    /**
     * mcp工具获取游标 用于分页，首次请求为空字符串
     */
    private String mcpCursor = "";

    public Long getMcpRequestId() {
        return mcpRequestId ++;
    }
}