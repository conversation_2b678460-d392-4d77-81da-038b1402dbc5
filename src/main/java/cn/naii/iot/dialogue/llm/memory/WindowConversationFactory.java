package cn.naii.iot.dialogue.llm.memory;

import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static cn.naii.iot.dialogue.llm.memory.MessageWindowConversation.DEFAULT_HISTORY_LIMIT;

@Service
public class WindowConversationFactory implements ConversationFactory{

    private final ChatMemory chatMemory;

    @Autowired
    public WindowConversationFactory(ChatMemory chatMemory) {
        this.chatMemory = chatMemory;
    }

    @Override
    public Conversation initConversation(SysDevice device, SysRole role, String sessionId) {
        Conversation conversation = MessageWindowConversation.builder().chatMemory(chatMemory)
                .maxMessages(DEFAULT_HISTORY_LIMIT)
                .role(role)
                .device(device)
                .sessionId(sessionId)
                .build();
        return conversation;
    }
}
