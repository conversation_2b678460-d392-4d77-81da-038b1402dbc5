package cn.naii.iot.dialogue.llm.memory;

import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.service.LocationCoordinateService;
import cn.naii.iot.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class WindowConversationFactory implements ConversationFactory{

    private final ChatMemory chatMemory;
    private final SysUserService userService;
    private final LocationCoordinateService locationCoordinateService;

    @Autowired
    public WindowConversationFactory(ChatMemory chatMemory, SysUserService userService,
                                     LocationCoordinateService locationCoordinateService) {
        this.chatMemory = chatMemory;
        this.userService = userService;
        this.locationCoordinateService = locationCoordinateService;
    }

    @Override
    public Conversation initConversation(SysDevice device, SysRole role, String sessionId) {
        return MessageWindowConversation.builder()
                .chatMemory(chatMemory)
                .maxMessages(MessageWindowConversation.DEFAULT_HISTORY_LIMIT)
                .role(role)
                .device(device)
                .sessionId(sessionId)
                .userService(userService)
                .locationCoordinateService(locationCoordinateService)
                .build();
    }
}
