package cn.naii.iot.dialogue.llm.memory;

import cn.naii.iot.entity.LocationCoordinate;
import cn.naii.iot.entity.SysDevice;
import cn.naii.iot.entity.SysMessage;
import cn.naii.iot.entity.SysRole;
import cn.naii.iot.entity.SysUser;
import cn.naii.iot.service.LocationCoordinateService;
import cn.naii.iot.service.SysUserService;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 限定消息条数（消息窗口）的Conversation实现。根据不同的策略，可实现聊天会话的持久化、加载、清除等功能。
 */
public class MessageWindowConversation extends Conversation {
    // 历史记录默认限制数量
    public static final int DEFAULT_HISTORY_LIMIT = 10;
    private final ChatMemory chatMemory;
    private final int maxMessages;
    private final SysUserService userService;
    private final LocationCoordinateService locationCoordinateService;
    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(MessageWindowConversation.class);


    public MessageWindowConversation(SysDevice device, SysRole role, String sessionId, int maxMessages,
                                     ChatMemory chatMemory, SysUserService userService,
                                     LocationCoordinateService locationCoordinateService){
        super(device, role, sessionId);
        this.maxMessages = maxMessages;
        this.chatMemory = chatMemory;
        this.userService = userService;
        this.locationCoordinateService = locationCoordinateService;
        logger.info("加载设备{}的普通消息(SysMessage.MESSAGE_TYPE_NORMAL)作为对话历史",device.getDeviceId());
        List<SysMessage> history = chatMemory.getMessages(device.getDeviceId(), SysMessage.MESSAGE_TYPE_NORMAL, maxMessages);
        super.messages.addAll(convert(history)) ;
    }

    public static class Builder {
        private SysDevice device;
        private SysRole role;
        private String sessionId;
        private int maxMessages;
        private ChatMemory chatMemory;
        private SysUserService userService;
        private LocationCoordinateService locationCoordinateService;

        public Builder device(SysDevice device) {
            this.device = device;
            return this;
        }

        public Builder role(SysRole role) {
            this.role = role;
            return this;
        }
        public Builder sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public Builder chatMemory(ChatMemory chatMemory) {
            this.chatMemory = chatMemory;
            return this;
        }

        public Builder maxMessages(int maxMessages) {
            this.maxMessages = maxMessages;
            return this;
        }

        public Builder userService(SysUserService userService) {
            this.userService = userService;
            return this;
        }

        public Builder locationCoordinateService(LocationCoordinateService locationCoordinateService) {
            this.locationCoordinateService = locationCoordinateService;
            return this;
        }

        public MessageWindowConversation build(){
            return new MessageWindowConversation(device, role, sessionId, maxMessages, chatMemory,
                                                userService, locationCoordinateService);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override
    public void clear() {
        messages().clear();
        chatMemory.clearMessages(device().getDeviceId());
    }


    /**
     * 添加消息
     * 后续考虑：继承封装UserMessage和AssistantMessage,UserMessageWithTime,AssistantMessageWithTime
     * 后续考虑：将function 或者 mcp 的相关信息封装在AssistantMessageWithTime，来精细处理。或者根据元数据判断是function_call还是mcp调用
     * @param userMessage
     * @param userTimeMillis
     * @param assistantMessage
     * @param assistantTimeMillis
     */
    @Override
    public void addMessage(UserMessage userMessage,  Long userTimeMillis, AssistantMessage assistantMessage, Long assistantTimeMillis) {

        //boolean hasToolCalls = assistantMessage.hasToolCalls();
        // 检查元数据中是否包含工具调用标识
        String toolName = (String) assistantMessage.getMetadata().get("toolName");

        // 发生了工具调用，获取函数调用的名称，通过名称反查类型
        // String functionName = chatResponse.getMetadata().get("function_name");

        boolean hasToolCalls = StringUtils.hasText(toolName);

        // 非function消息才加入对话历史，避免调用混乱。
        // 这个逻辑面对更多的工具调用时，可能是值得商榷的。有些工具调用的结果直接作为AssistantMessage加入对话历史并不会影响对话效果。
        // 后续考虑：在XiaozhiToolCallingManager实现类里，包装出的AssistantMessage由工具来添加标识是否影响对话效果。
        if(!hasToolCalls){
            // 更新缓存
            messages().add(userMessage);
            messages().add(assistantMessage);
        }

        // 判断消息类型（不是spring-ai的消息类型），同一轮对话里UserMessage和AssistantMessage的messageType相同
        String messageType = hasToolCalls ? SysMessage.MESSAGE_TYPE_FUNCTION_CALL : SysMessage.MESSAGE_TYPE_NORMAL;
        String deviceId = device().getDeviceId();
        int roleId = role().getRoleId();
        // 如果本轮对话是function_call或mcp调用(最后一条信息的类型)，把用户的消息类型也修正为同样类型
        chatMemory.addMessage(deviceId, sessionId(), userMessage.getMessageType().getValue(), userMessage.getText(),
                roleId, messageType, userTimeMillis);

        String response = assistantMessage.getText();
        if (StringUtils.hasText(response)) {
            chatMemory.addMessage(deviceId, sessionId(), assistantMessage.getMessageType().getValue(), response,
                    roleId, messageType, assistantTimeMillis);
        }
    }

    @Override
    public List<Message> prompt(UserMessage userMessage) {
        String roleDesc = role().getRoleDesc();

        // 填充用户信息到提示词
        if (StringUtils.hasText(roleDesc) && roleDesc.contains("%s")) {
            roleDesc = fillUserInfoToPrompt(roleDesc);
        }

        SystemMessage systemMessage = new SystemMessage(StringUtils.hasText(roleDesc)?roleDesc:"");

        final var historyMessages = messages();
        while (historyMessages.size() > maxMessages) {
            historyMessages.remove(0);
        }

        List<Message> messages = new ArrayList<>();
        messages.add(systemMessage);
        messages.addAll(historyMessages);
        messages.add(userMessage);

        return messages;
    }

    /**
     * 填充用户信息到提示词
     * 格式: 姓名: %s、出生时间: %s、出生地: %s、出生地经纬度: %s
     *
     * @param roleDesc 原始提示词
     * @return 填充后的提示词
     */
    private String fillUserInfoToPrompt(String roleDesc) {
        try {
            // 获取设备的用户ID
            Integer userId = device().getUserId();
            if (userId == null) {
                logger.warn("设备{}没有关联用户，无法填充用户信息", device().getDeviceId());
                return "用户信息异常：设备没有绑定用户，无法填充用户信息";
            }

            // 查询用户信息
            SysUser user = userService.selectUserByUserId(userId);
            if (user == null) {
                logger.warn("未找到用户ID为{}的用户信息", userId);
                return "用户信息异常：用户不存在，无法填充用户信息";
            }

            // 准备填充的数据
            String name = StringUtils.hasText(user.getName()) ? user.getName() : "";
            String gender = "";
            if (StringUtils.hasText(user.getAvatar()) && user.getGender() != null) {
                gender = switch (user.getGender()) {
                    case 1 -> "先生";
                    case 2 -> "女士";
                    default -> "未知";
                };
            }
            String birthTime = "";
            String birthPlace = "";
            String birthCoordinate = "";

            // 获取出生时间（birthDayTime已经是String类型，直接使用）
            if (StringUtils.hasText(user.getBirthDayTime())) {
                birthTime = user.getBirthDayTime();
            }

            // 获取出生地和经纬度信息
            if (StringUtils.hasText(user.getBirthPlace())) {
                try {
                    // birthPlace 存储的是地点名称字符串，格式如："辽宁省 沈阳市 和平区"
                    birthPlace = user.getBirthPlace();

                    // 将出生地字符串按空格分割成省、市、区
                    String[] addressParts = birthPlace.trim().split("\\s+");

                    if (addressParts.length > 0) {
                        // 构建查询条件对象
                        LocationCoordinate queryCondition = new LocationCoordinate();

                        // 根据分割后的部分设置查询条件
                        queryCondition.setProvince(addressParts[0]);
                        if (addressParts.length >= 2) {
                            queryCondition.setCity(addressParts[1]);
                        }
                        if (addressParts.length >= 3) {
                            queryCondition.setCounty(addressParts[2]);
                        }

                        // 通过省市区查询经纬度信息
                        List<LocationCoordinate> locations = locationCoordinateService.findByCondition(queryCondition);

                        if (locations != null && !locations.isEmpty()) {
                            // 取第一条匹配的记录
                            LocationCoordinate location = locations.get(0);

                            // 设置经纬度
                            if (location.getLongitude() != null && location.getLatitude() != null) {
                                birthCoordinate = String.format("经度:%s,纬度:%s",
                                    location.getLongitude(), location.getLatitude());
                            }

                            logger.debug("成功查询到出生地经纬度信息: 省={}, 市={}, 区={}, 经度={}, 纬度={}",
                                location.getProvince(), location.getCity(), location.getCounty(),
                                location.getLongitude(), location.getLatitude());
                        } else {
                            logger.warn("未找到匹配的经纬度信息: {}", birthPlace);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("获取出生地经纬度信息失败: {}", e.getMessage(), e);
                }
            }

            // 使用String.format填充，按照顺序：姓名、出生时间、出生地、出生地经纬度
            String filledRoleDesc = String.format(roleDesc, name, birthTime, birthPlace, birthCoordinate, gender);
            logger.info("已填充用户信息到提示词，用户ID: {}, 姓名: {}, 出生时间: {}, 出生地: {}, 经纬度: {},  性别: {}",
                userId, name, birthTime, birthPlace, birthCoordinate,  gender);

            return filledRoleDesc;
        } catch (Exception e) {
            logger.error("填充用户信息到提示词时发生错误: {}", e.getMessage(), e);
            return roleDesc;
        }
    }

}
