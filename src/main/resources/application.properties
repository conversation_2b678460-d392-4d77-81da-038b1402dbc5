# 通用配置（所有环境共享）
server.port=12309

# Mybatis配置
mybatis.mapper-locations=classpath*:cn/naii/iot/mapper/*.xml

# 数据库连接池通用配置
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.pool-name=DatebookHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.keepalive-time=30000
spring.datasource.hikari.register-mbeans=true

# 日志通用配置
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.io.github.imfangs.dify.client.impl.StreamEventDispatcher=ERROR

# 禁用Groovy模板位置检查
spring.groovy.template.check-template-location=false

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=2048MB
spring.servlet.multipart.max-request-size=2048MB

spring.jmx.enabled=false

# 允许的WebSocket源（跨域配置），多个源用逗号分隔
spring.websocket.allowed-origins=*

# 添加 Spring MVC Session 配置
spring.session.cookie.http-only=true
spring.session.cookie.secure=false
spring.session.cookie.same-site=lax
spring.session.cookie.path=/
server.servlet.session.timeout=60m
# 启用虚拟线程
spring.threads.virtual.enabled=true

# ========== TTS配置 ==========
# TTS超时时间(毫秒)
tts.timeout.ms=10000
# TTS最大重试次数
tts.max.retry.count=1
# TTS重试延迟(毫秒)
tts.retry.delay.ms=2000
# 每个会话最大并发TTS任务数
# 注意：阿里云TTS有并发限制，建议设置为1-2以避免限流
# - Qwen-TTS流式：建议1-2
# - CosyVoice：默认并发只有3个，建议设置为1
tts.max.concurrent.per.session=1

# 流式TTS配置
# 是否启用流式TTS(true=优先使用流式,false=强制使用非流式)
tts.streaming.enabled=true
# 流式TTS超时倍数(相对于tts.timeout.ms)
tts.streaming.timeout.multiplier=1

# ========== 阿里云TTS限流配置 ==========
# 阿里云TTS全局请求间隔(毫秒)
# 用于避免账号级别的QPS限流，所有阿里云TTS请求之间会强制等待此时间
# 建议值：500-1000ms，如果仍然被限流可以增加到1500-2000ms
aliyun.tts.request.interval.ms=1500

# ========== 音频文件清理配置 ==========
# 是否启用音频文件清理
audio.cleanup.enabled=true
# 会话结束后延迟清理时间(分钟)
audio.cleanup.session.delay.minutes=5
# 定时清理文件的年龄阈值(小时)
audio.cleanup.scheduled.hours=24
# 定时清理的cron表达式(默认每6小时执行一次)
audio.cleanup.scheduled.cron=0 0 */6 * * ?

# 音频缓存配置
# 是否启用音频缓存
audio.cleanup.cache.enabled=true
# 缓存最大数量
audio.cleanup.cache.max.size=1000
# 缓存过期时间(小时)
audio.cleanup.cache.ttl.hours=24

# ========== 保温语音配置 ==========
# 是否启用保温功能
warmup.enabled=true
# 保温延迟(毫秒) - VAD检测完成后延迟多久发送保温语音
warmup.delay.ms=300
# 保温音频最大播放时长(毫秒)
warmup.max.duration.ms=2000
# 是否允许中断保温语音
warmup.allow.interrupt=true
# 保温音频类型(ok/hmm/listening/processing,空表示随机选择)
warmup.audio.type=

# SpringDoc OpenAPI 配置
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
# 启用 Swagger UI
springdoc.swagger-ui.enabled=true
# 确保静态资源映射正确
spring.web.resources.add-mappings=true
# 配置 Swagger UI 的 URL
springdoc.swagger-ui.url=/v3/api-docs

# 激活的配置文件（默认为开发环境）
spring.profiles.active=dev

role.default.id=1