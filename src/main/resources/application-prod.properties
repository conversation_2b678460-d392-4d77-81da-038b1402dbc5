# 开发环境配置

# 数据库连接配置
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.url=***************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=naiiPassw0rd@_

# 开发环境邮箱配置
email.smtp.username=<EMAIL>
email.smtp.password=dibzrgefuvtvjghe

# 使用 Simple 缓存
spring.cache.type=simple



# 配置微信登录url
wx.url.login=https://api.weixin.qq.com/sns/jscode2session
# 微信accessToken接口
wx.url.token=https://api.weixin.qq.com/cgi-bin/token
# 查询电话号码token
wx.url.phone=https://api.weixin.qq.com/wxa/business/getuserphonenumber
# 微信 appId 和secret
wx.appId=wxb6470a55ddac312d
wx.secret=8d8ec57d0c642aacf4879ddc826edb18
