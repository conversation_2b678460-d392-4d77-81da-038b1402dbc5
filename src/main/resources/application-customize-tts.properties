# ========== 自定义WebSocket TTS服务配置 ==========

# TTS服务提供商设置为自定义
tts.provider=customize

# WebSocket TTS服务器地址
# 支持 ws:// 和 wss:// 协议
# 示例：
# - 本地开发: ws://localhost:8080
# - 生产环境: wss://tts-api.example.com
tts.websocket.url=ws://localhost:8080

# API认证密钥
# 用于WebSocket连接的Authorization头
tts.api.key=your-api-key-here

# 默认语音名称
# 支持的语音取决于TTS服务器实现
tts.default.voice=zh-CN-XiaoyiNeural

# ========== WebSocket连接配置 ==========

# WebSocket连接超时时间（毫秒）
tts.websocket.connect.timeout=10000

# TTS处理超时时间（毫秒）
tts.websocket.process.timeout=30000

# WebSocket写入超时时间（毫秒）
tts.websocket.write.timeout=10000

# 连接重试次数
tts.websocket.retry.count=3

# 重试间隔（毫秒）
tts.websocket.retry.interval=1000

# ========== 音频格式配置 ==========

# 音频输出格式
# 支持: pcm, wav, mp3
tts.audio.format=pcm

# 音频采样率（Hz）
# 常用值: 8000, 16000, 22050, 44100
tts.audio.sample.rate=16000

# 音频声道数
# 1=单声道, 2=立体声
tts.audio.channels=1

# 音频位深度
# 常用值: 8, 16, 24, 32
tts.audio.bit.depth=16

# ========== 性能优化配置 ==========

# HTTP客户端连接池大小
tts.http.connection.pool.size=10

# HTTP客户端连接保持时间（分钟）
tts.http.connection.keep.alive=5

# 音频缓冲区大小（字节）
tts.audio.buffer.size=8192

# 最大并发TTS请求数
tts.max.concurrent.requests=5

# ========== 日志配置 ==========

# 启用详细日志
logging.level.cn.naii.iot.dialogue.tts.providers.CustomizeTtsService=DEBUG

# 启用WebSocket消息日志
tts.websocket.log.messages=true

# 启用音频数据统计日志
tts.audio.log.statistics=true

# ========== 开发和测试配置 ==========

# 启用模拟模式（用于测试）
tts.mock.enabled=false

# 模拟服务器端口
tts.mock.server.port=8080

# 模拟音频块大小（字节）
tts.mock.audio.chunk.size=1600

# 模拟音频块间隔（毫秒）
tts.mock.audio.chunk.interval=50

# ========== 错误处理配置 ==========

# 启用自动重连
tts.websocket.auto.reconnect=true

# 重连最大尝试次数
tts.websocket.reconnect.max.attempts=5

# 重连指数退避基数（毫秒）
tts.websocket.reconnect.backoff.base=1000

# 重连最大延迟（毫秒）
tts.websocket.reconnect.backoff.max=30000

# ========== 监控和指标配置 ==========

# 启用TTS性能监控
tts.monitoring.enabled=true

# 性能指标收集间隔（秒）
tts.monitoring.metrics.interval=60

# 启用连接状态监控
tts.monitoring.connection.enabled=true

# 启用音频质量监控
tts.monitoring.audio.quality.enabled=false

# ========== 安全配置 ==========

# 启用SSL/TLS验证
tts.websocket.ssl.verify=true

# SSL证书路径（可选）
# tts.websocket.ssl.cert.path=/path/to/cert.pem

# 启用API密钥验证
tts.api.key.validation.enabled=true

# API密钥最小长度
tts.api.key.min.length=16

# ========== 缓存配置 ==========

# 启用音频缓存
tts.cache.enabled=true

# 缓存最大大小（MB）
tts.cache.max.size=100

# 缓存过期时间（小时）
tts.cache.expire.hours=24

# 缓存清理间隔（小时）
tts.cache.cleanup.interval=6

# ========== 高级配置 ==========

# 启用音频压缩
tts.audio.compression.enabled=false

# 压缩算法
# 支持: gzip, lz4, snappy
tts.audio.compression.algorithm=gzip

# 启用音频流水线处理
tts.audio.pipeline.enabled=true

# 流水线缓冲区大小
tts.audio.pipeline.buffer.size=10

# 启用负载均衡
tts.load.balancing.enabled=false

# 负载均衡策略
# 支持: round_robin, least_connections, random
tts.load.balancing.strategy=round_robin

# TTS服务器列表（负载均衡时使用）
# tts.servers[0].url=ws://tts1.example.com:8080
# tts.servers[0].weight=1
# tts.servers[1].url=ws://tts2.example.com:8080
# tts.servers[1].weight=2

# ========== 环境特定配置示例 ==========

# 开发环境配置
# spring.profiles.active=dev
# tts.websocket.url=ws://localhost:8080
# tts.api.key=dev-api-key
# tts.websocket.log.messages=true

# 测试环境配置
# spring.profiles.active=test
# tts.mock.enabled=true
# tts.websocket.url=ws://test-tts.example.com:8080
# tts.api.key=test-api-key

# 生产环境配置
# spring.profiles.active=prod
# tts.websocket.url=wss://tts-api.example.com:443
# tts.api.key=${TTS_API_KEY}
# tts.websocket.ssl.verify=true
# tts.monitoring.enabled=true
