<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- MyBatis3Simple风格 -->
    <context id="simple" targetRuntime="MyBatis3Simple">

        <property name="javaFileEncoding" value="UTF-8"/>
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <commentGenerator>
            <!-- 是否去除自动生成的注释 true：是 ： false:否。 自动生成注释太啰嗦，可以编码扩展CommentGenerator -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***********************************************************************************************************************************************************************"
                        userId="root" password="Passw0rd@_">
        </jdbcConnection>

        <!-- 生成PO的包名和位置 -->
        <javaModelGenerator targetPackage="cn.naii.iot.entity" targetProject="src/main/java"/>

        <!-- 生成XML映射文件的包名和位置 -->
        <sqlMapGenerator targetPackage="cn.naii.iot.mapper" targetProject="src/main/java"/>

        <!-- 生成Mapper接口的包名和位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="cn.naii.iot.dao" targetProject="src/main/java"/>

        <!-- 要生成对应表配置 -->
        <table tableName="sys_voice_print" domainObjectName="SysVoicePrint" ></table>
    </context>
</generatorConfiguration>
