package cn.naii.iot.dialogue.tts;

import cn.naii.iot.dialogue.tts.providers.CustomizeTtsService;
import cn.naii.iot.entity.SysConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 自定义TTS客户端测试类
 * 演示如何使用CustomizeTtsService进行WebSocket流式TTS
 * 
 * 本测试展示了：
 * 1. 如何配置自定义TTS服务
 * 2. 如何使用流式TTS接口
 * 3. 如何处理音频流数据
 * 4. 错误处理和连接管理
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class CustomizeTtsClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomizeTtsClientTest.class);
    
    /**
     * 测试流式TTS功能
     * 注意：此测试需要实际的WebSocket TTS服务器才能运行
     * 启动服务器：python tts_stream_server.py --port 60009
     */
    @Test
    public void testStreamTextToSpeech() throws Exception {
        // 配置TTS服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://localhost:60009"); // 匹配Python版本的默认端口
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建自定义TTS服务实例
        CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);

        // 设置提示音频
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        try {
            ttsService.setPrompt(promptText, null); // 使用空音频文件
            logger.info("✓ 提示音频设置成功");
        } catch (Exception e) {
            logger.warn("提示音频设置失败: {}", e.getMessage());
            throw e;
        }

        // 测试文本
        String testText = "你好，这是一个WebSocket流式TTS测试。";
        logger.info("🎬 开始测试流式TTS - 文本: {}", testText);
        
        // 用于统计和同步的变量
        AtomicInteger chunkCount = new AtomicInteger(0);
        AtomicInteger totalBytes = new AtomicInteger(0);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);
        
        // 创建流式回调
        StreamTtsCallback callback = new StreamTtsCallback() {
            @Override
            public void onStart() {
                logger.info("🎬 TTS流式处理开始");
            }
            
            @Override
            public void onAudioChunk(byte[] audioChunk) throws Exception {
                int chunkIndex = chunkCount.incrementAndGet();
                int chunkSize = audioChunk.length;
                totalBytes.addAndGet(chunkSize);

                logger.info("📦 接收音频块 #{} - 大小: {} bytes, 累计: {} bytes",
                    chunkIndex, chunkSize, totalBytes.get());
            }
            
            @Override
            public void onComplete() {
                logger.info("✅ TTS流式处理完成 - 总块数: {}, 总字节数: {}", 
                    chunkCount.get(), totalBytes.get());
                completionLatch.countDown();
            }
            
            @Override
            public void onError(Throwable error) {
                logger.error("❌ TTS流式处理错误", error);
                errorRef.set(new Exception("TTS处理失败", error));
                completionLatch.countDown();
            }
        };
        
        try {
            // 执行流式TTS
            ttsService.streamTextToSpeech(testText, callback);
            
            // 等待处理完成（最多等待30秒）
            boolean completed = completionLatch.await(30, TimeUnit.SECONDS);
            
            if (!completed) {
                logger.error("❌ TTS处理超时");
                throw new Exception("TTS处理超时");
            }
            
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }
            
            logger.info("🎉 流式TTS测试成功完成！");
            
        } catch (Exception e) {
            logger.error("❌ 流式TTS测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试非流式TTS功能
     */
    @Test
    public void testTextToSpeech() throws Exception {
        // 配置TTS服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://localhost:60009");
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建自定义TTS服务实例
        CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);

        // 设置提示音频
        ttsService.setPrompt("好汉，你又是何人哪？关羽，字云长。", null);

        // 测试文本
        String testText = "这是一个非流式TTS测试。";

        logger.info("🎬 开始测试非流式TTS - 文本: {}", testText);

        try {
            // 执行非流式TTS
            String audioFilePath = ttsService.textToSpeech(testText);

            logger.info("✅ 非流式TTS测试成功 - 音频文件: {}", audioFilePath);

        } catch (Exception e) {
            logger.error("❌ 非流式TTS测试失败", e);
            throw e;
        }
    }
    

}
