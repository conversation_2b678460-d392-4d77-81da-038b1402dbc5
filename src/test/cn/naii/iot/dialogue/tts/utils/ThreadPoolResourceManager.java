package cn.naii.iot.dialogue.tts.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;

/**
 * 线程池资源管理器
 * 用于管理多线程测试中的资源分配和回收
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class ThreadPoolResourceManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolResourceManager.class);
    
    private final ExecutorService executorService;
    private final int threadCount;
    private final String managerName;
    private final AtomicInteger activeThreads = new AtomicInteger(0);
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    private final List<Future<?>> futures = new ArrayList<>();
    
    /**
     * 构造函数
     * 
     * @param threadCount 线程数量
     * @param managerName 管理器名称
     */
    public ThreadPoolResourceManager(int threadCount, String managerName) {
        this.threadCount = threadCount;
        this.managerName = managerName;
        this.executorService = Executors.newFixedThreadPool(threadCount, new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, managerName + "-Thread-" + threadNumber.getAndIncrement());
                thread.setDaemon(false); // 确保主线程等待
                return thread;
            }
        });
        
        logger.info("🚀 线程池资源管理器 [{}] 初始化完成 - 线程数: {}", managerName, threadCount);
    }
    
    /**
     * 提交任务
     * 
     * @param task 要执行的任务
     * @return Future 对象
     */
    public <T> Future<T> submit(Callable<T> task) {
        Future<T> future = executorService.submit(() -> {
            long startTime = System.currentTimeMillis();
            activeThreads.incrementAndGet();
            
            try {
                logger.debug("🔄 [{}] 线程开始执行任务", Thread.currentThread().getName());
                T result = task.call();
                logger.debug("✅ [{}] 线程任务执行成功", Thread.currentThread().getName());
                return result;
                
            } catch (Exception e) {
                logger.error("❌ [{}] 线程任务执行失败", Thread.currentThread().getName(), e);
                throw new RuntimeException(e);
                
            } finally {
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;
                totalExecutionTime.addAndGet(executionTime);
                activeThreads.decrementAndGet();
                
                logger.debug("🏁 [{}] 线程任务完成，耗时: {} ms", Thread.currentThread().getName(), executionTime);
            }
        });
        
        synchronized (futures) {
            futures.add(future);
        }
        
        return future;
    }
    
    /**
     * 提交 Runnable 任务
     * 
     * @param task 要执行的任务
     * @return Future 对象
     */
    public Future<?> submit(Runnable task) {
        return submit(() -> {
            task.run();
            return null;
        });
    }
    
    /**
     * 等待所有任务完成
     * 
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 执行结果统计
     */
    public ExecutionResult waitForCompletion(long timeout, TimeUnit unit) {
        logger.info("⏳ [{}] 等待所有任务完成，超时时间: {} {}", managerName, timeout, unit.name());
        
        List<Future<?>> currentFutures;
        synchronized (futures) {
            currentFutures = new ArrayList<>(futures);
        }
        
        int successCount = 0;
        int errorCount = 0;
        int timeoutCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < currentFutures.size(); i++) {
            Future<?> future = currentFutures.get(i);
            
            try {
                future.get(timeout, unit);
                successCount++;
                logger.debug("✅ [{}] 任务 {} 执行成功", managerName, i + 1);
                
            } catch (TimeoutException e) {
                timeoutCount++;
                String errorMsg = String.format("任务 %d 执行超时", i + 1);
                errorMessages.add(errorMsg);
                logger.warn("⏰ [{}] {}", managerName, errorMsg);
                future.cancel(true);
                
            } catch (ExecutionException e) {
                errorCount++;
                String errorMsg = String.format("任务 %d 执行异常: %s", i + 1, e.getCause().getMessage());
                errorMessages.add(errorMsg);
                logger.error("❌ [{}] {}", managerName, errorMsg, e.getCause());
                
            } catch (InterruptedException e) {
                errorCount++;
                String errorMsg = String.format("任务 %d 被中断", i + 1);
                errorMessages.add(errorMsg);
                logger.warn("🚫 [{}] {}", managerName, errorMsg);
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        long endTime = System.currentTimeMillis();
        long totalWaitTime = endTime - startTime;
        
        return new ExecutionResult(
            currentFutures.size(),
            successCount,
            errorCount,
            timeoutCount,
            totalWaitTime,
            totalExecutionTime.get(),
            errorMessages
        );
    }
    
    /**
     * 关闭线程池并回收资源
     * 
     * @param forceShutdownTimeout 强制关闭超时时间（秒）
     */
    public void shutdown(int forceShutdownTimeout) {
        logger.info("🧹 [{}] 开始关闭线程池...", managerName);
        
        // 停止接受新任务
        executorService.shutdown();
        
        try {
            // 等待现有任务完成
            if (!executorService.awaitTermination(forceShutdownTimeout, TimeUnit.SECONDS)) {
                logger.warn("⚠️ [{}] 线程池未能在 {} 秒内正常关闭，开始强制关闭", managerName, forceShutdownTimeout);
                
                // 取消所有未完成的任务
                synchronized (futures) {
                    for (Future<?> future : futures) {
                        if (!future.isDone()) {
                            future.cancel(true);
                        }
                    }
                }
                
                // 强制关闭
                executorService.shutdownNow();
                
                // 再等待一段时间
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    logger.error("❌ [{}] 线程池强制关闭失败", managerName);
                } else {
                    logger.info("✅ [{}] 线程池强制关闭成功", managerName);
                }
            } else {
                logger.info("✅ [{}] 线程池正常关闭成功", managerName);
            }
            
        } catch (InterruptedException e) {
            logger.error("❌ [{}] 等待线程池关闭时被中断", managerName, e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 清理资源
        synchronized (futures) {
            futures.clear();
        }
        
        logger.info("🎉 [{}] 线程池资源回收完成", managerName);
    }
    
    /**
     * 获取当前活跃线程数
     */
    public int getActiveThreadCount() {
        return activeThreads.get();
    }
    
    /**
     * 获取总执行时间
     */
    public long getTotalExecutionTime() {
        return totalExecutionTime.get();
    }
    
    /**
     * 获取线程池状态
     */
    public String getStatus() {
        return String.format("[%s] 线程数: %d, 活跃线程: %d, 已提交任务: %d, 总执行时间: %d ms",
            managerName, threadCount, activeThreads.get(), futures.size(), totalExecutionTime.get());
    }
    
    /**
     * 执行结果统计
     */
    public static class ExecutionResult {
        private final int totalTasks;
        private final int successCount;
        private final int errorCount;
        private final int timeoutCount;
        private final long totalWaitTime;
        private final long totalExecutionTime;
        private final List<String> errorMessages;
        
        public ExecutionResult(int totalTasks, int successCount, int errorCount, int timeoutCount,
                             long totalWaitTime, long totalExecutionTime, List<String> errorMessages) {
            this.totalTasks = totalTasks;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.timeoutCount = timeoutCount;
            this.totalWaitTime = totalWaitTime;
            this.totalExecutionTime = totalExecutionTime;
            this.errorMessages = new ArrayList<>(errorMessages);
        }
        
        public int getTotalTasks() { return totalTasks; }
        public int getSuccessCount() { return successCount; }
        public int getErrorCount() { return errorCount; }
        public int getTimeoutCount() { return timeoutCount; }
        public long getTotalWaitTime() { return totalWaitTime; }
        public long getTotalExecutionTime() { return totalExecutionTime; }
        public List<String> getErrorMessages() { return new ArrayList<>(errorMessages); }
        
        public double getSuccessRate() {
            return totalTasks > 0 ? (double) successCount / totalTasks * 100 : 0;
        }
        
        public long getAverageExecutionTime() {
            return totalTasks > 0 ? totalExecutionTime / totalTasks : 0;
        }
        
        public boolean isAllSuccessful() {
            return successCount == totalTasks;
        }
        
        @Override
        public String toString() {
            return String.format(
                "ExecutionResult{总任务: %d, 成功: %d, 失败: %d, 超时: %d, 成功率: %.1f%%, 平均耗时: %d ms, 总等待时间: %d ms}",
                totalTasks, successCount, errorCount, timeoutCount, getSuccessRate(), getAverageExecutionTime(), totalWaitTime
            );
        }
    }
}
