package cn.naii.iot.dialogue.tts;

import cn.naii.iot.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 模拟WebSocket TTS服务器
 * 基于 tts_stream_server.py 的协议实现
 *
 * 本服务器模拟了Python版本的协议流程：
 * 1. 接收配置信息（prompt_text和prompt_wav_base64）
 * 2. 接收文本流进行TTS转换
 * 3. 发送音频流数据（PCM格式，22050Hz）
 * 4. 发送控制信号（__STREAM_COMPLETE__, __ERROR__等）
 * 5. 处理结束信号（__END_STREAM__）
 *
 * 端点路径：/ws/tts_stream（匹配Python版本）
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class MockWebSocketTtsServer extends WebSocketServer {
    
    private static final Logger logger = LoggerFactory.getLogger(MockWebSocketTtsServer.class);

    // 控制信号（匹配Python版本）
    private static final String SIGNAL_STREAM_COMPLETE = "__STREAM_COMPLETE__";
    private static final String SIGNAL_CONNECTION_CLOSING = "__CONNECTION_CLOSING__";
    private static final String SIGNAL_END_STREAM = "__END_STREAM__";
    private static final String SIGNAL_ERROR_PREFIX = "__ERROR__";

    // 音频格式配置（匹配Python版本）
    private static final int SAMPLE_RATE = 22050; // 22050Hz
    private static final int CHANNELS = 1;
    private static final int CHUNK_SIZE = 2205; // 22050Hz * 1声道 * 2字节 * 0.05秒 = 2205字节（50ms音频）

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(4);

    // 连接状态管理
    private int activeConnections = 0;
    private int totalRequests = 0;

    public MockWebSocketTtsServer(int port) {
        super(new InetSocketAddress(port));
        logger.info("🚀 模拟WebSocket TTS服务器初始化 - 端口: {}", port);
        logger.info("📍 WebSocket端点: ws://localhost:{}/ws/tts_stream", port);
    }
    
    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        String connectionId = "conn_" + (++totalRequests);
        activeConnections++;

        String clientAddress = conn.getRemoteSocketAddress().toString();
        logger.info("[{}] 🔗 客户端连接: {} - 活跃连接数: {}", connectionId, clientAddress, activeConnections);

        // 存储连接ID到连接对象的映射
        conn.setAttachment(connectionId);
    }
    
    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        String connectionId = (String) conn.getAttachment();
        activeConnections--;

        String clientAddress = conn.getRemoteSocketAddress().toString();
        logger.info("[{}] ❌ 客户端断开: {} - 代码: {}, 原因: {}, 活跃连接数: {}",
            connectionId, clientAddress, code, reason, activeConnections);
    }
    
    @Override
    public void onMessage(WebSocket conn, String message) {
        String connectionId = (String) conn.getAttachment();
        logger.info("[{}] 📨 收到消息: {}", connectionId,
            message.length() > 100 ? message.substring(0, 100) + "..." : message);

        try {
            // 检查是否为结束信号
            if (SIGNAL_END_STREAM.equals(message)) {
                logger.info("[{}] 收到结束信号，准备关闭连接", connectionId);
                conn.send(SIGNAL_CONNECTION_CLOSING);
                scheduler.schedule(() -> {
                    try {
                        conn.close();
                    } catch (Exception e) {
                        logger.warn("关闭连接失败", e);
                    }
                }, 100, TimeUnit.MILLISECONDS);
                return;
            }

            // 尝试解析为配置信息
            try {
                TtsConfig config = JsonUtil.fromJson(message, TtsConfig.class);
                if (config.promptText != null || config.promptWavBase64 != null) {
                    logger.info("[{}] 收到配置信息 - 提示文本长度: {}, 音频长度: {}",
                        connectionId,
                        config.promptText != null ? config.promptText.length() : 0,
                        config.promptWavBase64 != null ? config.promptWavBase64.length() : 0);

                    // 存储配置信息到连接属性中
                    conn.setAttachment(connectionId + "|" + JsonUtil.toJson(config));
                    return;
                }
            } catch (Exception e) {
                // 不是配置信息，继续处理为文本
            }

            // 处理为TTS文本请求
            processTtsTextRequest(conn, message);

        } catch (Exception e) {
            logger.error("[{}] 处理消息失败", connectionId, e);
            sendErrorMessage(conn, "消息处理错误: " + e.getMessage());
        }
    }
    
    @Override
    public void onError(WebSocket conn, Exception ex) {
        String clientAddress = conn != null ? conn.getRemoteSocketAddress().toString() : "unknown";
        logger.error("WebSocket错误 - 客户端: {}", clientAddress, ex);
    }
    
    @Override
    public void onStart() {
        logger.info("✅ 模拟WebSocket TTS服务器启动成功");
        logger.info("📍 服务地址: ws://localhost:{}/", getPort());
        logger.info("🎯 TTS端点: ws://localhost:{}/ws/tts_stream", getPort());
        logger.info("🔧 音频格式: PCM, {}Hz, {}声道, {}位", SAMPLE_RATE, CHANNELS, 16);
        logger.info("📊 健康检查: http://localhost:{}/health", getPort());
    }
    
    /**
     * 处理TTS文本请求（匹配Python版本的协议）
     */
    private void processTtsTextRequest(WebSocket conn, String text) {
        String connectionId = getConnectionId(conn);
        String textPreview = text.length() > 50 ? text.substring(0, 50) + "..." : text;
        logger.info("[{}] 🎬 开始处理TTS文本请求 - 文本: {}", connectionId, textPreview);

        if (text == null || text.trim().isEmpty()) {
            sendErrorMessage(conn, "文本内容不能为空");
            return;
        }

        // 模拟TTS处理过程
        scheduler.schedule(() -> {
            try {
                // 模拟生成音频数据并流式发送
                generateAndSendAudioStream(conn, text);

            } catch (Exception e) {
                logger.error("[{}] TTS处理失败", connectionId, e);
                sendErrorMessage(conn, "TTS处理失败: " + e.getMessage());
            }
        }, 100, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取连接ID
     */
    private String getConnectionId(WebSocket conn) {
        String attachment = (String) conn.getAttachment();
        if (attachment != null && attachment.contains("|")) {
            return attachment.split("\\|")[0];
        }
        return attachment != null ? attachment : "unknown";
    }
    
    /**
     * 生成并发送音频流（匹配Python版本的协议）
     */
    private void generateAndSendAudioStream(WebSocket conn, String text) {
        String connectionId = getConnectionId(conn);
        int textLength = text.length();

        // 根据文本长度估算音频块数量（每个字符大约对应100ms音频）
        int estimatedChunks = Math.max(5, textLength / 2);

        logger.info("[{}] 📦 开始发送音频流 - 预计块数: {}, 每块大小: {} bytes",
            connectionId, estimatedChunks, CHUNK_SIZE);

        // 分批发送音频数据
        for (int i = 0; i < estimatedChunks; i++) {
            final int chunkIndex = i;
            final boolean isLastChunk = (i == estimatedChunks - 1);

            scheduler.schedule(() -> {
                try {
                    if (conn.isOpen()) {
                        // 生成模拟PCM音频数据（22050Hz）
                        byte[] audioChunk = generateMockPcmAudio(CHUNK_SIZE, chunkIndex);

                        // 发送音频数据
                        conn.send(audioChunk);

                        logger.debug("[{}] 📤 发送音频块 #{}/{} - 大小: {} bytes",
                            connectionId, chunkIndex + 1, estimatedChunks, audioChunk.length);

                        // 如果是最后一块，发送完成状态
                        if (isLastChunk) {
                            scheduler.schedule(() -> {
                                conn.send(SIGNAL_STREAM_COMPLETE);
                                logger.info("[{}] ✅ 音频流发送完成 - 总块数: {}", connectionId, estimatedChunks);
                            }, 50, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    logger.error("[{}] 发送音频块失败", connectionId, e);
                    sendErrorMessage(conn, "发送音频失败: " + e.getMessage());
                }
            }, i * 50, TimeUnit.MILLISECONDS); // 每50ms发送一块
        }
    }
    
    /**
     * 生成模拟PCM音频数据（匹配Python版本的22050Hz）
     */
    private byte[] generateMockPcmAudio(int size, int chunkIndex) {
        byte[] audioData = new byte[size];
        Random random = new Random(chunkIndex); // 使用块索引作为种子，确保可重现

        // 生成模拟的PCM音频数据（16位，单声道，22050Hz）
        for (int i = 0; i < size; i += 2) {
            // 生成简单的正弦波音频
            double frequency = 440.0; // A4音符频率
            double amplitude = 0.3; // 音量
            double sampleRate = SAMPLE_RATE; // 22050Hz
            double time = (chunkIndex * size / 2 + i / 2) / sampleRate;

            short sample = (short) (amplitude * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));

            // 小端序存储
            audioData[i] = (byte) (sample & 0xFF);
            if (i + 1 < size) {
                audioData[i + 1] = (byte) ((sample >> 8) & 0xFF);
            }
        }

        return audioData;
    }
    
    /**
     * 发送错误消息（匹配Python版本的格式）
     */
    private void sendErrorMessage(WebSocket conn, String errorMessage) {
        try {
            String errorMsg = SIGNAL_ERROR_PREFIX + ":" + errorMessage;
            conn.send(errorMsg);

            String connectionId = getConnectionId(conn);
            logger.debug("[{}] 📤 发送错误消息: {}", connectionId, errorMsg);

        } catch (Exception e) {
            logger.error("发送错误消息失败", e);
        }
    }
    
    /**
     * TTS配置数据类（匹配Python版本的JSON格式）
     */
    private static class TtsConfig {
        @JsonProperty("prompt_text")
        public String promptText;

        @JsonProperty("prompt_wav_base64")
        public String promptWavBase64;
    }
    
    /**
     * 启动模拟服务器
     */
    public static void main(String[] args) {
        int port = 8080;
        
        MockWebSocketTtsServer server = new MockWebSocketTtsServer(port);
        server.start();
        
        logger.info("🎯 模拟WebSocket TTS服务器已启动");
        logger.info("📍 测试地址: ws://localhost:{}/tts/stream", port);
        logger.info("💡 使用Ctrl+C停止服务器");
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("🛑 正在关闭服务器...");
            try {
                server.stop();
                server.scheduler.shutdown();
                logger.info("✅ 服务器已关闭");
            } catch (Exception e) {
                logger.error("关闭服务器失败", e);
            }
        }));
    }
}
