package cn.naii.iot.dialogue.tts;

import cn.naii.iot.dialogue.tts.providers.WebSocketStreamTtsService;
import cn.naii.iot.entity.SysConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * WebSocket 流式 TTS 服务测试类
 * 基于 test_tts_client.py 和 tts_stream_server.py 的协议实现
 * 
 * 测试前需要启动 Python TTS 服务器：
 * python tts_stream_server.py --port 60009 --model_dir /path/to/model
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class WebSocketStreamTtsTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketStreamTtsTest.class);
    
    /**
     * 测试流式 TTS 功能
     * 注意：此测试需要实际的 WebSocket TTS 服务器才能运行
     */
    @Test
    public void testWebSocketStreamTts() throws Exception {
        logger.info("🧪 开始 WebSocket 流式 TTS 测试");
        
        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("websocket_stream");
        config.setApiUrl("ws://localhost:60009"); // 匹配 Python 版本的默认端口
        config.setApiKey("demo-api-key");
        
        String voiceName = "关羽";
        String outputPath = "audio/test/";
        
        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);
        
        // 设置提示音频（匹配 Python 版本的协议要求）
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        String promptWavPath = "prompt.wav"; // 可选的音频文件路径
        
        try {
            // 设置提示音频（如果文件不存在会使用空的 Base64）
            ttsService.setPrompt(promptText, promptWavPath);
            logger.info("✓ 提示音频设置成功");
        } catch (Exception e) {
            logger.warn("提示音频文件不存在，使用默认配置: {}", e.getMessage());
            // 设置空的提示音频
            ttsService.setPrompt(promptText, null);
        }
        
        // 测试文本
        String testText = "你好，这是一个基于 WebSocket 的流式 TTS 测试。我们正在验证与 Python 服务器的协议兼容性。";
        logger.info("🎬 开始测试流式 TTS - 文本: {}", testText);
        
        // 用于统计和同步的变量
        AtomicInteger chunkCount = new AtomicInteger(0);
        AtomicInteger totalBytes = new AtomicInteger(0);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);
        
        // 创建流式回调
        StreamTtsCallback callback = new StreamTtsCallback() {
            @Override
            public void onStart() {
                logger.info("🎬 TTS 流式处理开始");
            }
            
            @Override
            public void onAudioChunk(byte[] audioChunk) throws Exception {
                int chunkIndex = chunkCount.incrementAndGet();
                int chunkSize = audioChunk.length;
                totalBytes.addAndGet(chunkSize);
                
                logger.info("📦 接收音频块 #{} - 大小: {} bytes, 累计: {} bytes", 
                    chunkIndex, chunkSize, totalBytes.get());
                
                // 这里可以添加音频处理逻辑
                // 例如：实时播放、发送到客户端、保存到缓冲区等
            }
            
            @Override
            public void onComplete() {
                logger.info("✅ TTS 流式处理完成 - 总块数: {}, 总字节数: {}", 
                    chunkCount.get(), totalBytes.get());
                completionLatch.countDown();
            }
            
            @Override
            public void onError(Throwable error) {
                logger.error("❌ TTS 流式处理错误", error);
                errorRef.set(new Exception("TTS 处理失败", error));
                completionLatch.countDown();
            }
        };
        
        try {
            // 执行流式 TTS
            ttsService.streamTextToSpeech(testText, callback);
            
            // 等待处理完成（最多等待 60 秒）
            boolean completed = completionLatch.await(60, TimeUnit.SECONDS);
            
            if (!completed) {
                logger.error("❌ TTS 处理超时");
                throw new Exception("TTS 处理超时");
            }
            
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }
            
            logger.info("🎉 WebSocket 流式 TTS 测试成功完成！");
            
        } catch (Exception e) {
            logger.error("❌ WebSocket 流式 TTS 测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试非流式 TTS 功能
     */
    @Test
    public void testWebSocketNonStreamTts() throws Exception {
        logger.info("🧪 开始 WebSocket 非流式 TTS 测试");
        
        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("websocket_stream");
        config.setApiUrl("ws://localhost:60009");
        config.setApiKey("demo-api-key");
        
        String voiceName = "关羽";
        String outputPath = "audio/test/";
        
        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);
        
        // 设置提示音频
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        ttsService.setPrompt(promptText, null);
        
        // 测试文本
        String testText = "这是一个非流式 TTS 测试，内部使用 WebSocket 流式实现。";
        
        logger.info("🎬 开始测试非流式 TTS - 文本: {}", testText);
        
        try {
            // 执行非流式 TTS
            String audioFilePath = ttsService.textToSpeech(testText);
            
            logger.info("✅ 非流式 TTS 测试成功 - 音频文件: {}", audioFilePath);
            
        } catch (Exception e) {
            logger.error("❌ 非流式 TTS 测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试批量文本处理
     */
    @Test
    public void testBatchTextProcessing() throws Exception {
        logger.info("🧪 开始批量文本处理测试");
        
        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("websocket_stream");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream");
        config.setApiKey("demo-api-key");
        
        String voiceName = "关羽";
        String outputPath = "audio/test/";
        
        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);
        
        // 设置提示音频
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        ttsService.setPrompt(promptText, null);
        
        // 测试文本列表
        String[] texts = {
            "第一段测试文本。",
            "第二段测试文本，稍微长一些。",
            "第三段测试文本，用于验证批量处理功能。"
        };
        
        logger.info("🎬 开始批量文本处理 - 共 {} 段文本", texts.length);
        
        try {
            for (int i = 0; i < texts.length; i++) {
                String text = texts[i];
                logger.info("处理第 {}/{} 段文本: {}", i + 1, texts.length, text);
                
                // 执行 TTS
                String audioFilePath = ttsService.textToSpeech(text);
                logger.info("✓ 第 {} 段音频文件已保存: {}", i + 1, audioFilePath);
                
                // 避免服务器过载
                Thread.sleep(1000);
            }
            
            logger.info("✅ 批量文本处理测试成功完成！");
            
        } catch (Exception e) {
            logger.error("❌ 批量文本处理测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试基本配置功能
     */
    @Test
    public void testBasicConfiguration() {
        logger.info("🧪 开始基本配置测试");
        
        // 测试配置创建
        SysConfig config = new SysConfig();
        config.setProvider("websocket_stream");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream");
        config.setApiKey("demo-api-key");
        
        String voiceName = "关羽";
        String outputPath = "audio/test/";
        
        // 测试服务创建
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

        // 测试提示设置
        try {
            ttsService.setPrompt("好汉，你又是何人哪？", null);
            logger.info("✅ 基本配置测试成功");
        } catch (Exception e) {
            logger.error("❌ 基本配置测试失败", e);
            throw new RuntimeException("配置测试失败", e);
        }
    }
}
