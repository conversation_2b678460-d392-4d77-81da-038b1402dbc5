package cn.naii.iot.dialogue.tts;

import cn.naii.iot.dialogue.tts.providers.WebSocketStreamTtsService;
import cn.naii.iot.dialogue.tts.utils.ThreadPoolResourceManager;
import cn.naii.iot.entity.SysConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * WebSocket 流式 TTS 服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class WebSocketStreamTtsTest {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketStreamTtsTest.class);

    /**
     * 创建测试配置
     */
    private SysConfig createTestConfig() {
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream");
        config.setApiKey("demo-api-key");
        return config;
    }


    /**
     * 测试流式 TTS 功能
     * 注意：此测试需要实际的 WebSocket TTS 服务器才能运行
     */
//    @Test
    public void testWebSocketStreamTts() throws Exception {
        logger.info("🧪 开始 WebSocket 流式 TTS 测试");

        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream"); // 匹配 Python 版本的默认端口
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

        // 设置提示音频（匹配 Python 版本的协议要求）
        // String promptText = "好汉，你又是何人哪？关羽，字云长。";
        String promptWavPath = "audio/guanyu.WAV"; // 可选的音频文件路径

        try {
            // 设置提示音频（如果文件不存在会使用空的 Base64）
            ttsService.setPrompt("", "", "guanyu");
            logger.info("✓ 提示音频设置成功");
        } catch (Exception e) {
            logger.warn("提示音频文件不存在，使用默认配置: {}", e.getMessage());
            // 设置空的提示音频
            ttsService.setPrompt(null, null, null);
        }

        // 测试文本
        String testText = "你好，这是一个基于 WebSocket 的流式 TTS 测试。我们正在验证与 Python 服务器的协议兼容性。";


        logger.info("🎬 开始测试流式 TTS - 文本: {}", testText);

        // 用于统计和同步的变量
        AtomicInteger chunkCount = new AtomicInteger(0);
        AtomicInteger totalBytes = new AtomicInteger(0);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);

        // 创建流式回调
        StreamTtsCallback callback = new StreamTtsCallback() {
            @Override
            public void onStart() {
                logger.info("🎬 TTS 流式处理开始");
            }

            @Override
            public void onAudioChunk(byte[] audioChunk) throws Exception {
                int chunkIndex = chunkCount.incrementAndGet();
                int chunkSize = audioChunk.length;
                totalBytes.addAndGet(chunkSize);

                logger.info("📦 接收音频块 #{} - 大小: {} bytes, 累计: {} bytes",
                        chunkIndex, chunkSize, totalBytes.get());

                // 这里可以添加音频处理逻辑 todo
                // 例如：实时播放、发送到客户端、保存到缓冲区等
                // 示例：保存音频块到文件
            }

            @Override
            public void onComplete() {
                logger.info("✅ TTS 流式处理完成 - 总块数: {}, 总字节数: {}",
                        chunkCount.get(), totalBytes.get());
                completionLatch.countDown();
            }

            @Override
            public void onError(Throwable error) {
                logger.error("❌ TTS 流式处理错误", error);
                errorRef.set(new Exception("TTS 处理失败", error));
                completionLatch.countDown();
            }
        };
        try {
            // 执行流式 TTS
            ttsService.streamTextToSpeech(testText, callback);

            // 等待处理完成（最多等待 60 秒）
            boolean completed = completionLatch.await(60, TimeUnit.SECONDS);

            if (!completed) {
                logger.error("❌ TTS 处理超时");
                throw new Exception("TTS 处理超时");
            }

            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }

            logger.info("🎉 WebSocket 流式 TTS 测试成功完成！");

        } catch (Exception e) {
            logger.error("❌ WebSocket 流式 TTS 测试失败", e);
            throw e;
        }
    }


    /**
     * 多线程批量文本处理测试
     * 使用10个线程并发执行，确保资源正确回收
     */
//    @Test
    public void testMultiThreadBatchTextProcessing() throws Exception {
        logger.info("🧪 开始多线程批量文本处理测试 - 线程数: 10");

        final int THREAD_COUNT = 10;
        final int TEXTS_PER_THREAD = 3;

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_COUNT);

        // 用于收集所有任务的 Future
        List<Future<String>> futures = new ArrayList<>();

        // 统计信息
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalProcessingTime = new AtomicLong(0);

        try {
            // 提交任务到线程池
            for (int threadId = 0; threadId < THREAD_COUNT; threadId++) {
                final int currentThreadId = threadId;

                Future<String> future = executorService.submit(() -> {
                    String threadName = "Thread-" + currentThreadId;
                    long startTime = System.currentTimeMillis();

                    try {
                        logger.info("🚀 {} 开始执行", threadName);

                        // 为每个线程创建独立的 TTS 服务实例
                        SysConfig config = createTestConfig();
                        String voiceName = "关羽_" + currentThreadId; // 区分不同线程的语音
                        String outputPath = "audio/test/thread_" + currentThreadId + "/";

                        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

                        // 设置提示音频
                        String promptText = "好汉，你又是何人哪？关羽，字云长。";
                        ttsService.setPrompt("", "","guanyu");

                        // 每个线程处理的文本
                        String[] texts = {
                            String.format("[%s] 第一段测试文本。", threadName),
                            String.format("[%s] 第二段测试文本，稍微长一些。", threadName),
                            String.format("[%s] 第三段测试文本，用于验证多线程处理功能。", threadName)
                        };

                        // 处理文本
                        for (int i = 0; i < texts.length; i++) {
                            String text = texts[i];
                            logger.info("📝 {} 处理第 {}/{} 段文本", threadName, i + 1, texts.length);

                            try {
                                String audioFilePath = ttsService.textToSpeech(text);
                                logger.info("✅ {} 第 {} 段音频文件已保存: {}", threadName, i + 1, audioFilePath);
                                successCount.incrementAndGet();

                                // 避免服务器过载
                                Thread.sleep(500);

                            } catch (Exception e) {
                                logger.error("❌ {} 第 {} 段文本处理失败", threadName, i + 1, e);
                                errorCount.incrementAndGet();
                            }
                        }

                        long endTime = System.currentTimeMillis();
                        long processingTime = endTime - startTime;
                        totalProcessingTime.addAndGet(processingTime);

                        logger.info("🎉 {} 完成，耗时: {} ms", threadName, processingTime);
                        return String.format("%s 成功完成，耗时: %d ms", threadName, processingTime);

                    } catch (Exception e) {
                        logger.error("❌ {} 执行失败", threadName, e);
                        errorCount.addAndGet(TEXTS_PER_THREAD); // 整个线程失败，计入所有文本的错误
                        throw new RuntimeException(threadName + " 执行失败", e);
                    }
                });

                futures.add(future);
            }

            // 等待所有任务完成
            logger.info("⏳ 等待所有线程完成...");
            List<String> results = new ArrayList<>();

            for (Future<String> future : futures) {
                try {
                    // 设置超时时间，避免无限等待
                    String result = future.get(120, TimeUnit.SECONDS);
                    results.add(result);
                } catch (TimeoutException e) {
                    logger.error("❌ 线程执行超时", e);
                    future.cancel(true);
                    errorCount.addAndGet(TEXTS_PER_THREAD);
                } catch (ExecutionException e) {
                    logger.error("❌ 线程执行异常", e.getCause());
                    errorCount.addAndGet(TEXTS_PER_THREAD);
                } catch (InterruptedException e) {
                    logger.error("❌ 线程被中断", e);
                    Thread.currentThread().interrupt();
                    errorCount.addAndGet(TEXTS_PER_THREAD);
                }
            }

            // 输出统计信息
            int totalTexts = THREAD_COUNT * TEXTS_PER_THREAD;
            long avgProcessingTime = totalProcessingTime.get() / THREAD_COUNT;

            logger.info("📊 多线程测试统计:");
            logger.info("   - 总线程数: {}", THREAD_COUNT);
            logger.info("   - 每线程文本数: {}", TEXTS_PER_THREAD);
            logger.info("   - 总文本数: {}", totalTexts);
            logger.info("   - 成功处理: {}", successCount.get());
            logger.info("   - 失败处理: {}", errorCount.get());
            logger.info("   - 成功率: {:.1f}%", (double) successCount.get() / totalTexts * 100);
            logger.info("   - 平均线程耗时: {} ms", avgProcessingTime);
            logger.info("   - 总耗时: {} ms", totalProcessingTime.get());

            // 输出成功的结果
            for (String result : results) {
                logger.info("✅ {}", result);
            }

            // 检查是否有失败
            if (errorCount.get() > 0) {
                logger.warn("⚠️ 有 {} 个文本处理失败", errorCount.get());
            } else {
                logger.info("🎉 所有文本处理成功！");
            }

        } finally {
            // 确保资源回收
            logger.info("🧹 开始回收线程池资源...");

            // 关闭线程池
            executorService.shutdown();

            try {
                // 等待线程池关闭，最多等待30秒
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    logger.warn("⚠️ 线程池未能在30秒内正常关闭，强制关闭");
                    executorService.shutdownNow();

                    // 再等待10秒
                    if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                        logger.error("❌ 线程池强制关闭失败");
                    }
                }
            } catch (InterruptedException e) {
                logger.error("❌ 等待线程池关闭时被中断", e);
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }

            logger.info("✅ 线程池资源回收完成");
        }

        logger.info("🎉 多线程批量文本处理测试完成！");
    }

    /**
     * 使用资源管理器的多线程批量文本处理测试
     * 更好的资源管理和监控
     */
//    @Test
    public void testMultiThreadBatchTextProcessingWithResourceManager() throws Exception {
        logger.info("🧪 开始多线程批量文本处理测试（使用资源管理器）- 线程数: 10");

        final int THREAD_COUNT = 10;
        final int TEXTS_PER_THREAD = 3;

        // 创建线程池资源管理器
        ThreadPoolResourceManager resourceManager = new ThreadPoolResourceManager(THREAD_COUNT, "TTS-Test");

        try {
            // 提交任务到资源管理器
            for (int threadId = 0; threadId < THREAD_COUNT; threadId++) {
                final int currentThreadId = threadId;

                resourceManager.submit(() -> {
                    String threadName = "TTS-Thread-" + currentThreadId;

                    try {
                        logger.info("🚀 {} 开始执行", threadName);

                        // 为每个线程创建独立的 TTS 服务实例
                        SysConfig config = createTestConfig();
                        String voiceName = "关羽_" + currentThreadId;
                        String outputPath = "audio/test/managed_thread_" + currentThreadId + "/";

                        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

                        // 设置提示音频
                        String promptText = "好汉，你又是何人哪？关羽，字云长。";
                        ttsService.setPrompt("", "","guanyu");


                        // 每个线程处理的文本
                        String[] texts = {
                            String.format("[%s] 第一段测试文本。", threadName),
                            String.format("[%s] 第二段测试文本，稍微长一些。", threadName),
                            String.format("[%s] 第三段测试文本，用于验证资源管理器功能。", threadName)
                        };

                        // 处理文本
                        for (int i = 0; i < texts.length; i++) {
                            String text = texts[i];
                            logger.info("📝 {} 处理第 {}/{} 段文本", threadName, i + 1, texts.length);

                            String audioFilePath = ttsService.textToSpeech(text);
                            logger.info("✅ {} 第 {} 段音频文件已保存: {}", threadName, i + 1, audioFilePath);

                            // 避免服务器过载
                            Thread.sleep(500);
                        }

                        logger.info("🎉 {} 完成所有文本处理", threadName);
                        return threadName + " 成功完成";

                    } catch (Exception e) {
                        logger.error("❌ {} 执行失败", threadName, e);
                        throw new RuntimeException(threadName + " 执行失败", e);
                    }
                });
            }

            // 等待所有任务完成
            logger.info("⏳ 等待所有任务完成...");
            ThreadPoolResourceManager.ExecutionResult result = resourceManager.waitForCompletion(120, TimeUnit.SECONDS);

            // 输出详细统计信息
            logger.info("📊 多线程测试统计（资源管理器版本）:");
            logger.info("   - {}", result.toString());
            logger.info("   - 资源管理器状态: {}", resourceManager.getStatus());

            // 输出错误信息
            if (!result.getErrorMessages().isEmpty()) {
                logger.warn("⚠️ 错误详情:");
                for (String errorMsg : result.getErrorMessages()) {
                    logger.warn("   - {}", errorMsg);
                }
            }

            // 检查结果
            if (result.isAllSuccessful()) {
                logger.info("🎉 所有任务执行成功！");
            } else {
                logger.warn("⚠️ 有 {} 个任务失败", result.getErrorCount() + result.getTimeoutCount());
            }

        } finally {
            // 确保资源回收
            resourceManager.shutdown(30);
        }

        logger.info("🎉 多线程批量文本处理测试（资源管理器版本）完成！");
    }


    /**
     * 测试批量文本处理
     */
//    @Test
    public void testBatchTextProcessing() throws Exception {
        logger.info("🧪 开始批量文本处理测试");

        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream");
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

        // 设置提示音频
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        //todo
//        ttsService.setPrompt("","", "guanyu");
        ttsService.setPrompt(promptText,"audio/guanyu.WAV", "");

        // 测试文本列表
        String[] texts = {
                "第一段测试文本。",
                "第二段测试文本，稍微长一些。",
                "第三段测试文本，用于验证批量处理功能。"
        };

        logger.info("🎬 开始批量文本处理 - 共 {} 段文本", texts.length);

        try {
            for (int i = 0; i < texts.length; i++) {
                String text = texts[i];
                logger.info("处理第 {}/{} 段文本: {}", i + 1, texts.length, text);

                // 执行 TTS
                String audioFilePath = ttsService.textToSpeech(text);
                logger.info("✓ 第 {} 段音频文件已保存: {}", i + 1, audioFilePath);

                // 避免服务器过载
                Thread.sleep(1000);
            }

            logger.info("✅ 批量文本处理测试成功完成！");

        } catch (Exception e) {
            logger.error("❌ 批量文本处理测试失败", e);
            throw e;
        }
    }

}
