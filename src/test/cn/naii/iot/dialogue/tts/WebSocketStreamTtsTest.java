package cn.naii.iot.dialogue.tts;

import cn.naii.iot.dialogue.tts.providers.WebSocketStreamTtsService;
import cn.naii.iot.entity.SysConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * WebSocket 流式 TTS 服务测试类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class WebSocketStreamTtsTest {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketStreamTtsTest.class);


    /**
     * 测试流式 TTS 功能
     * 注意：此测试需要实际的 WebSocket TTS 服务器才能运行
     */
    @Test
    public void testWebSocketStreamTts() throws Exception {
        logger.info("🧪 开始 WebSocket 流式 TTS 测试");

        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream"); // 匹配 Python 版本的默认端口
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

        // 设置提示音频（匹配 Python 版本的协议要求）
        // String promptText = "好汉，你又是何人哪？关羽，字云长。";
        String promptWavPath = "audio/guanyu.WAV"; // 可选的音频文件路径

        try {
            // 设置提示音频（如果文件不存在会使用空的 Base64）
            ttsService.setPrompt("", "", "guanyu");
            logger.info("✓ 提示音频设置成功");
        } catch (Exception e) {
            logger.warn("提示音频文件不存在，使用默认配置: {}", e.getMessage());
            // 设置空的提示音频
            ttsService.setPrompt(null, null, null);
        }

        // 测试文本
        String testText = "你好，这是一个基于 WebSocket 的流式 TTS 测试。我们正在验证与 Python 服务器的协议兼容性。";


        logger.info("🎬 开始测试流式 TTS - 文本: {}", testText);

        // 用于统计和同步的变量
        AtomicInteger chunkCount = new AtomicInteger(0);
        AtomicInteger totalBytes = new AtomicInteger(0);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);

        // 创建流式回调
        StreamTtsCallback callback = new StreamTtsCallback() {
            @Override
            public void onStart() {
                logger.info("🎬 TTS 流式处理开始");
            }

            @Override
            public void onAudioChunk(byte[] audioChunk) throws Exception {
                int chunkIndex = chunkCount.incrementAndGet();
                int chunkSize = audioChunk.length;
                totalBytes.addAndGet(chunkSize);

                logger.info("📦 接收音频块 #{} - 大小: {} bytes, 累计: {} bytes",
                        chunkIndex, chunkSize, totalBytes.get());

                // 这里可以添加音频处理逻辑 todo
                // 例如：实时播放、发送到客户端、保存到缓冲区等
                // 示例：保存音频块到文件
            }

            @Override
            public void onComplete() {
                logger.info("✅ TTS 流式处理完成 - 总块数: {}, 总字节数: {}",
                        chunkCount.get(), totalBytes.get());
                completionLatch.countDown();
            }

            @Override
            public void onError(Throwable error) {
                logger.error("❌ TTS 流式处理错误", error);
                errorRef.set(new Exception("TTS 处理失败", error));
                completionLatch.countDown();
            }
        };
        try {
            // 执行流式 TTS
            ttsService.streamTextToSpeech(testText, callback);

            // 等待处理完成（最多等待 60 秒）
            boolean completed = completionLatch.await(60, TimeUnit.SECONDS);

            if (!completed) {
                logger.error("❌ TTS 处理超时");
                throw new Exception("TTS 处理超时");
            }

            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }

            logger.info("🎉 WebSocket 流式 TTS 测试成功完成！");

        } catch (Exception e) {
            logger.error("❌ WebSocket 流式 TTS 测试失败", e);
            throw e;
        }
    }


    //todo 多线程测试
    @Test
    public void test2(){
        logger.info("测试多线程");
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                try {
                    testBatchTextProcessing();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }


    /**
     * 测试批量文本处理
     */
    @Test
    public void testBatchTextProcessing() throws Exception {
        logger.info("🧪 开始批量文本处理测试");

        // 配置 TTS 服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://192.168.30.61:60009/ws/tts_stream");
        config.setApiKey("demo-api-key");

        String voiceName = "关羽";
        String outputPath = "audio/test/";

        // 创建 WebSocket 流式 TTS 服务实例
        WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);

        // 设置提示音频
        String promptText = "好汉，你又是何人哪？关羽，字云长。";
        //todo
//        ttsService.setPrompt("","", "guanyu");
        ttsService.setPrompt(promptText,"audio/guanyu.WAV", "");

        // 测试文本列表
        String[] texts = {
                "第一段测试文本。",
                "第二段测试文本，稍微长一些。",
                "第三段测试文本，用于验证批量处理功能。"
        };

        logger.info("🎬 开始批量文本处理 - 共 {} 段文本", texts.length);

        try {
            for (int i = 0; i < texts.length; i++) {
                String text = texts[i];
                logger.info("处理第 {}/{} 段文本: {}", i + 1, texts.length, text);

                // 执行 TTS
                String audioFilePath = ttsService.textToSpeech(text);
                logger.info("✓ 第 {} 段音频文件已保存: {}", i + 1, audioFilePath);

                // 避免服务器过载
                Thread.sleep(1000);
            }

            logger.info("✅ 批量文本处理测试成功完成！");

        } catch (Exception e) {
            logger.error("❌ 批量文本处理测试失败", e);
            throw e;
        }
    }

}
