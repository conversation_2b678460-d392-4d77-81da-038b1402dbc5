package cn.naii.iot.dialogue.tts;

import cn.naii.iot.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 模拟WebSocket TTS服务器
 * 用于演示和测试CustomizeTtsService的WebSocket流式TTS功能
 * 
 * 本服务器模拟了：
 * 1. WebSocket连接处理
 * 2. TTS请求解析
 * 3. 模拟音频数据生成和流式发送
 * 4. 状态消息发送
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class MockWebSocketTtsServer extends WebSocketServer {
    
    private static final Logger logger = LoggerFactory.getLogger(MockWebSocketTtsServer.class);
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(4);
    
    public MockWebSocketTtsServer(int port) {
        super(new InetSocketAddress(port));
        logger.info("🚀 模拟WebSocket TTS服务器初始化 - 端口: {}", port);
    }
    
    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        String clientAddress = conn.getRemoteSocketAddress().toString();
        logger.info("🔗 客户端连接: {}", clientAddress);
        
        // 发送连接确认消息
        sendStatusMessage(conn, "connected", "WebSocket连接已建立");
    }
    
    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        String clientAddress = conn.getRemoteSocketAddress().toString();
        logger.info("❌ 客户端断开: {} - 代码: {}, 原因: {}", clientAddress, code, reason);
    }
    
    @Override
    public void onMessage(WebSocket conn, String message) {
        logger.info("📨 收到消息: {}", message);
        
        try {
            // 解析TTS请求
            TtsRequest request = JsonUtil.fromJson(message, TtsRequest.class);
            
            if (request.text == null || request.text.trim().isEmpty()) {
                sendStatusMessage(conn, "error", "文本内容不能为空");
                return;
            }
            
            // 开始处理TTS请求
            processTtsRequest(conn, request);
            
        } catch (Exception e) {
            logger.error("处理消息失败", e);
            sendStatusMessage(conn, "error", "消息格式错误: " + e.getMessage());
        }
    }
    
    @Override
    public void onError(WebSocket conn, Exception ex) {
        String clientAddress = conn != null ? conn.getRemoteSocketAddress().toString() : "unknown";
        logger.error("WebSocket错误 - 客户端: {}", clientAddress, ex);
    }
    
    @Override
    public void onStart() {
        logger.info("✅ 模拟WebSocket TTS服务器启动成功");
        logger.info("📍 服务地址: ws://localhost:{}/", getPort());
        logger.info("🎯 TTS端点: ws://localhost:{}/tts/stream", getPort());
    }
    
    /**
     * 处理TTS请求
     */
    private void processTtsRequest(WebSocket conn, TtsRequest request) {
        logger.info("🎬 开始处理TTS请求 - 文本: {}, 语音: {}", request.text, request.voice);
        
        // 发送开始状态
        sendStatusMessage(conn, "started", "TTS处理已开始");
        
        // 模拟TTS处理过程
        scheduler.schedule(() -> {
            try {
                // 发送处理中状态
                sendStatusMessage(conn, "processing", "正在生成音频...");
                
                // 模拟生成音频数据并流式发送
                generateAndSendAudioStream(conn, request);
                
            } catch (Exception e) {
                logger.error("TTS处理失败", e);
                sendStatusMessage(conn, "error", "TTS处理失败: " + e.getMessage());
            }
        }, 100, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 生成并发送音频流
     */
    private void generateAndSendAudioStream(WebSocket conn, TtsRequest request) {
        String text = request.text;
        int textLength = text.length();
        
        // 根据文本长度估算音频块数量（每个字符大约对应100ms音频）
        int estimatedChunks = Math.max(5, textLength / 2);
        int chunkSize = 1600; // 16kHz * 1声道 * 2字节 * 0.05秒 = 1600字节（50ms音频）
        
        logger.info("📦 开始发送音频流 - 预计块数: {}, 每块大小: {} bytes", estimatedChunks, chunkSize);
        
        // 分批发送音频数据
        for (int i = 0; i < estimatedChunks; i++) {
            final int chunkIndex = i;
            final boolean isLastChunk = (i == estimatedChunks - 1);
            
            scheduler.schedule(() -> {
                try {
                    if (conn.isOpen()) {
                        // 生成模拟PCM音频数据
                        byte[] audioChunk = generateMockPcmAudio(chunkSize, chunkIndex);
                        
                        // 发送音频数据
                        conn.send(audioChunk);
                        
                        logger.debug("📤 发送音频块 #{}/{} - 大小: {} bytes", 
                            chunkIndex + 1, estimatedChunks, audioChunk.length);
                        
                        // 如果是最后一块，发送完成状态
                        if (isLastChunk) {
                            scheduler.schedule(() -> {
                                sendStatusMessage(conn, "completed", "TTS处理完成");
                                logger.info("✅ 音频流发送完成 - 总块数: {}", estimatedChunks);
                            }, 50, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    logger.error("发送音频块失败", e);
                    sendStatusMessage(conn, "error", "发送音频失败: " + e.getMessage());
                }
            }, i * 50, TimeUnit.MILLISECONDS); // 每50ms发送一块
        }
    }
    
    /**
     * 生成模拟PCM音频数据
     */
    private byte[] generateMockPcmAudio(int size, int chunkIndex) {
        byte[] audioData = new byte[size];
        Random random = new Random(chunkIndex); // 使用块索引作为种子，确保可重现
        
        // 生成模拟的PCM音频数据（16位，单声道，16kHz）
        for (int i = 0; i < size; i += 2) {
            // 生成简单的正弦波音频
            double frequency = 440.0; // A4音符频率
            double amplitude = 0.3; // 音量
            double sampleRate = 16000.0;
            double time = (chunkIndex * size / 2 + i / 2) / sampleRate;
            
            short sample = (short) (amplitude * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
            
            // 小端序存储
            audioData[i] = (byte) (sample & 0xFF);
            if (i + 1 < size) {
                audioData[i + 1] = (byte) ((sample >> 8) & 0xFF);
            }
        }
        
        return audioData;
    }
    
    /**
     * 发送状态消息
     */
    private void sendStatusMessage(WebSocket conn, String status, String message) {
        try {
            TtsResponse response = new TtsResponse();
            response.status = status;
            response.message = message;
            
            String responseJson = JsonUtil.toJson(response);
            conn.send(responseJson);
            
            logger.debug("📤 发送状态消息: {}", responseJson);
            
        } catch (Exception e) {
            logger.error("发送状态消息失败", e);
        }
    }
    
    /**
     * TTS请求数据类
     */
    private static class TtsRequest {
        @JsonProperty("text")
        public String text;
        
        @JsonProperty("voice")
        public String voice;
        
        @JsonProperty("format")
        public String format;
        
        @JsonProperty("sample_rate")
        public int sampleRate;
        
        @JsonProperty("channels")
        public int channels;
    }
    
    /**
     * TTS响应数据类
     */
    private static class TtsResponse {
        @JsonProperty("status")
        public String status;
        
        @JsonProperty("message")
        public String message;
        
        @JsonProperty("audio_length")
        public Integer audioLength;
        
        @JsonProperty("chunk_index")
        public Integer chunkIndex;
    }
    
    /**
     * 启动模拟服务器
     */
    public static void main(String[] args) {
        int port = 8080;
        
        MockWebSocketTtsServer server = new MockWebSocketTtsServer(port);
        server.start();
        
        logger.info("🎯 模拟WebSocket TTS服务器已启动");
        logger.info("📍 测试地址: ws://localhost:{}/tts/stream", port);
        logger.info("💡 使用Ctrl+C停止服务器");
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("🛑 正在关闭服务器...");
            try {
                server.stop();
                server.scheduler.shutdown();
                logger.info("✅ 服务器已关闭");
            } catch (Exception e) {
                logger.error("关闭服务器失败", e);
            }
        }));
    }
}
