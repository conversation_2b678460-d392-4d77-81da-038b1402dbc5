package java.cn.naii.iot.dialogue.tts;

import cn.naii.iot.dialogue.tts.StreamTtsCallback;
import cn.naii.iot.dialogue.tts.providers.CustomizeTtsService;
import cn.naii.iot.entity.SysConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 自定义TTS客户端测试类
 * 演示如何使用CustomizeTtsService进行WebSocket流式TTS
 * 
 * 本测试展示了：
 * 1. 如何配置自定义TTS服务
 * 2. 如何使用流式TTS接口
 * 3. 如何处理音频流数据
 * 4. 错误处理和连接管理
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class CustomizeTtsClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomizeTtsClientTest.class);
    
    /**
     * 测试流式TTS功能
     * 注意：此测试需要实际的WebSocket TTS服务器才能运行
     */
    @Test
    public void testStreamTextToSpeech() throws Exception {
        // 配置TTS服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://localhost:8080"); // 替换为实际的WebSocket服务地址
        config.setApiKey("your-api-key"); // 替换为实际的API密钥
        
        String voiceName = "zh-CN-XiaoyiNeural"; // 语音名称
        String outputPath = "audio/test/"; // 输出路径
        
        // 创建自定义TTS服务实例
        CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);
        
        // 测试文本
        String testText = "你好，这是一个WebSocket流式TTS测试。我们正在演示如何使用自定义TTS服务进行实时语音合成。";
        
        logger.info("🎬 开始测试流式TTS - 文本: {}", testText);
        
        // 用于统计和同步的变量
        AtomicInteger chunkCount = new AtomicInteger(0);
        AtomicInteger totalBytes = new AtomicInteger(0);
        AtomicReference<Exception> errorRef = new AtomicReference<>();
        CountDownLatch completionLatch = new CountDownLatch(1);
        
        // 创建流式回调
        StreamTtsCallback callback = new StreamTtsCallback() {
            @Override
            public void onStart() {
                logger.info("🎬 TTS流式处理开始");
            }
            
            @Override
            public void onAudioChunk(byte[] audioChunk) throws Exception {
                int chunkIndex = chunkCount.incrementAndGet();
                int chunkSize = audioChunk.length;
                totalBytes.addAndGet(chunkSize);
                
                logger.info("📦 接收音频块 #{} - 大小: {} bytes, 累计: {} bytes", 
                    chunkIndex, chunkSize, totalBytes.get());
                
                // 这里可以添加音频处理逻辑
                // 例如：播放音频、保存到文件、发送到客户端等
                processAudioChunk(audioChunk, chunkIndex);
            }
            
            @Override
            public void onComplete() {
                logger.info("✅ TTS流式处理完成 - 总块数: {}, 总字节数: {}", 
                    chunkCount.get(), totalBytes.get());
                completionLatch.countDown();
            }
            
            @Override
            public void onError(Throwable error) {
                logger.error("❌ TTS流式处理错误", error);
                errorRef.set(new Exception("TTS处理失败", error));
                completionLatch.countDown();
            }
        };
        
        try {
            // 执行流式TTS
            ttsService.streamTextToSpeech(testText, callback);
            
            // 等待处理完成（最多等待30秒）
            boolean completed = completionLatch.await(30, TimeUnit.SECONDS);
            
            if (!completed) {
                logger.error("❌ TTS处理超时");
                throw new Exception("TTS处理超时");
            }
            
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }
            
            logger.info("🎉 流式TTS测试成功完成！");
            
        } catch (Exception e) {
            logger.error("❌ 流式TTS测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试非流式TTS功能
     */
    @Test
    public void testTextToSpeech() throws Exception {
        // 配置TTS服务
        SysConfig config = new SysConfig();
        config.setProvider("customize");
        config.setApiUrl("ws://localhost:8080");
        config.setApiKey("your-api-key");
        
        String voiceName = "zh-CN-XiaoyiNeural";
        String outputPath = "audio/test/";
        
        // 创建自定义TTS服务实例
        CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);
        
        // 测试文本
        String testText = "这是一个非流式TTS测试。";
        
        logger.info("🎬 开始测试非流式TTS - 文本: {}", testText);
        
        try {
            // 执行非流式TTS
            String audioFilePath = ttsService.textToSpeech(testText);
            
            logger.info("✅ 非流式TTS测试成功 - 音频文件: {}", audioFilePath);
            
        } catch (Exception e) {
            logger.error("❌ 非流式TTS测试失败", e);
            throw e;
        }
    }
    
    /**
     * 处理音频块数据
     * 这里可以添加具体的音频处理逻辑
     */
    private void processAudioChunk(byte[] audioChunk, int chunkIndex) {
        // 示例：可以在这里添加以下处理逻辑
        
        // 1. 实时播放音频
        // audioPlayer.play(audioChunk);
        
        // 2. 保存音频块到文件
        // saveAudioChunk(audioChunk, chunkIndex);
        
        // 3. 发送音频到WebSocket客户端
        // webSocketSession.sendBinary(audioChunk);
        
        // 4. 音频格式转换
        // byte[] convertedAudio = AudioUtils.convertFormat(audioChunk);
        
        // 5. 音频质量分析
        // analyzeAudioQuality(audioChunk);
        
        logger.debug("处理音频块 #{} - 大小: {} bytes", chunkIndex, audioChunk.length);
    }
    
    /**
     * 演示如何创建模拟的WebSocket TTS服务器
     * 注意：这只是一个示例，实际使用时需要实现真正的WebSocket服务器
     */
    public static void createMockTtsServer() {
        logger.info("💡 提示：要运行此测试，您需要创建一个WebSocket TTS服务器");
        logger.info("服务器应该：");
        logger.info("1. 监听WebSocket连接 (例如: ws://localhost:8080/tts/stream)");
        logger.info("2. 接收TTS请求JSON消息");
        logger.info("3. 返回状态消息和音频数据流");
        logger.info("4. 支持以下消息格式：");
        logger.info("   请求: {\"text\":\"文本\",\"voice\":\"语音\",\"format\":\"pcm\",\"sample_rate\":16000,\"channels\":1}");
        logger.info("   响应: {\"status\":\"started|processing|completed|error\",\"message\":\"消息\"}");
        logger.info("   音频: 二进制PCM数据流");
    }
}
