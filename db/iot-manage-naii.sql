-- MySQL dump 10.13  Distrib 8.0.19, for Win64 (x86_64)
--
-- Host: *************    Database: iot-manage-naii
-- ------------------------------------------------------
-- Server version	8.0.40

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `location_coordinate`
--

DROP TABLE IF EXISTS `location_coordinate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `location_coordinate` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '经纬度ID，主键自增',
  `province` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '省份（直辖市,自治区）',
  `city` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市（自治州）',
  `county` varchar(300) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '自治县、县级市',
  `longitude` decimal(20,6) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(20,6) DEFAULT NULL COMMENT '纬度',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3233 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='地理位置坐标表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_code`
--

DROP TABLE IF EXISTS `sys_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_code` (
  `codeid` int unsigned NOT NULL AUTO_INCREMENT COMMENT '验证码ID',
  `code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '验证码',
  `type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `deviceid` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备ID',
  `sessionid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话ID',
  `audiopath` text COLLATE utf8mb4_general_ci COMMENT '音频路径',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`codeid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_config` (
  `configid` int unsigned NOT NULL COMMENT '配置ID',
  `userid` int NOT NULL COMMENT '用户ID',
  `configtype` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置类型',
  `modeltype` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型类型',
  `provider` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提供商',
  `configname` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置名称',
  `configdesc` text COLLATE utf8mb4_general_ci COMMENT '配置描述',
  `appid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用ID',
  `apikey` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'API密钥',
  `apisecret` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'API密钥',
  `ak` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '访问密钥',
  `sk` text COLLATE utf8mb4_general_ci COMMENT '密钥',
  `apiurl` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'API地址',
  `isdefault` enum('1','0') COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否默认',
  `state` enum('1','0') COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`configid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_device`
--

DROP TABLE IF EXISTS `sys_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_device` (
  `deviceid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备ID',
  `devicename` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备名称',
  `roleid` int unsigned DEFAULT NULL COMMENT '角色ID',
  `function_names` varchar(250) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可用全局function的名称列表',
  `ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP地址',
  `location` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地理位置',
  `wifiname` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'WiFi名称',
  `chipmodelname` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '芯片型号',
  `type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '芯片类型',
  `version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '固件版本',
  `state` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '设备状态',
  `userid` int DEFAULT NULL COMMENT '用户ID',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `lastlogin` timestamp NULL DEFAULT NULL COMMENT '最后在线时间',
  `openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序用户的设备openid',
  `volume` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备音量',
  `batteryStatus` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备电池状态',
  `signalStrength` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '信号强度'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_message`
--

DROP TABLE IF EXISTS `sys_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_message` (
  `messageid` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `deviceid` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备ID',
  `sessionid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话ID',
  `sender` enum('user','assistant') COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发送者',
  `roleid` bigint DEFAULT NULL COMMENT '角色ID',
  `message` text COLLATE utf8mb4_general_ci COMMENT '消息内容',
  `messagetype` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息类型',
  `audiopath` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '音频路径',
  `state` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '状态',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`messageid`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `roleid` int unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `rolename` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色名称',
  `roledesc` text COLLATE utf8mb4_general_ci COMMENT '角色描述',
  `avatar` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `ttsid` int DEFAULT NULL COMMENT 'TTS配置ID',
  `modelid` int unsigned DEFAULT NULL COMMENT '模型ID',
  `sttid` int unsigned DEFAULT NULL COMMENT 'STT配置ID',
  `vadspeechth` float DEFAULT NULL COMMENT 'VAD语音阈值',
  `vadsilenceth` float DEFAULT NULL COMMENT 'VAD静音阈值',
  `vadenergyth` float DEFAULT NULL COMMENT 'VAD能量阈值',
  `vadsilencems` int DEFAULT NULL COMMENT 'VAD静音毫秒数',
  `timbre_id` bigint DEFAULT NULL COMMENT '音色id',
  `voicename` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语音名称',
  `state` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '状态',
  `isdefault` enum('1','0') COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否默认',
  `userid` int NOT NULL COMMENT '用户ID',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`roleid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_template`
--

DROP TABLE IF EXISTS `sys_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_template` (
  `userid` int NOT NULL COMMENT '用户ID',
  `templateid` int unsigned NOT NULL COMMENT '模板ID',
  `templatename` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板名称',
  `templatedesc` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板描述',
  `templatecontent` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板内容',
  `category` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类',
  `isdefault` enum('1','0') COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否默认',
  `state` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '状态',
  `createtime` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` timestamp NULL DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_timbre`
--

DROP TABLE IF EXISTS `sys_timbre`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_timbre` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `label` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '音色名称',
  `value` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'male-男声 female-女声',
  `provider` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提供商',
  `model_id` bigint DEFAULT NULL COMMENT '模型来源',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='音色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `userid` int unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `password` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
  `tel` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话',
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `state` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '状态',
  `loginip` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录IP',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别 0-未知 1-男 2-女',
  `birth_place` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出生地址',
  `calendar_type` tinyint DEFAULT NULL COMMENT '日历类型 0-未知 1-农历 2-阳历',
  `birth_day_time` datetime DEFAULT NULL COMMENT '出生日期',
  `isadmin` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否管理员',
  `logintime` datetime DEFAULT NULL COMMENT '登录时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `voice_print`
--

DROP TABLE IF EXISTS `voice_print`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `voice_print` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户声纹ID，主键自增',
  `wx_user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序用户表主键id',
  `user_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `nick_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
  `birth` datetime DEFAULT NULL COMMENT '出生年月日',
  `birth_place` bigint DEFAULT NULL COMMENT '出生地ID',
  `voice_print` bigint DEFAULT NULL COMMENT '声纹标识',
  `gender` int DEFAULT NULL COMMENT '性别：0未知，1男，2女',
  `status` int DEFAULT NULL COMMENT '状态：0正常，1禁用',
  `mobile` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户声纹信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'iot-manage-naii'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-10-23 10:12:32
