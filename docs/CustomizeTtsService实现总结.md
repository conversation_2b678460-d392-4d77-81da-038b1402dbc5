# CustomizeTtsService 实现总结

## 项目概述

基于现有的 `test_tts_client` 代码结构，我们在 `CustomizeTtsService` 中成功实现了 WebSocket 文本流发送和音频流接收功能。这个实现展示了如何在 IoT 管理系统中集成实时流式 TTS（文本转语音）服务。

## 完成的工作

### 1. 核心服务实现

#### `CustomizeTtsService.java`
- ✅ **完整的 WebSocket TTS 服务实现**
- ✅ **流式文本发送**：通过 WebSocket 发送 JSON 格式的 TTS 请求
- ✅ **音频流接收**：实时接收和处理 PCM 音频数据流
- ✅ **连接管理**：自动处理连接建立、维护和关闭
- ✅ **错误处理**：完善的异常处理和错误恢复机制
- ✅ **状态监控**：实时监控 TTS 处理状态
- ✅ **支持流式和非流式**：同时支持两种调用方式

#### 关键特性：
```java
// 流式 TTS 接口
public void streamTextToSpeech(String text, StreamTtsCallback callback)

// 非流式 TTS 接口（内部使用流式实现）
public String textToSpeech(String text)

// WebSocket 消息处理
private void handleTextMessage(String message, StreamTtsCallback callback)
private void handleBinaryMessage(byte[] audioData, StreamTtsCallback callback)
```

### 2. 工厂集成

#### `TtsServiceFactory.java`
- ✅ **添加自定义 TTS 支持**：在工厂类中注册 "customize" 提供商
- ✅ **无缝集成**：与现有 TTS 服务（阿里云、讯飞、MiniMax 等）保持一致的接口

```java
case "customize" -> new CustomizeTtsService(config, voiceName, outputPath);
```

### 3. 测试和演示代码

#### `CustomizeTtsClientTest.java`
- ✅ **完整的测试用例**：演示流式和非流式 TTS 使用
- ✅ **回调处理示例**：展示如何处理音频流数据
- ✅ **错误处理演示**：展示异常处理最佳实践

#### `MockWebSocketTtsServer.java`
- ✅ **模拟 WebSocket 服务器**：用于测试和演示
- ✅ **完整的协议实现**：支持 TTS 请求解析和音频流发送
- ✅ **状态消息处理**：模拟真实 TTS 服务器的状态反馈

### 4. 使用示例和文档

#### `CustomizeTtsExample.java`
- ✅ **实际使用示例**：展示在 Spring Boot 项目中的集成方式
- ✅ **多种使用场景**：基本使用、异步处理、批量处理、WebSocket 集成
- ✅ **最佳实践**：错误处理、性能优化、资源管理

#### 配置文件
- ✅ **详细配置示例**：`application-customize-tts.properties`
- ✅ **环境特定配置**：开发、测试、生产环境配置
- ✅ **性能调优参数**：连接池、缓冲区、超时设置

#### 文档
- ✅ **使用指南**：`CustomizeTtsService使用指南.md`
- ✅ **架构说明**：详细的架构图和流程说明
- ✅ **故障排除**：常见问题和解决方案

## 技术架构

### WebSocket 通信流程

```
客户端 (CustomizeTtsService)          服务器 (TTS Server)
         │                                    │
         │ 1. WebSocket 连接建立                │
         │ ──────────────────────────────────► │
         │                                    │
         │ 2. 发送 TTS 请求 (JSON)              │
         │ ──────────────────────────────────► │
         │                                    │
         │ 3. 接收状态消息 (JSON)               │
         │ ◄────────────────────────────────── │
         │                                    │
         │ 4. 接收音频流 (Binary)               │
         │ ◄────────────────────────────────── │
         │ ◄────────────────────────────────── │
         │ ◄────────────────────────────────── │
         │                                    │
         │ 5. 接收完成状态 (JSON)               │
         │ ◄────────────────────────────────── │
         │                                    │
         │ 6. 关闭连接                         │
         │ ──────────────────────────────────► │
```

### 消息格式

#### TTS 请求消息
```json
{
  "text": "要转换的文本内容",
  "voice": "zh-CN-XiaoyiNeural",
  "format": "pcm",
  "sample_rate": 16000,
  "channels": 1
}
```

#### 状态响应消息
```json
{
  "status": "started|processing|completed|error",
  "message": "状态描述信息",
  "audio_length": 1024,
  "chunk_index": 1
}
```

#### 音频数据
- **格式**: 二进制 PCM 数据
- **参数**: 16kHz, 单声道, 16位
- **传输**: WebSocket Binary Message

## 与现有系统的集成

### 1. 与 DialogueService 集成

现有的 `DialogueService` 已经支持流式 TTS，我们的实现可以无缝集成：

```java
// 在 DialogueService 中的使用
ttsService.streamTextToSpeech(
    task.emoSentence.getTtsSentence(),
    new StreamTtsCallback() {
        @Override
        public void onAudioChunk(byte[] pcmChunk) throws Exception {
            pcmChunkHandler.accept(pcmChunk);
        }
        // ... 其他回调方法
    }
);
```

### 2. 与 AudioService 集成

可以直接与现有的 `AudioService.sendStreamingAudio` 方法集成：

```java
audioService.sendStreamingAudio(
    session, sentence, isFirst, isLast,
    pcmChunkHandler -> {
        customizeTtsService.streamTextToSpeech(text, new StreamTtsCallback() {
            @Override
            public void onAudioChunk(byte[] pcmChunk) throws Exception {
                pcmChunkHandler.accept(pcmChunk);
            }
        });
    }
);
```

### 3. 配置管理

通过现有的 `SysConfig` 系统进行配置：

```java
SysConfig config = new SysConfig();
config.setProvider("customize");
config.setApiUrl("ws://your-tts-server:8080");
config.setApiKey("your-api-key");
```

## 性能特点

### 1. 低延迟
- ✅ **流式处理**：音频数据实时生成和传输
- ✅ **WebSocket 连接**：避免 HTTP 请求开销
- ✅ **异步处理**：非阻塞音频数据处理

### 2. 高并发
- ✅ **连接池管理**：复用 WebSocket 连接
- ✅ **虚拟线程**：支持大量并发请求
- ✅ **资源管理**：自动清理连接和缓冲区

### 3. 可扩展性
- ✅ **插件化架构**：通过工厂模式集成
- ✅ **配置驱动**：支持多环境配置
- ✅ **负载均衡**：支持多服务器部署

## 使用场景

### 1. 实时对话系统
- 用户语音输入 → STT → 对话处理 → **CustomizeTtsService** → 音频输出
- 支持低延迟的实时语音交互

### 2. IoT 设备语音反馈
- 设备状态变化 → 生成提示文本 → **CustomizeTtsService** → 设备播放
- 支持设备端实时语音提示

### 3. 批量语音生成
- 批量文本处理 → **CustomizeTtsService** → 音频文件生成
- 支持大规模语音内容生成

## 部署建议

### 1. 开发环境
```properties
tts.provider=customize
tts.websocket.url=ws://localhost:8080
tts.mock.enabled=true
```

### 2. 测试环境
```properties
tts.provider=customize
tts.websocket.url=ws://test-tts.example.com:8080
tts.monitoring.enabled=true
```

### 3. 生产环境
```properties
tts.provider=customize
tts.websocket.url=wss://tts-api.example.com:443
tts.websocket.ssl.verify=true
tts.cache.enabled=true
tts.monitoring.enabled=true
```

## 总结

我们成功实现了一个完整的 WebSocket 流式 TTS 服务，该服务：

1. **完全兼容现有架构**：无需修改现有的 TTS 接口和调用方式
2. **提供完整的流式支持**：支持实时音频流处理
3. **包含完善的错误处理**：确保服务的稳定性和可靠性
4. **提供详细的文档和示例**：便于开发者理解和使用
5. **支持多种部署场景**：从开发测试到生产环境

这个实现为 IoT 管理系统提供了一个强大而灵活的语音合成解决方案，可以满足各种实时语音交互需求。
