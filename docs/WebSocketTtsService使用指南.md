# WebSocket TTS Service 使用指南

## 概述

基于 `test_tts_client.py` 和 `tts_stream_server.py` 的协议，我们完善了 Java 版本的 `CustomizeTtsService`，实现了完全兼容的 WebSocket 流式 TTS 功能。该实现展示了如何使用 WebSocket 发送文本流并接收音频流，包括完整的流式 TTS 处理流程、连接管理、错误处理和音频数据流处理。

## 功能特性

- ✅ **Python 协议兼容**：完全兼容 `test_tts_client.py` 和 `tts_stream_server.py` 的协议
- ✅ **WebSocket 流式通信**：支持实时双向数据传输
- ✅ **配置信息发送**：支持 prompt_text 和 prompt_wav_base64 配置
- ✅ **文本流式发送**：将文本请求通过 WebSocket 发送到 TTS 服务器
- ✅ **音频流式接收**：实时接收和处理音频数据流（PCM 格式，22050Hz）
- ✅ **控制信号处理**：支持 `__STREAM_COMPLETE__`, `__ERROR__`, `__END_STREAM__` 等信号
- ✅ **连接管理**：自动处理连接建立、维护和关闭
- ✅ **错误处理**：完善的异常处理和错误恢复机制
- ✅ **提示音频支持**：支持设置提示文本和音频文件

## 协议流程

基于 Python 版本的协议设计，完整的 WebSocket TTS 流程如下：

```
客户端 (Java)                    服务器 (Python/Java Mock)
     │                                    │
     │ 1. 建立 WebSocket 连接               │
     │ ──────────────────────────────────► │
     │                                    │
     │ 2. 发送配置信息 (JSON)               │
     │ {"prompt_text":"...",              │
     │  "prompt_wav_base64":"..."}        │
     │ ──────────────────────────────────► │
     │                                    │
     │ 3. 发送文本消息                     │
     │ "要转换的文本内容"                   │
     │ ──────────────────────────────────► │
     │                                    │
     │ 4. 接收音频流 (Binary)               │
     │ ◄────────────────────────────────── │
     │ ◄────────────────────────────────── │
     │ ◄────────────────────────────────── │
     │                                    │
     │ 5. 接收完成信号 (Text)               │
     │ "__STREAM_COMPLETE__"              │
     │ ◄────────────────────────────────── │
     │                                    │
     │ 6. 发送结束信号                     │
     │ "__END_STREAM__"                   │
     │ ──────────────────────────────────► │
     │                                    │
     │ 7. 接收关闭确认                     │
     │ "__CONNECTION_CLOSING__"           │
     │ ◄────────────────────────────────── │
     │                                    │
     │ 8. 关闭连接                         │
     │ ──────────────────────────────────► │
```

## 使用方法

### 1. 基本配置

```java
// 创建配置对象（匹配Python版本）
SysConfig config = new SysConfig();
config.setProvider("customize");
config.setApiUrl("ws://localhost:60009"); // 匹配Python版本的默认端口
config.setApiKey("demo-api-key");

// 设置语音参数
String voiceName = "关羽";
String outputPath = "audio/";

// 创建服务实例
CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);
```

### 2. 设置提示音频

```java
// 设置提示文本和音频（匹配Python版本的协议要求）
String promptText = "好汉，你又是何人哪？关羽，字云长。";
String promptWavPath = "prompt.wav"; // 可选的音频文件路径

try {
    ttsService.setPrompt(promptText, promptWavPath);
    logger.info("✓ 提示音频设置成功");
} catch (Exception e) {
    logger.warn("提示音频文件不存在，使用默认配置: {}", e.getMessage());
    // 设置空的提示音频
    ttsService.setPrompt(promptText, null);
}
```

### 3. 流式 TTS 使用

```java
String text = "你好，这是一个WebSocket流式TTS测试。";

// 创建流式回调
StreamTtsCallback callback = new StreamTtsCallback() {
    @Override
    public void onStart() {
        System.out.println("🎬 TTS 处理开始");
    }
    
    @Override
    public void onAudioChunk(byte[] audioChunk) throws Exception {
        System.out.println("📦 接收音频块: " + audioChunk.length + " bytes");
        
        // 处理音频数据（22050Hz PCM格式）
        // 1. 实时播放
        // audioPlayer.play(audioChunk);
        
        // 2. 发送到客户端
        // webSocketSession.sendBinary(audioChunk);
        
        // 3. 保存到缓冲区
        // audioBuffer.add(audioChunk);
    }
    
    @Override
    public void onComplete() {
        System.out.println("✅ TTS 处理完成");
    }
    
    @Override
    public void onError(Throwable error) {
        System.err.println("❌ TTS 处理错误: " + error.getMessage());
    }
};

// 执行流式 TTS
ttsService.streamTextToSpeech(text, callback);
```

### 4. 非流式 TTS 使用

```java
String text = "这是一个非流式TTS测试。";

// 执行非流式 TTS（内部使用流式实现）
String audioFilePath = ttsService.textToSpeech(text);
System.out.println("音频文件已保存: " + audioFilePath);
```

## 服务器部署

### 1. 使用 Python 服务器

```bash
# 启动 Python TTS 服务器
python tts_stream_server.py --port 60009 --model_dir /path/to/model

# 检查服务器状态
curl http://localhost:60009/health
```

### 2. 使用 Java 模拟服务器

```java
// 启动模拟服务器进行测试
MockWebSocketTtsServer server = new MockWebSocketTtsServer(60009);
server.start();

// 或者使用启动脚本
java -cp "your-classpath" scripts.StartMockTtsServer 60009
```

### 3. 健康检查

```bash
# 检查服务器健康状态
curl http://localhost:60009/health

# 预期响应
{
  "status": "healthy",
  "active_connections": 0,
  "total_requests": 0,
  "max_concurrent_requests": 4,
  "available_slots": 4
}
```

## 测试和验证

### 1. 运行集成测试

```java
// 运行完整的集成测试
WebSocketTtsIntegrationTest test = new WebSocketTtsIntegrationTest();

// 基本功能测试
test.testBasicWebSocketTts();

// 批量处理测试
test.testBatchTextProcessing();

// 非流式测试
test.testNonStreamingTts();

// 错误处理测试
test.testErrorHandling();
```

### 2. 使用 Python 客户端验证

```bash
# 使用 Python 客户端测试 Java 服务器
python test_tts_client.py \
  --host localhost \
  --port 60009 \
  --prompt_text "好汉，你又是何人哪？关羽，字云长。" \
  --prompt_wav prompt.wav \
  --texts "你好，这是测试文本。" \
  --output_dir ./output
```

## 协议兼容性

### 消息格式

#### 1. 配置消息（客户端 → 服务器）

```json
{
  "prompt_text": "好汉，你又是何人哪？关羽，字云长。",
  "prompt_wav_base64": "UklGRiQAAABXQVZFZm10IBAAAAABAAEA..."
}
```

#### 2. 文本消息（客户端 → 服务器）

```
要转换的文本内容
```

#### 3. 控制信号（服务器 → 客户端）

```
__STREAM_COMPLETE__     # 单轮处理完成
__CONNECTION_CLOSING__  # 连接关闭确认
__ERROR__:错误信息      # 错误消息
```

#### 4. 结束信号（客户端 → 服务器）

```
__END_STREAM__          # 结束会话
```

#### 5. 音频数据（服务器 → 客户端）

- **格式**: 二进制数据
- **编码**: PCM（16位，单声道，22050Hz）
- **传输**: WebSocket Binary Message

### 端点路径

```
WebSocket: ws://host:port/ws/tts_stream
HTTP健康检查: http://host:port/health
```

## 性能优化

### 1. 连接复用

```java
// 配置连接池
OkHttpClient httpClient = new OkHttpClient.Builder()
    .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
    .connectTimeout(30, TimeUnit.SECONDS)
    .readTimeout(300, TimeUnit.SECONDS)
    .writeTimeout(10, TimeUnit.SECONDS)
    .build();
```

### 2. 音频缓冲

```java
// 使用缓冲区处理音频流
private final Queue<byte[]> audioBuffer = new ConcurrentLinkedQueue<>();

@Override
public void onAudioChunk(byte[] audioChunk) {
    audioBuffer.offer(audioChunk);
    // 异步处理音频数据
    processAudioAsync();
}
```

### 3. 批量处理

```java
// 批量处理多个文本
String[] texts = {"文本1", "文本2", "文本3"};
for (String text : texts) {
    ttsService.streamTextToSpeech(text, callback);
    Thread.sleep(100); // 避免服务器过载
}
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查服务器是否启动：`curl http://localhost:60009/health`
   - 检查防火墙设置
   - 调整连接超时参数

2. **音频数据丢失**
   - 检查网络带宽和延迟
   - 增加客户端缓冲区大小
   - 检查服务器并发限制

3. **协议不兼容**
   - 确认使用正确的端点路径：`/ws/tts_stream`
   - 检查控制信号格式
   - 验证音频格式（22050Hz PCM）

### 调试技巧

```java
// 启用详细日志
logging.level.cn.naii.iot.dialogue.tts.providers.CustomizeTtsService=DEBUG

// 监控连接状态
webSocket.addListener(new ConnectionMonitor());

// 统计音频数据
private final AtomicLong totalBytes = new AtomicLong();
private final AtomicInteger chunkCount = new AtomicInteger();
```

## 总结

通过参考 `test_tts_client.py` 和 `tts_stream_server.py` 的实现，我们成功完善了 Java 版本的 WebSocket TTS 流式服务，实现了：

1. **完全协议兼容**：与 Python 版本的协议完全兼容
2. **流式音频处理**：支持实时音频流生成和传输
3. **配置信息支持**：支持提示文本和音频配置
4. **控制信号处理**：正确处理各种控制信号
5. **错误处理机制**：完善的异常处理和错误恢复
6. **测试和验证**：提供完整的测试用例和验证方法

该实现为 IoT 管理系统提供了一个强大而灵活的语音合成解决方案，可以满足各种实时语音交互需求。
