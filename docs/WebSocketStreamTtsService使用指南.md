# WebSocketStreamTtsService 使用指南

## 概述

`WebSocketStreamTtsService` 是基于 `test_tts_client.py` 和 `tts_stream_server.py` 协议实现的 WebSocket 流式 TTS 服务。该服务完全兼容 Python 版本的 TTS 流式服务器，支持实时音频流传输和处理。

## 核心特性

- ✅ **完全协议兼容**：与 Python `tts_stream_server.py` 100% 兼容
- ✅ **流式音频传输**：支持实时音频块接收和处理
- ✅ **提示音频支持**：支持 Base64 编码的提示音频文件
- ✅ **错误处理机制**：完整的错误处理和重试机制
- ✅ **异步处理**：基于 WebSocket 的异步通信
- ✅ **音频格式标准**：PCM 格式，22050Hz 采样率

## 协议流程

### 1. 连接建立
```
客户端 -> 服务器: WebSocket 连接到 /ws/tts_stream
```

### 2. 配置阶段
```json
{
  "prompt_text": "好汉，你又是何人哪？关羽，字云长。",
  "prompt_wav_base64": "UklGRiQAAABXQVZFZm10IBAAAAABAAEA..."
}
```

### 3. 文本处理
```
客户端 -> 服务器: "要转换的文本内容"
服务器 -> 客户端: [二进制音频数据块1]
服务器 -> 客户端: [二进制音频数据块2]
...
服务器 -> 客户端: "__STREAM_COMPLETE__"
```

### 4. 结束流程
```
客户端 -> 服务器: "__END_STREAM__"
服务器 -> 客户端: "__CONNECTION_CLOSING__"
```

## 快速开始

### 1. 基本配置

```java
// 创建配置
SysConfig config = new SysConfig();
config.setProvider("websocket_stream");
config.setApiUrl("ws://localhost:60009");  // Python 服务器地址
config.setApiKey("your-api-key");

// 创建服务实例
String voiceName = "关羽";
String outputPath = "audio/";
WebSocketStreamTtsService ttsService = new WebSocketStreamTtsService(config, voiceName, outputPath);
```

### 2. 设置提示音频

```java
// 设置提示文本和音频文件
String promptText = "好汉，你又是何人哪？关羽，字云长。";
String promptWavPath = "prompt.wav";  // 可选，为 null 则不使用音频文件

ttsService.setPrompt(promptText, promptWavPath);
```

### 3. 流式 TTS 处理

```java
String text = "你好，这是一个 WebSocket 流式 TTS 测试。";

// 创建流式回调
StreamTtsCallback callback = new StreamTtsCallback() {
    @Override
    public void onStart() {
        System.out.println("TTS 处理开始");
    }
    
    @Override
    public void onAudioChunk(byte[] audioChunk) throws Exception {
        // 处理音频块：播放、保存、转发等
        System.out.println("接收音频块: " + audioChunk.length + " bytes");
    }
    
    @Override
    public void onComplete() {
        System.out.println("TTS 处理完成");
    }
    
    @Override
    public void onError(Throwable error) {
        System.err.println("TTS 处理错误: " + error.getMessage());
    }
};

// 执行流式 TTS
ttsService.streamTextToSpeech(text, callback);
```

### 4. 非流式 TTS 处理

```java
String text = "这是一个非流式 TTS 测试。";

// 直接生成音频文件
String audioFilePath = ttsService.textToSpeech(text);
System.out.println("音频文件已保存: " + audioFilePath);
```

## 工厂模式使用

### 1. 通过 TtsServiceFactory 创建

```java
@Autowired
private TtsServiceFactory ttsServiceFactory;

// 创建配置
SysConfig config = new SysConfig();
config.setProvider("websocket_stream");
config.setApiUrl("ws://localhost:60009");
config.setApiKey("your-api-key");

// 通过工厂获取服务
TtsService ttsService = ttsServiceFactory.getTtsService(config, "关羽");

// 如果是 WebSocketStreamTtsService，可以设置提示音频
if (ttsService instanceof WebSocketStreamTtsService) {
    WebSocketStreamTtsService streamService = (WebSocketStreamTtsService) ttsService;
    streamService.setPrompt("提示文本", "prompt.wav");
}
```

## 配置参数

### SysConfig 配置项

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| provider | String | 是 | 设置为 "websocket_stream" |
| apiUrl | String | 是 | WebSocket 服务器地址，如 "ws://localhost:60009" |
| apiKey | String | 否 | API 密钥（如果服务器需要认证） |

### 服务参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| voiceName | String | 是 | 语音名称，如 "关羽" |
| outputPath | String | 是 | 音频文件输出目录 |

### 提示音频参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| promptText | String | 是 | 提示文本内容 |
| promptWavPath | String | 否 | 提示音频文件路径，为 null 则不使用音频文件 |

## 错误处理

### 常见错误类型

1. **连接错误**
   ```
   WebSocket 连接失败: Connection refused
   ```
   - 检查服务器是否启动
   - 检查 URL 和端口是否正确

2. **协议错误**
   ```
   服务器错误: Invalid configuration format
   ```
   - 检查配置 JSON 格式
   - 检查提示音频文件是否有效

3. **超时错误**
   ```
   TTS 处理超时
   ```
   - 检查网络连接
   - 增加超时时间配置

### 错误处理最佳实践

```java
try {
    ttsService.streamTextToSpeech(text, new StreamTtsCallback() {
        @Override
        public void onError(Throwable error) {
            if (error.getMessage().contains("Connection refused")) {
                // 服务器连接失败，尝试重连
                logger.warn("服务器连接失败，尝试重连...");
            } else if (error.getMessage().contains("timeout")) {
                // 超时处理
                logger.warn("请求超时，请检查网络连接");
            } else {
                // 其他错误
                logger.error("TTS 处理失败", error);
            }
        }
        
        // ... 其他回调方法
    });
} catch (Exception e) {
    logger.error("TTS 服务调用失败", e);
    // 降级处理：使用其他 TTS 服务
    TtsService fallbackService = ttsServiceFactory.getDefaultTtsService();
    return fallbackService.textToSpeech(text);
}
```

## 性能优化

### 1. 连接复用
- 服务实例会被 TtsServiceFactory 缓存
- 相同配置的请求会复用同一个服务实例

### 2. 音频处理优化
```java
// 使用缓冲区减少内存分配
private final ByteArrayOutputStream audioBuffer = new ByteArrayOutputStream(8192);

@Override
public void onAudioChunk(byte[] audioChunk) throws Exception {
    audioBuffer.write(audioChunk);
    
    // 当缓冲区达到一定大小时处理
    if (audioBuffer.size() >= 4096) {
        processAudioBuffer(audioBuffer.toByteArray());
        audioBuffer.reset();
    }
}
```

### 3. 并发处理
```java
// 使用线程池处理多个 TTS 请求
ExecutorService executor = Executors.newFixedThreadPool(4);

for (String text : textList) {
    executor.submit(() -> {
        try {
            ttsService.streamTextToSpeech(text, callback);
        } catch (Exception e) {
            logger.error("TTS 处理失败", e);
        }
    });
}
```

## 与 Python 服务器集成

### 1. 启动 Python 服务器
```bash
# 启动 TTS 流式服务器
python tts_stream_server.py --port 60009 --model_dir /path/to/model

# 检查服务器状态
curl http://localhost:60009/health
```

### 2. Java 客户端配置
```java
SysConfig config = new SysConfig();
config.setProvider("websocket_stream");
config.setApiUrl("ws://localhost:60009");  // 匹配 Python 服务器端口
```

### 3. 协议兼容性验证
```java
// 测试与 Python 服务器的兼容性
@Test
public void testPythonServerCompatibility() throws Exception {
    // 使用与 test_tts_client.py 相同的配置
    String promptText = "好汉，你又是何人哪？关羽，字云长。";
    String testText = "这是一个协议兼容性测试。";
    
    ttsService.setPrompt(promptText, null);
    String audioFile = ttsService.textToSpeech(testText);
    
    // 验证音频文件格式（应为 22050Hz PCM WAV）
    assertAudioFormat(audioFile, 22050, 1, 16);
}
```

## 故障排除

### 1. 连接问题
- 确认 Python 服务器已启动并监听正确端口
- 检查防火墙设置
- 验证 WebSocket URL 格式

### 2. 音频问题
- 检查提示音频文件格式（应为 WAV 格式）
- 验证音频文件路径是否正确
- 确认音频文件大小不超过限制

### 3. 性能问题
- 监控内存使用情况
- 检查网络延迟
- 优化音频处理逻辑

## 示例代码

完整的使用示例请参考：
- `src/test/cn/naii/iot/dialogue/tts/WebSocketStreamTtsTest.java`
- `test_tts_client.py`（Python 参考实现）

## 更新日志

### v1.0.0 (2025-01-27)
- 初始版本发布
- 完整实现 WebSocket 流式 TTS 协议
- 支持与 Python `tts_stream_server.py` 完全兼容
- 集成到 TtsServiceFactory 工厂模式
