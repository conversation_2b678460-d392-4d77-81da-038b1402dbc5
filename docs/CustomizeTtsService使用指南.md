# CustomizeTtsService 使用指南

## 概述

`CustomizeTtsService` 是一个基于 WebSocket 的自定义 TTS（文本转语音）服务实现，演示了如何使用 WebSocket 发送文本流并接收音频流。该实现展示了完整的流式 TTS 处理流程，包括连接管理、错误处理和音频数据流处理。

## 功能特性

- ✅ **WebSocket 流式通信**：支持实时双向数据传输
- ✅ **文本流式发送**：将文本请求通过 WebSocket 发送到 TTS 服务器
- ✅ **音频流式接收**：实时接收和处理音频数据流
- ✅ **连接管理**：自动处理连接建立、维护和关闭
- ✅ **错误处理**：完善的异常处理和错误恢复机制
- ✅ **状态监控**：实时监控 TTS 处理状态
- ✅ **格式支持**：支持 PCM 音频格式（16kHz, 单声道, 16位）

## 架构设计

```
┌─────────────────┐    WebSocket     ┌─────────────────┐
│                 │ ──────────────► │                 │
│ CustomizeTtsService │                 │  TTS Server     │
│                 │ ◄────────────── │                 │
└─────────────────┘                 └─────────────────┘
        │                                   │
        │ 文本请求 (JSON)                    │ 状态消息 (JSON)
        │ {"text":"...", "voice":"..."}      │ {"status":"started"}
        │                                   │
        │                                   │ 音频数据 (Binary)
        │                                   │ PCM 音频流
        │                                   │
        ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│ StreamTtsCallback │                 │ Audio Processing │
│ - onStart()     │                 │ - Real-time     │
│ - onAudioChunk()│                 │ - Streaming     │
│ - onComplete()  │                 │ - Low Latency   │
│ - onError()     │                 │                 │
└─────────────────┘                 └─────────────────┘
```

## 使用方法

### 1. 配置 TTS 服务

```java
// 创建配置对象
SysConfig config = new SysConfig();
config.setProvider("customize");
config.setApiUrl("ws://localhost:8080"); // WebSocket 服务地址
config.setApiKey("your-api-key");        // API 密钥

// 设置语音参数
String voiceName = "zh-CN-XiaoyiNeural"; // 语音名称
String outputPath = "audio/";            // 输出路径

// 创建服务实例
CustomizeTtsService ttsService = new CustomizeTtsService(config, voiceName, outputPath);
```

### 2. 流式 TTS 使用

```java
String text = "你好，这是一个WebSocket流式TTS测试。";

// 创建流式回调
StreamTtsCallback callback = new StreamTtsCallback() {
    @Override
    public void onStart() {
        System.out.println("🎬 TTS 处理开始");
    }
    
    @Override
    public void onAudioChunk(byte[] audioChunk) throws Exception {
        System.out.println("📦 接收音频块: " + audioChunk.length + " bytes");
        
        // 处理音频数据
        // 1. 实时播放
        // audioPlayer.play(audioChunk);
        
        // 2. 发送到客户端
        // webSocketSession.sendBinary(audioChunk);
        
        // 3. 保存到缓冲区
        // audioBuffer.add(audioChunk);
    }
    
    @Override
    public void onComplete() {
        System.out.println("✅ TTS 处理完成");
    }
    
    @Override
    public void onError(Throwable error) {
        System.err.println("❌ TTS 处理错误: " + error.getMessage());
    }
};

// 执行流式 TTS
ttsService.streamTextToSpeech(text, callback);
```

### 3. 非流式 TTS 使用

```java
String text = "这是一个非流式TTS测试。";

// 执行非流式 TTS（内部使用流式实现）
String audioFilePath = ttsService.textToSpeech(text);
System.out.println("音频文件已保存: " + audioFilePath);
```

## WebSocket 协议规范

### 连接建立

```
WebSocket URL: ws://your-server:port/tts/stream
Headers:
  - Authorization: Bearer your-api-key
  - User-Agent: IoT-Manager-TTS-Client/1.0
```

### 消息格式

#### 1. TTS 请求（客户端 → 服务器）

```json
{
  "text": "要转换的文本内容",
  "voice": "zh-CN-XiaoyiNeural",
  "format": "pcm",
  "sample_rate": 16000,
  "channels": 1
}
```

#### 2. 状态响应（服务器 → 客户端）

```json
{
  "status": "started|processing|completed|error",
  "message": "状态描述信息",
  "audio_length": 1024,
  "chunk_index": 1
}
```

#### 3. 音频数据（服务器 → 客户端）

- **格式**: 二进制数据
- **编码**: PCM（16位，单声道，16kHz）
- **传输**: WebSocket Binary Message

## 测试和演示

### 1. 启动模拟服务器

```java
// 运行模拟 WebSocket TTS 服务器
MockWebSocketTtsServer server = new MockWebSocketTtsServer(8080);
server.start();
```

### 2. 运行测试客户端

```java
// 运行测试用例
CustomizeTtsClientTest test = new CustomizeTtsClientTest();
test.testStreamTextToSpeech(); // 测试流式 TTS
test.testTextToSpeech();       // 测试非流式 TTS
```

### 3. 集成到 IoT 系统

在 `TtsServiceFactory` 中已经添加了对自定义 TTS 服务的支持：

```java
// 在配置中设置 provider 为 "customize"
SysConfig config = new SysConfig();
config.setProvider("customize");
config.setApiUrl("ws://your-tts-server:8080");
config.setApiKey("your-api-key");

// 通过工厂获取服务实例
TtsService ttsService = ttsServiceFactory.getTtsService(config, voiceName);
```

## 性能优化建议

### 1. 连接池管理

```java
// 使用连接池复用 WebSocket 连接
private final OkHttpClient httpClient = new OkHttpClient.Builder()
    .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
    .build();
```

### 2. 音频缓冲优化

```java
// 使用环形缓冲区处理音频流
private final RingBuffer<byte[]> audioBuffer = new RingBuffer<>(100);

@Override
public void onAudioChunk(byte[] audioChunk) {
    audioBuffer.offer(audioChunk);
    // 异步处理音频数据
    processAudioAsync();
}
```

### 3. 错误重试机制

```java
// 添加指数退避重试
private void connectWithRetry(int maxRetries) {
    for (int i = 0; i < maxRetries; i++) {
        try {
            connect();
            break;
        } catch (Exception e) {
            long delay = Math.min(1000 * (1L << i), 30000); // 最大30秒
            Thread.sleep(delay);
        }
    }
}
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接和服务器状态
   - 调整 `WS_CONNECT_TIMEOUT_MS` 参数

2. **音频数据丢失**
   - 检查网络带宽和延迟
   - 增加客户端缓冲区大小

3. **内存泄漏**
   - 确保正确关闭 WebSocket 连接
   - 及时释放音频数据缓冲区

### 调试技巧

```java
// 启用详细日志
logger.setLevel(Level.DEBUG);

// 监控连接状态
webSocket.addListener(new ConnectionMonitor());

// 统计音频数据
private final AtomicLong totalBytes = new AtomicLong();
private final AtomicInteger chunkCount = new AtomicInteger();
```

## 扩展开发

### 1. 支持更多音频格式

```java
// 添加音频格式转换
private byte[] convertAudioFormat(byte[] audioData, String fromFormat, String toFormat) {
    // 实现格式转换逻辑
    return AudioUtils.convert(audioData, fromFormat, toFormat);
}
```

### 2. 添加音频后处理

```java
// 音频增强处理
private byte[] enhanceAudio(byte[] audioData) {
    // 降噪、音量调节、音效处理等
    return AudioProcessor.enhance(audioData);
}
```

### 3. 支持多语言

```java
// 多语言支持
private void setLanguage(String languageCode) {
    this.language = languageCode;
    // 调整语音参数
}
```

## 总结

`CustomizeTtsService` 提供了一个完整的 WebSocket 流式 TTS 解决方案，展示了如何在 IoT 系统中集成实时语音合成功能。通过合理的架构设计和错误处理，该服务能够提供稳定、高效的语音合成服务。

开发者可以基于此实现进行扩展，添加更多功能如音频格式转换、音质优化、多语言支持等，以满足不同场景的需求。
