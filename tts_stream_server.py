# Copyright (c) 2024 Alibaba Inc (authors: <PERSON><PERSON>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import os
import sys
import argparse
import logging
import json
import asyncio
import base64
from io import BytesIO
from typing import Dict, Any
import time
from queue import Queue  # 用于线程间通信
logging.getLogger('matplotlib').setLevel(logging.WARNING)
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from concurrent.futures import ThreadPoolExecutor  # 引入线程池
import uvicorn
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/../../..'.format(ROOT_DIR))
sys.path.append('{}/../../../third_party/Matcha-TTS'.format(ROOT_DIR))
from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav

import sys
sys.path.append('third_party/Matcha-TTS')
from vllm import ModelRegistry
from cosyvoice.vllm.cosyvoice2 import CosyVoice2ForCausalLM
ModelRegistry.register_model("CosyVoice2ForCausalLM", CosyVoice2ForCausalLM)

from cosyvoice.cli.cosyvoice import CosyVoice2
from cosyvoice.utils.file_utils import load_wav
from cosyvoice.utils.common import set_all_random_seed
from tqdm import tqdm

app = FastAPI()
# 1. 配置CORS
app.add_middleware(
    CORSMiddleware,
    # 仅允许本机来源（包含localhost、127.0.0.1、IPv6本地回环）
    # 若前端在本机特定端口（如3000），需补充具体端口，例如 "http://localhost:3000"
    allow_origins=[
        # "http://localhost",
        # "http://127.0.0.1",
        # "http://[::1]",  # IPv6本地回环地址
        # "http://***********",
        # "http://************"
    ],
    allow_credentials=True,  # 允许携带凭证（如Cookie）
    allow_methods=["GET"],  # WebSocket基于GET升级，仅允许GET方法
    allow_headers=["*"]  # 允许所有请求头（或根据需要限制，如 ["Authorization", "Content-Type"]）
)


# 2. 创建线程池（根据CPU核心数调整，避免过多线程导致资源竞争）
# 建议设置为 CPU核心数 * 2（兼顾GPU推理时的并发）
executor = ThreadPoolExecutor(max_workers=8)

# 3. 并发控制：使用信号量限制同时处理的推理请求数
# 避免GPU显存溢出，根据实际GPU显存调整
MAX_CONCURRENT_REQUESTS = 4
semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

# 4. 请求统计
active_connections = 0
total_requests = 0


def generate_data(model_output):
    """
    生成音频数据的辅助函数
    将模型输出转换为PCM格式的音频字节流
    """
    for i in model_output:
        tts_audio = (i['tts_speech'].numpy() * (2 ** 15)).astype(np.int16).tobytes()
        yield tts_audio


def process_tts_inference_streaming(tts_text: str, prompt_text: str, prompt_speech_16k, cosyvoice_model, audio_queue: Queue):
    """
    在线程池中执行的TTS推理函数（流式版本）
    使用队列实时传递音频块，降低首包延迟
    
    参数:
        audio_queue: 线程安全队列，用于传递音频数据
                     放入 (True, audio_chunk) 表示音频数据
                     放入 (False, None) 表示推理完成
                     放入 (False, Exception) 表示发生错误
    """
    try:
        # 执行推理，stream=True返回生成器
        model_output = cosyvoice_model.inference_zero_shot(
            tts_text=tts_text,
            prompt_text=prompt_text,
            prompt_speech_16k=prompt_speech_16k,
            stream=True
        )
        
        # 逐块生成并立即放入队列，实现真正的流式传输
        chunk_count = 0
        for audio_data in generate_data(model_output):
            audio_queue.put((True, audio_data))  # (True, data) 表示音频块
            chunk_count += 1
        
        # 推理完成信号
        audio_queue.put((False, None))  # (False, None) 表示正常结束
        logger.debug(f"TTS inference completed, generated {chunk_count} chunks")
        
    except Exception as e:
        logger.error(f"TTS inference error: {e}")
        audio_queue.put((False, e))  # (False, Exception) 表示错误


@app.websocket("/ws/tts_stream")
async def websocket_tts_stream(websocket: WebSocket):
    """
    WebSocket端点：接收文本流，返回音频流
    支持并发控制和流式处理
    """
    global active_connections, total_requests
    connection_id = f"conn_{total_requests}"
    total_requests += 1
    active_connections += 1
    
    logger.info(f"[{connection_id}] New WebSocket connection. Active: {active_connections}")
    
    await websocket.accept()
    prompt_speech_16k = None
    prompt_text = ""
    
    try:
        # 第一步：接收配置信息（包含prompt_text和prompt_wav的base64数据）
        config_data = await asyncio.wait_for(
            websocket.receive_text(),
            timeout=30.0  # 30秒超时
        )
        config = json.loads(config_data)
        
        prompt_text = config.get("prompt_text", "")
        prompt_wav_base64 = config.get("prompt_wav_base64", "")
        
        logger.info(f"[{connection_id}] Received config: prompt_text_len={len(prompt_text)}, wav_len={len(prompt_wav_base64)}")
        
        # 解码base64的音频数据（在线程池中执行，避免阻塞事件循环）
        loop = asyncio.get_event_loop()
        prompt_wav_bytes = await loop.run_in_executor(
            executor,
            lambda: base64.b64decode(prompt_wav_base64)
        )
        prompt_wav_file = BytesIO(prompt_wav_bytes)
        prompt_speech_16k = await loop.run_in_executor(
            executor,
            lambda: load_wav(prompt_wav_file, 16000)
        )
        
        logger.info(f"[{connection_id}] Prompt audio loaded successfully")
        
        # 第二步：持续接收文本流并生成音频
        text_count = 0
        while True:
            # 接收客户端发送的文本（异步操作，不阻塞）
            text_data = await asyncio.wait_for(
                websocket.receive_text(),
                timeout=300.0  # 5分钟超时（等待客户端发送）
            )
            
            # 检查结束信号
            if text_data == "__END_STREAM__":
                await websocket.send_text("__CONNECTION_CLOSING__")
                logger.info(f"[{connection_id}] Stream ended by client. Processed {text_count} texts")
                await websocket.close()
                return
            
            text_count += 1
            text_preview = text_data[:50] + "..." if len(text_data) > 50 else text_data
            logger.info(f"[{connection_id}] Processing text #{text_count}: {text_preview}")
            
            # 第三步：使用信号量控制并发，避免GPU显存溢出
            async with semaphore:
                start_time = time.time()
                first_chunk_time = None
                chunk_count = 0
                
                # 创建线程安全队列用于接收音频块
                audio_queue = Queue()
                
                # 将同步模型推理提交到线程池（不等待完成，立即返回Future）
                loop = asyncio.get_event_loop()
                inference_future = loop.run_in_executor(
                    executor,
                    process_tts_inference_streaming,
                    text_data,
                    prompt_text,
                    prompt_speech_16k,
                    cosyvoice,
                    audio_queue
                )
                
                # 第四步：边生成边发送，实现真正的流式传输
                try:
                    while True:
                        # 从队列中获取数据（使用超时避免永久阻塞）
                        # 在另一个线程中执行 queue.get() 以避免阻塞事件循环
                        is_data, content = await asyncio.wait_for(
                            loop.run_in_executor(executor, audio_queue.get),
                            timeout=60.0
                        )
                        
                        if not is_data:
                            # 推理结束或出错
                            if content is None:
                                # 正常结束
                                break
                            else:
                                # 发生错误
                                raise content
                        
                        # 发送音频块
                        chunk_count += 1
                        if first_chunk_time is None:
                            first_chunk_time = time.time() - start_time
                            logger.info(f"[{connection_id}] First chunk latency: {first_chunk_time:.3f}s")
                        
                        await websocket.send_bytes(content)
                    
                    # 等待推理任务完全结束（确保没有异常）
                    await inference_future
                    
                except Exception as e:
                    # 取消推理任务
                    inference_future.cancel()
                    raise e
                
                total_time = time.time() - start_time
                logger.info(f"[{connection_id}] Text #{text_count} completed: {chunk_count} chunks, "
                           f"first_chunk={first_chunk_time:.3f}s, total={total_time:.2f}s")
                
                # 发送单轮完成标志
                await websocket.send_text("__STREAM_COMPLETE__")
            
    except asyncio.TimeoutError:
        error_msg = "Request timeout"
        logger.warning(f"[{connection_id}] {error_msg}")
        try:
            await websocket.send_text(f"__ERROR__:{error_msg}")
            await websocket.close(code=1008, reason=error_msg)
        except:
            pass
            
    except WebSocketDisconnect:
        logger.info(f"[{connection_id}] Client disconnected")
        
    except json.JSONDecodeError as e:
        error_msg = f"Invalid JSON config: {str(e)}"
        logger.error(f"[{connection_id}] {error_msg}")
        try:
            await websocket.send_text(f"__ERROR__:{error_msg}")
            await websocket.close(code=1003, reason=error_msg)
        except:
            pass
            
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"[{connection_id}] {error_msg}", exc_info=True)
        try:
            await websocket.send_text(f"__ERROR__:{error_msg}")
            await websocket.close(code=1011, reason=str(e))
        except:
            pass
            
    finally:
        active_connections -= 1
        logger.info(f"[{connection_id}] Connection closed. Active: {active_connections}")


@app.get("/health")
async def health_check():
    """
    健康检查端点
    返回服务状态信息
    """
    return {
        "status": "healthy",
        "active_connections": active_connections,
        "total_requests": total_requests,
        "max_concurrent_requests": MAX_CONCURRENT_REQUESTS,
        "available_slots": MAX_CONCURRENT_REQUESTS - semaphore._value if hasattr(semaphore, '_value') else 0
    }


@app.get("/")
async def root():
    """
    根路径，返回API使用说明
    """
    return {
        "service": "CosyVoice TTS Streaming API",
        "version": "2.0",
        "endpoints": {
            "websocket": "/ws/tts_stream",
            "health": "/health"
        },
        "usage": {
            "step1": "Connect to WebSocket endpoint: ws://host:port/ws/tts_stream",
            "step2": "Send config JSON with prompt_text and prompt_wav_base64",
            "step3": "Send text messages for TTS conversion",
            "step4": "Receive audio chunks as binary data",
            "step5": "Send '__END_STREAM__' to close connection",
            "signals": {
                "__STREAM_COMPLETE__": "Single text processing completed",
                "__CONNECTION_CLOSING__": "Connection closing confirmation",
                "__ERROR__:<msg>": "Error occurred"
            }
        }
    }


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='CosyVoice TTS Streaming Server')
    parser.add_argument('--port',
                        type=int,
                        default=60009,
                        help='Server port (default: 60009)')
    parser.add_argument('--model_dir',
                        type=str,
                        default='/app/maxingran/workspace/tts/CosyVoice/pretrained_models/CosyVoice2-0.5B',
                        help='Local path or modelscope repo id')
    parser.add_argument('--max_concurrent',
                        type=int,
                        default=4,
                        help='Maximum concurrent TTS inference requests (default: 4)')
    parser.add_argument('--workers',
                        type=int,
                        default=8,
                        help='Thread pool size (default: 8)')
    args = parser.parse_args()
    
    # 更新并发限制配置
    if args.max_concurrent != 4:  # 默认值
        MAX_CONCURRENT_REQUESTS = args.max_concurrent
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
        logger.info(f"Updated MAX_CONCURRENT_REQUESTS to {MAX_CONCURRENT_REQUESTS}")
    
    # 更新线程池配置
    if args.workers != 8:  # 默认值
        executor.shutdown(wait=False)
        executor = ThreadPoolExecutor(max_workers=args.workers)
        logger.info(f"Updated thread pool size to {args.workers}")
    
    # 加载模型
    logger.info(f"Loading model from {args.model_dir}...")
    try:
        cosyvoice = CosyVoice(args.model_dir)
        logger.info("Model loaded successfully with CosyVoice")
    except Exception as e1:
        logger.warning(f"Failed to load CosyVoice: {e1}")
        try:
            cosyvoice = CosyVoice2(args.model_dir, load_jit=True, fp16=True, load_trt=True, load_vllm=True)
            logger.info("!!!!!!!!!!!!!!! Model loaded successfully with CosyVoice2, using vLLM and JIT and FP16 and TRT  !!!!!!!!!!!!!!!!!!")
        except Exception as e2:
            logger.error(f"Failed to load CosyVoice2: {e2}")
            raise TypeError('No valid model_type! Both CosyVoice and CosyVoice2 failed.')
    
    # 启动服务器
    logger.info(f"Starting server on 127.0.0.1:{args.port}")
    logger.info(f"Configuration: max_concurrent={args.max_concurrent}, workers={args.workers}")
    try:
        uvicorn.run(app, host="127.0.0.1", port=args.port)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Error in uvicorn server: {e}", exc_info=True)
    finally:
        # 关闭线程池（释放资源）
        logger.info("Shutting down thread pool...")
        executor.shutdown(wait=True)
        logger.info("Server stopped")
