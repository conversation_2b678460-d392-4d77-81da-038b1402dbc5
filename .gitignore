target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
.kotlin

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
*.log
logs/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

/audio

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store

# 屏蔽audio目录下的所有文件
audio/*
# 但需要提交audio/warmup目录下的文件
!audio/warmup/