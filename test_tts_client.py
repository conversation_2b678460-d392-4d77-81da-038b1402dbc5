#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket TTS 客户端测试脚本
演示如何使用 WebSocket 发送文本流并接收音频流
"""

import asyncio
import websockets
import json
import base64
import wave
import argparse
from pathlib import Path


async def test_tts_stream(
    server_url: str,
    prompt_text: str,
    prompt_wav_path: str,
    texts: list,
    output_dir: str = "./output"
):
    """
    测试 TTS WebSocket 流式接口
    
    Args:
        server_url: WebSocket 服务器 URL，如 ws://localhost:60009/ws/tts_stream
        prompt_text: 提示文本
        prompt_wav_path: 提示音频文件路径
        texts: 要转换的文本列表
        output_dir: 输出目录
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"连接到服务器: {server_url}")
    
    try:
        async with websockets.connect(server_url) as websocket:
            print("✓ WebSocket 连接成功")
            
            # 第一步：读取并编码提示音频
            with open(prompt_wav_path, 'rb') as f:
                prompt_wav_bytes = f.read()
            prompt_wav_base64 = base64.b64encode(prompt_wav_bytes).decode('utf-8')
            
            # 第二步：发送配置信息
            config = {
                "prompt_text": prompt_text,
                "prompt_wav_base64": prompt_wav_base64
            }
            await websocket.send(json.dumps(config))
            print(f"✓ 已发送配置信息 (提示文本长度: {len(prompt_text)}, 音频大小: {len(prompt_wav_bytes)} 字节)")
            
            # 第三步：逐个发送文本并接收音频
            for idx, text in enumerate(texts, 1):
                print(f"\n[文本 {idx}/{len(texts)}] 发送: {text[:50]}{'...' if len(text) > 50 else ''}")
                await websocket.send(text)
                
                # 接收音频流
                audio_chunks = []
                chunk_count = 0
                
                while True:
                    message = await websocket.recv()
                    
                    # 检查是否为文本消息（控制信号）
                    if isinstance(message, str):
                        if message == "__STREAM_COMPLETE__":
                            print(f"✓ 音频流接收完成 (共 {chunk_count} 个音频块)")
                            break
                        elif message.startswith("__ERROR__"):
                            error_msg = message.split(":", 1)[1] if ":" in message else "Unknown error"
                            print(f"✗ 服务器错误: {error_msg}")
                            return
                        else:
                            print(f"收到控制消息: {message}")
                    
                    # 二进制消息为音频数据
                    elif isinstance(message, bytes):
                        audio_chunks.append(message)
                        chunk_count += 1
                
                # 保存音频文件
                if audio_chunks:
                    output_file = output_path / f"tts_output_{idx}.wav"
                    save_audio_wav(audio_chunks, output_file, sample_rate=22050, channels=1)
                    print(f"✓ 音频已保存: {output_file} ({len(audio_chunks)} 块, {sum(len(c) for c in audio_chunks)} 字节)")
            
            # 第四步：发送结束信号
            print("\n发送结束信号...")
            await websocket.send("__END_STREAM__")
            
            # 等待确认
            response = await websocket.recv()
            if response == "__CONNECTION_CLOSING__":
                print("✓ 服务器确认连接关闭")
            
            print(f"\n✓ 所有任务完成！输出目录: {output_path.absolute()}")
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"✗ WebSocket 连接关闭: {e}")
    except Exception as e:
        print(f"✗ 错误: {e}")
        import traceback
        traceback.print_exc()


def save_audio_wav(audio_chunks: list, output_path: Path, sample_rate: int = 22050, channels: int = 1):
    """
    将音频块保存为 WAV 文件
    
    Args:
        audio_chunks: 音频数据块列表（PCM int16 格式）
        output_path: 输出文件路径
        sample_rate: 采样率
        channels: 声道数
    """
    # 合并所有音频块
    audio_data = b''.join(audio_chunks)
    
    # 写入 WAV 文件
    with wave.open(str(output_path), 'wb') as wf:
        wf.setnchannels(channels)
        wf.setsampwidth(2)  # 16-bit = 2 bytes
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data)


async def test_health_check(base_url: str):
    """
    测试健康检查端点
    
    Args:
        base_url: HTTP 服务器基础 URL，如 http://localhost:60009
    """
    import aiohttp
    
    print(f"检查服务器健康状态: {base_url}/health")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✓ 服务器健康状态:")
                    print(f"  - 状态: {data.get('status')}")
                    print(f"  - 活跃连接数: {data.get('active_connections')}")
                    print(f"  - 总请求数: {data.get('total_requests')}")
                    print(f"  - 最大并发数: {data.get('max_concurrent_requests')}")
                    print(f"  - 可用槽位: {data.get('available_slots')}")
                else:
                    print(f"✗ 健康检查失败: HTTP {response.status}")
    except Exception as e:
        print(f"✗ 健康检查错误: {e}")


def main():
    parser = argparse.ArgumentParser(description='WebSocket TTS 客户端测试工具')
    parser.add_argument('--host',
                        type=str,
                        default='localhost',
                        help='服务器地址 (default: localhost)')
    parser.add_argument('--port',
                        type=int,
                        default=60009,
                        help='服务器端口 (default: 60009)')
    parser.add_argument('--prompt_text',
                        type=str,
                        default='好汉，你又是何人哪？关羽，字云长。',
                        help='提示文本')
    parser.add_argument('--prompt_wav',
                        type=str,
                        required=True,
                        help='提示音频文件路径 (.wav)')
    parser.add_argument('--texts',
                        type=str,
                        nargs='+',
                        default=['你好，我是通过WebSocket流式接口生成的语音。'],
                        help='要转换的文本列表 (可以提供多个)')
    parser.add_argument('--output_dir',
                        type=str,
                        default='./output',
                        help='输出目录 (default: ./output)')
    parser.add_argument('--health_check',
                        action='store_true',
                        help='仅执行健康检查')
    
    args = parser.parse_args()
    
    # 构建 URL
    ws_url = f"ws://{args.host}:{args.port}/ws/tts_stream"
    http_url = f"http://{args.host}:{args.port}"
    
    # 运行测试
    if args.health_check:
        asyncio.run(test_health_check(http_url))
    else:
        asyncio.run(test_tts_stream(
            server_url=ws_url,
            prompt_text=args.prompt_text,
            prompt_wav_path=args.prompt_wav,
            texts=args.texts,
            output_dir=args.output_dir
        ))


if __name__ == '__main__':
    main()

